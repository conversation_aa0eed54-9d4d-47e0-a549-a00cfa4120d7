package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.analytics.config.PsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PSClientConnector {
    @Autowired
    private PsClient psClient;

    public ResponseEntity<String> getPsClientOrderDetails(Integer incrementId, String channel) {
        ResponseEntity<String> response = null;
        try {
            String url = psClient.getBaseUrl() + getAddressUrlForPsClient(channel) + incrementId;
            log.info("getPsClientOrderDetails: {}", url);
            response = psClient.exchange(url, HttpMethod.GET, null, String.class);
            log.info("getPsClientOrderDetails for order {} responseEntity:{}", incrementId ,response);
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
        return response;
    }

    public String getAddressUrlForPsClient(String channel) {
        String urlPath = "/franchise/order/";
        if ("FRANCHISEBULK".equalsIgnoreCase(channel)) {
            urlPath = "/franchise/bulkOrderAddress/";
        }
        return urlPath;
    }
}
