package com.lenskart.nexs.analytics.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.nexs.analytics.dao.FMSResponse;
import com.lenskart.nexs.analytics.util.AnalyticsUtil;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constant.AuthConstant;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.service.RedisHandler;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.lenskart.nexs.util.AuthUtil.getAuthSecretMessage;

@Service
public class FMSConnector {

    @Value("${nexs.analytics.fms.host}")
    private String fmsHost;

    @Value("${nexs.auth.public.key}")
    private String publicKeyValue;

    @Value("${auth.secret.message:LxFEgGL1WbHFuyywSFqrb1TvBcreGKjaBibeOwXLpXuGmO56up5fNNZmXQFu}")
    private String secretMessage;

    @Value("${nexs.analytics.fms.facility.details.url}")
    private String fmsFetchDetailsUrl;

    @Value("${nexs.analytics.fms.rest.timeout}")
    private int timeout;

    @Value("${nexs.analytics.redis.ttl}")
    private String redisTtl;

    @CustomLogger
    private static Logger logger;

    private static final String FMS_RESPONSE_CACHE_KEY = "NEXS_ANALYTICS_FMS_";

    public FMSResponse fetchFacilityDetails(String facilityCode) throws Exception {
        Object facilityDetails = RedisHandler.redisOps(RedisOps.GET, FMS_RESPONSE_CACHE_KEY + facilityCode);
        if (Objects.nonNull(facilityDetails))
            return ObjectHelper.getObjectMapper().readValue(String.valueOf(facilityDetails), FMSResponse.class);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add(AuthConstant.AUTH_SECRET_MESSAGE, String.valueOf(getAuthSecretMessage(publicKeyValue, secretMessage)));
        HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(fmsHost + fmsFetchDetailsUrl);
        logger.info("[fetchFacilityDetails] Request for FMS for facility details, facility code: {}, fmsHost: {}, fmsFetchDetailsUrl: {}, builder: {} ", facilityCode, fmsHost, fmsFetchDetailsUrl, builder.toUriString());
        RestTemplate template = AnalyticsUtil.restTemplate(timeout);
        ResponseEntity<String> responseEntity = template.exchange(builder.toUriString() + facilityCode, HttpMethod.GET, httpEntity, String.class, (Object) new Object());
        logger.info("[fetchFacilityDetails] responseEntity is {}", responseEntity);
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            BaseResponseModel baseResponseModel = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<BaseResponseModel>() {
            });
            logger.info("[fetchFacilityDetails] baseResponseModel is {}", baseResponseModel);
            FMSResponse fmsResponse = ObjectHelper.getObjectMapper().convertValue(baseResponseModel.getData(), new TypeReference<FMSResponse>() {
            });
            logger.info("Response from FMS for fetch facility details  : " + fmsResponse);
            if (Objects.isNull(fmsResponse)) {
                logger.error("[fetchFacilityDetails] Invalid facility code: {}", facilityCode);
                throw new CustomException("Invalid facility code", 400);
            }
            Object obj = RedisHandler.redisOps(RedisOps.SETVALUETTL, FMS_RESPONSE_CACHE_KEY + facilityCode, ObjectHelper.convertToString(fmsResponse), Long.valueOf(redisTtl), TimeUnit.HOURS);
            return fmsResponse;
        } else {
            logger.error("[fetchFacilityDetails] Fetch facility details from fms failed, response is {} and code is: {}",  responseEntity.getBody(), responseEntity.getStatusCodeValue());
            throw new Exception("Fetch facility details from fms failed, response is " + responseEntity.getStatusCodeValue() + "response is" + responseEntity.getBody());
        }
    }
}
