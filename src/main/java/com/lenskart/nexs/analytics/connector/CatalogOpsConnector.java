package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.analytics.dao.CacheDAO;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.wms.response.qc.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CatalogOpsConnector {

    @Value("${nexs.analytics.catalog.ops.host}")
    private String catalogOpsHost;

    @Value("${nexs.analytics.catalog.ops.product.url}")
    private String fetchProductDetailsUrl;

    @Value("${nexs.analytics.catalog.redis.ttl}")
    private String redisTtl;

    @Autowired
    private CacheDAO cacheDAO;

    private static final String CATALOG_PRODUCT_RESPONSE_CACHE_KEY = "NEXS_ANALYTICS_CATALOG_PRODUCT";

    @Logging
    @Retryable(value = Exception.class, maxAttemptsExpression = "${nexs.analytics.retry.max.attempts}",
            backoff = @Backoff(delayExpression = "${nexs.analytics.retry.max.delay}"))
    public Product getProductDetailsFromId(Integer productId) throws Exception {
        try {
            log.info("Fetching product details for product Id: {}", productId);

            Object productDetails =cacheDAO.getVal( CATALOG_PRODUCT_RESPONSE_CACHE_KEY+productId);

            if(Objects.nonNull(productDetails)) {
                Product product = ObjectHelper.getObjectMapper().readValue(String.valueOf(productDetails), Product.class);
                log.info("Fetched product details from redis for product Id: {} - {}", productId, product);
                return product;
            }

            String url = catalogOpsHost + fetchProductDetailsUrl + productId;
            Product product = RestUtils.getData(url, null, null, Product.class);
            log.info("Fetched product details from catalog ops for product Id: {} - {}", productId, product);

            cacheDAO.putVal(CATALOG_PRODUCT_RESPONSE_CACHE_KEY + productId,
                    product, Long.valueOf(redisTtl), TimeUnit.HOURS);

            return product;

        } catch (Exception e) {
            log.error("Unable to fetch product details for product id :{}, exception :{}", productId, e.getMessage());
            throw new Exception("Unable to fetch product details for product id : "+productId);
        }
    }

}

