package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.analytics.dao.GrnPriceRequest;
import com.lenskart.nexs.analytics.dao.GrnPriceResponse;
import com.lenskart.nexs.analytics.model.AnalyticsGrnInfo;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.common.http.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class PoServiceConnector {

    @Value("${nexs.ims.get.grn.analytics.info.url}")
    private String getGrnAnalyticsInfoUrl;


    @Retryable(value = Exception.class, maxAttemptsExpression = "${nexs.ims.get.grn.analytics.info.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${nexs.ims.get.grn.analytics.info.retry.maxDelay}"))
    public AnalyticsGrnInfo getGrnAnalyticsInfo(String barcode) throws Exception {
        log.info("Fetching Grn Analytics Info for barcode: {} ", barcode);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(getGrnAnalyticsInfoUrl)
                .queryParam("barcode", barcode);
        try {
            AnalyticsGrnInfo analyticsGrnInfo = RestUtils.getData(builder.toUriString(), null, null, AnalyticsGrnInfo.class);

            log.info("Fetched Grn Analytics Info for barcode: {} - {}", barcode, analyticsGrnInfo);
            return analyticsGrnInfo;
        } catch (Exception e) {
            log.error("Error while fetching Grn Analytics Info for barcode: {} - {} ", barcode, e.getMessage());
            throw e;
        }
    }

}
