package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.analytics.model.request.FetchGstItemDetailsRequest;
import com.lenskart.nexs.analytics.model.response.FetchGstItemDetailsResponse;
import com.lenskart.nexs.analytics.util.InvoicingUtil;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Objects;

@Service
@Slf4j
public class FinanceConnector{

    @Value("${nexs.analytics.finance.gst.details.url}")
    private String fetchGstDetailsUrl;

    @Value("${nexs.analytics.finance.host}")
    private String financeHost;

    @Value("${nexs.analytics.finance.rest.timeout}")
    private int timeout;


    public FetchGstItemDetailsResponse fetchGstDetails(FetchGstItemDetailsRequest fetchGstItemDetailsRequest) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(fetchGstItemDetailsRequest, httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(financeHost + fetchGstDetailsUrl);
        RestTemplate template = InvoicingUtil.restTemplate(timeout);
        ResponseEntity<String> responseEntity = template.exchange(builder.toUriString(), HttpMethod.POST, httpEntity, String.class, (Object) new Object());
        log.info("Request for finance and invoice for gst details  : " + fetchGstItemDetailsRequest);
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            FetchGstItemDetailsResponse fetchGstItemDetailsResponse = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(),FetchGstItemDetailsResponse.class);
            log.info("Response from finance and invoice for fetch gst details  : " + fetchGstItemDetailsResponse);
            if (Objects.nonNull(fetchGstItemDetailsResponse) && !fetchGstItemDetailsResponse.getSuccessful()) {
                throw new Exception(fetchGstItemDetailsResponse.getErrorMessage() == null ? String.valueOf(fetchGstItemDetailsResponse.getErrorMessage()) : "Fetch gst details failed");
            }
            return fetchGstItemDetailsResponse;
        } else {
            throw new Exception("Fetch gst details from finance and invoice failed ,response is " + responseEntity.getStatusCodeValue());

        }

    }




}
