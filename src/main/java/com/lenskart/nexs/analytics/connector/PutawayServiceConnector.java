package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.putawayservice.model.PutawayItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
@Slf4j
public class PutawayServiceConnector {

    @Value("${nexs.ims.get.putaway.analytics.info.url}")
    private String getPutawayAnalyticsInfoUrl;

    @Retryable(value = Exception.class, maxAttemptsExpression = "${nexs.ims.get.putaway.analytics.info.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${nexs.ims.get.putaway.analytics.info.retry.maxDelay}"))
    public PutawayItem getAnalyticsPutawayInfo(String barcode) {
        log.info("Fetching Putaway Analytics Info for barcode: {} ", barcode);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(getPutawayAnalyticsInfoUrl)
                .queryParam("barcode", barcode);
        try {
            PutawayItem putawayItem = RestUtils.getData(builder.toUriString(), null, null, PutawayItem.class);
            log.info("Fetched Putaway Analytics Info for barcode: {} - {}", barcode, putawayItem);
            return putawayItem;
        } catch(Exception e) {
            log.error("Error while fetching Putaway Analytics Info for barcode: {} - {} ", barcode, e.getMessage());
            throw e;
        }
    }

}
