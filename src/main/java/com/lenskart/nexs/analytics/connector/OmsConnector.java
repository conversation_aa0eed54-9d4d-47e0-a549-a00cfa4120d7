package com.lenskart.nexs.analytics.connector;

import com.google.gson.Gson;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.wms.response.OmsOrderItemDetailsResponse;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import org.apache.kafka.common.errors.InvalidRequestException;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Component
public class OmsConnector {

    public static final String AUTHORIZATION_HEADER_KEY = "Authorization";
    public static final String X_CLIENT_KEY_HAEADER_KEY = "x-client-key";
    public static final String SERVICE_NAME = "nexs-analytics";
    private static final Integer DEFAULT_CONNECTION_TIMEOUT = 3000;
    private static final Integer DEFAULT_READ_TIMEOUT = 3000;
    private static final String OMS_GET_ITEM_DETAILS ="/orderItem/search?searchTerms=uwItemId.eq:";

    @CustomLogger
    private Logger logger;

    @Value("${oms.base.url}")
    private String omsBaseUrl;

    @Value("${oms.auth.header.token}")
    private String omsAuthHeaderToken;

    private final RestTemplate restTemplate;

    public OmsConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${oms.read.timeout}") Integer readTimeout,
                        @Value("${oms.connection.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    public OmsOrderItemDetailsResponse getOrderItemDetailsByUwItemId(String uwItemId) throws InvalidRequestException {
        ResponseEntity<String> responseEntity;
        String url = omsBaseUrl.concat(OMS_GET_ITEM_DETAILS).concat(uwItemId);
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            appendAuthHeaders(httpHeaders);
            HttpEntity<ShipmentUpdateEvent> httpEntity = new HttpEntity(httpHeaders);
            logger.info("OmsConnector.getOrderItemDetailsByUwItemId: URL - {}, Payload - {}", url, new Gson().toJson(httpEntity));
            responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class, new Object[]{null});
            logger.info("OmsConnector.getOrderItemDetailsByUwItemId: Payload - {}, Response - {}", new Gson().toJson(httpEntity), responseEntity);
            if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.info("Oms update is successful with message {}", responseEntity.getBody());
                BaseResponseModel responseModel = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), BaseResponseModel.class);
                OmsOrderItemDetailsResponse omsOrderItemDetailsResponse = ObjectHelper.getObjectMapper().convertValue(responseModel.getData(), OmsOrderItemDetailsResponse.class);
                return omsOrderItemDetailsResponse;
            }
        } catch (Exception exception) {
            logger.error("get shipment details in oms failed for endpoint {}  with exception message {}  ", url, exception.getMessage(), exception);
            throw new InvalidRequestException("Error in getting response from oms shipment details api" + exception.getMessage());
        }
        throw new InvalidRequestException("Error in getting response from oms shipment details api"+
                responseEntity.getBody() !=null ? responseEntity.getBody() : "response body is null");
    }

    private void appendAuthHeaders(HttpHeaders httpHeaders) {
        httpHeaders.add(X_CLIENT_KEY_HAEADER_KEY, SERVICE_NAME);
        httpHeaders.add(AUTHORIZATION_HEADER_KEY, omsAuthHeaderToken);
    }

}
