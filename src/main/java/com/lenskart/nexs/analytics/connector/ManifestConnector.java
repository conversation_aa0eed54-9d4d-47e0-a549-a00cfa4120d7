package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.analytics.model.response.Manifest;
import com.lenskart.nexs.analytics.util.LCRDUtil;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service
public class ManifestConnector {

    @Value("${nexs.analytics.finance.gst.details.url}")
    private String fetchManifestDetailsUrl;

    @Value("${nexs.analytics.finance.host}")
    private String manifestHost;

    @Value("${nexs.analytics.finance.rest.timeout}")
    private int timeout;


    public Manifest fetchManifestDetails(String manifestId) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(manifestHost + fetchManifestDetailsUrl + manifestId);
        RestTemplate template = LCRDUtil.restTemplate(timeout);
        ResponseEntity<String> responseEntity = template.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, String.class, (Object) new Object());
        log.info("Request for fetching manifest details for manifest ID : " + manifestId);
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            Manifest manifest = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(),Manifest.class);
            log.info("Response from manifest for fetching manifest details  : " + manifest);
            return manifest;
        } else {
            throw new Exception("Fetch manifest details failed ,response is " + responseEntity.getStatusCodeValue());

        }

    }

}
