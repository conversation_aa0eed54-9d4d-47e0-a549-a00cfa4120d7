package com.lenskart.nexs.analytics.connector;

import com.lenskart.nexs.analytics.dao.GrnPriceRequest;
import com.lenskart.nexs.analytics.dao.GrnPriceResponse;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.common.http.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;


@Component
@Slf4j
public class GrnConnector {

    @Value("${nexs.grn.base.url}")
    private String grnBaseUrl;

    @Value("${nexs.grn.barcode.price.url}")
    private String grnUnitPriceUrl;

    @Retryable(value = Exception.class, maxAttemptsExpression = "${nexs.ims.get.grn.analytics.info.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${nexs.ims.get.grn.analytics.info.retry.maxDelay}"))
    public GrnPriceResponse getCostPrice(GrnPriceRequest grnPriceRequest) throws Exception {
        log.info("fetching grn price for request : {} ",grnPriceRequest );
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(grnBaseUrl+grnUnitPriceUrl);
        try {
            List<Object> list = RestUtils.postData(builder.toUriString(), grnPriceRequest, List.class);
            GrnPriceResponse grnPriceResponse = null;
            if(!CollectionUtils.isEmpty(list)) {
                grnPriceResponse = ObjectHelper.getObjectMapper().convertValue(list.get(0), GrnPriceResponse.class);
            }
            log.info("fetched price details for request : {} , as : {}", grnPriceRequest, grnPriceResponse);
            return grnPriceResponse;
        } catch (Exception e) {
            log.error("Error while fetching base cost price for request : {} - {} ", grnPriceRequest, e.getMessage());
            throw e;
        }
    }
}
