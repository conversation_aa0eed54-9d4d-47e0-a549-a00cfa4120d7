package com.lenskart.nexs.analytics.connector;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;

@Component
@Slf4j
public class FdsConnector {
    @Value("${fds.base.url:https://fds.scm.preprod.lenskart.com}")
    private String fdsBaseUrl;

    public static final String BASE_URL_V1 = "/fds/api/v1";

    private static final String FDS_SEARCH_TERMS_URL = "/%s/search?searchTerms=%s";

    private RestTemplate restTemplate;

    public FdsConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${fds.connect.timeout:2000}") Integer connectionTimeOut, @Value("${fds.read.timeout:9000}") Integer readTimeOut) {
        this.restTemplate = restTemplateBuilder.setReadTimeout(Duration.ofMillis(readTimeOut)).setConnectTimeout(Duration.ofMillis(connectionTimeOut)).build();
    }

    public String getDetails(String tableName, String searchTerms) throws Exception {
        String url = fdsBaseUrl + BASE_URL_V1 + String.format(FDS_SEARCH_TERMS_URL, tableName, searchTerms);
        log.info("[FdsConnector][getDetails] url={}", url);
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            if (HttpStatus.OK.equals(responseEntity.getStatusCode()) && responseEntity.getBody() != null) {
                log.info("[FdsConnector][getDetails] Success with response code {} for {}", responseEntity.getStatusCode(), url);
                return responseEntity.getBody();
            } else {
                log.error("[FdsConnector][getDetails] failed with response code {} and message {}", responseEntity.getStatusCode(), responseEntity.getBody());
                throw new Exception("getDetails call to FDS failed with response code " + responseEntity.getStatusCode() + " and message " + responseEntity.getBody());
            }
        } catch (Exception ex) {
            log.error("[FdsConnector][getDetails] Exception for " + url + " due to " + ex.getMessage(), ex);
            throw new Exception("getDetails call to FDS failed for searchTerms: " + searchTerms + " due to exception: " + ex.getMessage(), ex);
        }
    }
}
