package com.lenskart.nexs.analytics.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.nexs.analytics.model.response.PriceDetails;
import com.lenskart.nexs.analytics.util.LCRDUtil;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@Service
public class OrderOpsConnector {

    @Value("${nexs.analytics.orderops.base.url}")
    private String orderOpsHost;

    @CustomLogger
    private static Logger logger;

    @Value("${nexs.analytics.orderops.rest.timeout}")
    private int timeout;

    @Value("${nexs.analytics.orderops.price.details.url}")
    private String getPriceDetailsUrl;

    public Map<Integer, PriceDetails> fetchPriceDetails(List<Integer> itemIdList) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(itemIdList, httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(orderOpsHost + getPriceDetailsUrl);
        logger.info("Request for Orderops for address details  : " + builder.toUriString());
        RestTemplate template = LCRDUtil.restTemplate(timeout);
        ResponseEntity<String> responseEntity = template.exchange(builder.toUriString(), HttpMethod.POST, httpEntity, String.class, (Object) new Object());
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            Map<Integer, PriceDetails> priceDetailsMap = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<Map<Integer, PriceDetails>>() {
            });
            logger.info("Response from Orderops for address details  : " + priceDetailsMap);

            return priceDetailsMap;
        } else {
            throw new Exception("Fetch address details from orderops failed ,response is " + responseEntity.getStatusCodeValue() + "response is" + responseEntity.getBody());
        }

    }
}
