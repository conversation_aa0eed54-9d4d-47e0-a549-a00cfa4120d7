package com.lenskart.nexs.analytics.activity.controller;

import com.lenskart.nexs.analytics.activity.service.OperationDetailsService;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import com.lenskart.nexs.responseBuilder.ResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/nexs/analytics/barcode/activity/")
@Slf4j
public class BarcodeActivityController {

    @Autowired
    private ResponseBuilder responseBuilder;

    @Autowired
    OperationDetailsService operationDetailsService;

    @RequestMapping(value = Constants.FETCH_OPERATION_DETAILS, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> fetchOperationDetails(@RequestBody OperationProcessingDetails operationProcessingDetails,
                                                                   @RequestHeader(value = "facility-code") String facilityCode) throws Exception {

        OperationProcessingDetails response = new OperationProcessingDetails();
        response.setBarcode(operationProcessingDetails.getBarcode());
        response.setOperationDetails(operationProcessingDetails.getOperationDetails());
        try {
             response = operationDetailsService.fetchOperationDetails(operationProcessingDetails);
        } catch (Exception e) {
            log.error("Exception in fetching operationId details {}", e.getMessage());
        }
        return responseBuilder.successResponse(response, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

}
