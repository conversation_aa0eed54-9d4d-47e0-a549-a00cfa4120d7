package com.lenskart.nexs.analytics.activity.repository.picking;

import com.lenskart.nexs.analytics.entities.picking.PickingDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PickingDetailRepository extends JpaRepository<PickingDetail, Long> {
    List<PickingDetail> findByShipmentId(String shipmentId);
}
