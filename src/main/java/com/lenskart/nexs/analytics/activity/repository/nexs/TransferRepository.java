package com.lenskart.nexs.analytics.activity.repository.nexs;

import com.lenskart.nexs.analytics.entities.nexs.Transfer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TransferRepository extends JpaRepository<Transfer, Long> {

    @Query(value = "SELECT ot.* FROM nexs.outward_transfer ot\n" +
            "INNER JOIN nexs.outward_transfer_item oti on oti.transfer_id = ot.id\n" +
            "INNER JOIN nexs.outward_transfer_barcode otb on otb.transfer_item_id = oti.id\n" +
            "where otb.id = :barcodeId", nativeQuery = true)
    Optional<Transfer> findByBarcodeId(@Param("barcodeId") Long barcodeId);

}
