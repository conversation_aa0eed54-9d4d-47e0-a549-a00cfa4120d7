package com.lenskart.nexs.analytics.activity.repository.wms;

import com.lenskart.nexs.analytics.entities.wms.CourierShipmentDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CourierShipmentDetailRepository extends JpaRepository<CourierShipmentDetails, Long> {
    @Query(value = "SELECT csd.* FROM courier_shipment_details csd INNER JOIN courier_shipment cs ON cs.id = csd.courier_shipment_id where cs.shipping_package_id=:shippingPackageId", nativeQuery = true)
    Optional<CourierShipmentDetails> findByShippingPackageId(@Param("shippingPackageId") String shippingPackageId);
}
