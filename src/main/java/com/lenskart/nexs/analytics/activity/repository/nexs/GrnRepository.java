package com.lenskart.nexs.analytics.activity.repository.nexs;

import com.lenskart.nexs.analytics.entities.nexs.Transfer;
import com.lenskart.nexs.common.entity.po.grn.GrnItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Optional;

@Repository
public interface GrnRepository extends JpaRepository<GrnItemEntity, BigInteger> {

    Optional<GrnItemEntity> findByBarcode(String barcode);

    Optional<GrnItemEntity> findById(Long id);
}
