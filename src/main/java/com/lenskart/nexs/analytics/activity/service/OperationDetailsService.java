package com.lenskart.nexs.analytics.activity.service;

import com.lenskart.nexs.analytics.activity.model.ActionConfig;
import com.lenskart.nexs.analytics.activity.model.FetchOperationDetailsConfig;
import com.lenskart.nexs.analytics.activity.model.OperationDetails;
import com.lenskart.nexs.analytics.activity.model.OperationStatus;
import com.lenskart.nexs.analytics.activity.service.executors.OperationDetailExecutor;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.wms.enums.OrderItemOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OperationDetailsService {

    @Autowired
    @Qualifier("fetchOperationDetailsConfig")
    FetchOperationDetailsConfig fetchOperationDetailsConfig;

    @Autowired
    ApplicationContext applicationContext;

    public OperationProcessingDetails fetchOperationDetails(OperationProcessingDetails operationProcessingDetailsRequest) throws Exception{
        log.info("[fetchOperationDetails] operationDetailsRequest: {}", operationProcessingDetailsRequest);
        log.info("[fetchOperationDetailsConfig] fetchOperationDetailsConfig: {}", fetchOperationDetailsConfig);
        for (OperationDetails operationDetails : operationProcessingDetailsRequest.getOperationDetails()) {
            OperationStatus key = new OperationStatus(operationDetails.getOperation(), operationDetails.getStatus());
            ActionConfig actionConfig = fetchOperationDetailsConfig.operationExecutors.get(key);
            if (actionConfig != null && operationDetails.getActionId() != null) {
                OperationDetailExecutor operationDetailExecutor = (OperationDetailExecutor) applicationContext.getBean(actionConfig.getExecutor());
                operationDetails.setExecutor(actionConfig.getExecutor());
                operationDetailExecutor.execute(operationProcessingDetailsRequest,operationDetails.getActionId(), actionConfig);
            }
        }
        return operationProcessingDetailsRequest;
    }
}
