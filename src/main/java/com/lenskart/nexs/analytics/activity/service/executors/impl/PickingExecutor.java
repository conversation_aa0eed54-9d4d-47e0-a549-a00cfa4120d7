package com.lenskart.nexs.analytics.activity.service.executors.impl;

import com.lenskart.nexs.analytics.activity.model.ActionConfig;
import com.lenskart.nexs.analytics.activity.repository.picking.PickListOrderItemRepository;
import com.lenskart.nexs.analytics.activity.model.OperationDetails;
import com.lenskart.nexs.analytics.activity.service.executors.OperationDetailExecutor;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import com.lenskart.nexs.analytics.monitoring.entities.picking.PicklistOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@Component("PickingExecutor")
public class PickingExecutor implements OperationDetailExecutor {

    @Autowired
    PickListOrderItemRepository pickListOrderItemRepository;
    @Override
    public void execute(OperationProcessingDetails operationProcessingDetails, String actionId, ActionConfig actionConfig) throws Exception{

        try {
            String redirectLink = actionConfig.getRedirectLink();
            String linkParameter = actionConfig.getLinkParameter();
            String executor = actionConfig.getExecutor();
            log.info("[PickingExecutor] started with actionId : {}", actionId);
            Optional<PicklistOrderItem> picklistOrderItem = pickListOrderItemRepository.findById(Long.valueOf(actionId));
            for (OperationDetails operationDetail : operationProcessingDetails.getOperationDetails()) {
                if (operationDetail.getActionId() != null && operationDetail.getActionId().equals(actionId) && executor.equals(operationDetail.getExecutor())) {
                    if (picklistOrderItem.isPresent()) {
                        log.info("[PickingExecutor] started with pickListOrderItem : {}", picklistOrderItem);
                        operationDetail.setOperationId(picklistOrderItem.get().getShipmentId());
                        redirectLink = redirectLink.replace("{" + linkParameter + "}", String.valueOf(picklistOrderItem.get().getShipmentId()));
                        operationDetail.setLink(redirectLink);
                    } else {
                        operationDetail.setOperationId(null);
                        operationDetail.setLink(null);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in Picking Executor with exception {}", e.getMessage());
        }
    }
}
