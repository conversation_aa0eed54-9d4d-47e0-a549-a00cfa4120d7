package com.lenskart.nexs.analytics.activity.service.executors.impl;

import com.lenskart.nexs.analytics.activity.model.ActionConfig;
import com.lenskart.nexs.analytics.entities.putaway.PutawayItemEntity;
import com.lenskart.nexs.analytics.activity.model.OperationDetails;
import com.lenskart.nexs.analytics.activity.repository.putaway.PutawayItemRepository;
import com.lenskart.nexs.analytics.activity.service.executors.OperationDetailExecutor;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@Component("PutawayExecutor")
public class PutawayExecutor implements OperationDetailExecutor {

    @Autowired
    PutawayItemRepository putawayItemRepository;
    @Override
    public void execute(OperationProcessingDetails operationProcessingDetails, String actionId, ActionConfig actionConfig) throws Exception{

        try {
            String redirectLink = actionConfig.getRedirectLink();
            String linkParameter = actionConfig.getLinkParameter();
            String executor = actionConfig.getExecutor();
            log.info("[PutawayExecutor] started with actionId : {}", actionId);
            Optional<PutawayItemEntity> putawayItem = putawayItemRepository.findById(Integer.parseInt(actionId));
            for (OperationDetails operationDetail : operationProcessingDetails.getOperationDetails()) {
                if (operationDetail.getActionId() != null && operationDetail.getActionId().equals(actionId) && executor.equals(operationDetail.getExecutor())) {
                    if (putawayItem.isPresent()) {
                        log.info("[PutawayExecutor] started with putawayItem : {}", putawayItem);
                        operationDetail.setOperationId(putawayItem.get().getPutawayCode());
                        redirectLink = redirectLink.replace("{" + linkParameter + "}", String.valueOf(putawayItem.get().getPutawayCode()));
                        operationDetail.setLink(redirectLink);
                    } else {
                        operationDetail.setOperationId(null);
                        operationDetail.setLink(null);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in Putaway Executor with exception {}", e.getMessage());
        }
    }
}
