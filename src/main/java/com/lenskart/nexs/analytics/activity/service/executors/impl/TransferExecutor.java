package com.lenskart.nexs.analytics.activity.service.executors.impl;

import com.lenskart.nexs.analytics.activity.model.ActionConfig;
import com.lenskart.nexs.analytics.entities.nexs.Transfer;
import com.lenskart.nexs.analytics.activity.model.OperationDetails;
import com.lenskart.nexs.analytics.activity.repository.nexs.TransferRepository;
import com.lenskart.nexs.analytics.activity.service.executors.OperationDetailExecutor;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@Component("TransferExecutor")
public class TransferExecutor implements OperationDetailExecutor {
    @Autowired
    TransferRepository transferRepository;
    @Override
    public void execute(OperationProcessingDetails operationProcessingDetails, String actionId, ActionConfig actionConfig) throws Exception{

        try {
            String redirectLink = actionConfig.getRedirectLink();
            String linkParameter = actionConfig.getLinkParameter();
            String executor = actionConfig.getExecutor();
            log.info("[TransferExecutor] started with actionId : {}", actionId);
            Optional<Transfer> transfers = transferRepository.findByBarcodeId(Long.valueOf(actionId));
            for (OperationDetails operationDetail : operationProcessingDetails.getOperationDetails()) {
                if (operationDetail.getActionId() != null && operationDetail.getActionId().equals(actionId) && executor.equals(operationDetail.getExecutor())) {
                    if (transfers.isPresent()) {
                        log.info("[TransferExecutor] started with transfers : {}", transfers);
                        operationDetail.setOperationId(transfers.get().getTransferCode());
                        redirectLink = redirectLink.replace("{" + linkParameter + "}", String.valueOf(transfers.get().getTransferCode()));
                        operationDetail.setLink(redirectLink);
                    } else {
                        operationDetail.setOperationId(null);
                        operationDetail.setLink(null);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in Transfer Executor with exception {}", e.getMessage());
        }
    }
}
