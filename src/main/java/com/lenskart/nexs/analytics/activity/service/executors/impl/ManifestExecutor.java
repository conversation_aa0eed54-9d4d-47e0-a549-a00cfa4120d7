package com.lenskart.nexs.analytics.activity.service.executors.impl;

import com.lenskart.nexs.analytics.activity.model.ActionConfig;
import com.lenskart.nexs.analytics.entities.manifest.ManifestShipments;
import com.lenskart.nexs.analytics.activity.model.OperationDetails;
import com.lenskart.nexs.analytics.activity.repository.manifest.ManifestShipmentRepository;
import com.lenskart.nexs.analytics.activity.service.executors.OperationDetailExecutor;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@Component("ManifestExecutor")
public class ManifestExecutor implements OperationDetailExecutor {

    @Autowired
    ManifestShipmentRepository manifestShipmentRepository;
    @Override
    public void execute(OperationProcessingDetails operationProcessingDetails, String actionId, ActionConfig actionConfig) throws Exception{

        try {
            String redirectLink = actionConfig.getRedirectLink();
            String linkParameter = actionConfig.getLinkParameter();
            String executor = actionConfig.getExecutor();
            log.info("[ManifestExecutor] started with actionId : {}", actionId);
            Optional<ManifestShipments> manifestShipments = manifestShipmentRepository.findById(Integer.parseInt(actionId));
            for (OperationDetails operationDetail : operationProcessingDetails.getOperationDetails()) {
                if (operationDetail.getActionId() != null && operationDetail.getActionId().equals(actionId) && executor.equals(operationDetail.getExecutor())) {
                    if (manifestShipments.isPresent()) {
                        log.info("[ManifestExecutor] started with manifestShipment : {}", manifestShipments);
                        operationDetail.setOperationId(manifestShipments.get().getShippingManifestId());
                        redirectLink = redirectLink.replace("{" + linkParameter + "}", String.valueOf(manifestShipments.get().getShippingPackageId()));
                        operationDetail.setLink(redirectLink);
                    } else {
                        operationDetail.setOperationId(null);
                        operationDetail.setLink(null);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in Manifest Executor with exception {}", e.getMessage());
        }
    }
}
