package com.lenskart.nexs.analytics.activity.service.executors.impl;

import com.lenskart.nexs.analytics.activity.model.ActionConfig;
import com.lenskart.nexs.analytics.activity.model.OperationDetails;
import com.lenskart.nexs.analytics.activity.service.executors.OperationDetailExecutor;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.repository.wms.OrderItemRepository;
import com.lenskart.nexs.analytics.activity.request.OperationProcessingDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@Component("ShippingPackageExecutor")
public class ShippingPackageExecutor implements OperationDetailExecutor {

    @Autowired
    OrderItemRepository orderItemRepository;

    @Override
    public void execute(OperationProcessingDetails operationProcessingDetails, String actionId, ActionConfig actionConfig) throws Exception{

        try {
            String redirectLink = actionConfig.getRedirectLink();
            String linkParameter = actionConfig.getLinkParameter();
            String executor = actionConfig.getExecutor();
            log.info("[ShippingPackageExecutor] started with actionId : {}", actionId);
            OrderItems orderItems = orderItemRepository.findByOrderItemId(Integer.parseInt(actionId));
            for (OperationDetails operationDetail : operationProcessingDetails.getOperationDetails()) {
                if (operationDetail.getActionId() != null && operationDetail.getActionId().equals(actionId) && executor.equals(operationDetail.getExecutor())) {
                    if (orderItems!=null) {
                        log.info("[ShippingPackageExecutor] started with orderItems : {}", orderItems);
                        operationDetail.setOperationId(orderItems.getShippingPackageId());
                        redirectLink = redirectLink.replace("{" + linkParameter + "}", String.valueOf(orderItems.getShippingPackageId()));
                        operationDetail.setLink(redirectLink);
                    } else {
                        operationDetail.setOperationId(null);
                        operationDetail.setLink(null);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in Shipping Package Executor with exception {}", e.getMessage());
        }
    }
}
