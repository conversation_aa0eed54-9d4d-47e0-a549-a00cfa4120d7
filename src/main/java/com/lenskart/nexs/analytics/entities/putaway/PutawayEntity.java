package com.lenskart.nexs.analytics.entities.putaway;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
//@ToString
@RequiredArgsConstructor
@Entity
@Table(name = "putaway")
public class PutawayEntity implements Serializable {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "putaway_code", unique = true, nullable = false)
	private Integer putawayCode;
	@Column(name = "unicom_putaway_code")
	private String unicomPutawayCode;
	@Column(name = "unicom_status")
	private String unicomStatus;
	@Column(name = "putaway_type")
	private String putawayType;
	@Column(name = "location_id")
	private String locationId;
	@Column(name = "status")
	private String status;
	@Column(name = "number_of_items")
	private Integer numberOfItems;
	@Column(name = "created_at")
	private Date createdAt;
	@Column(name = "updated_at")
	private Date updatedAt;
	@Column(name = "facility_code")
	private String facilityCode;
	@Column(name = "scan_type")
	private String scanType;
	@Column(name = "location_scan_type")
	private String locationScanType;
	@Column(name = "created_by")
	private String createdBy;
	@Column(name = "updated_by")
	private String updatedBy;
	@Transient
	private String oldFacilityCode;
	@JsonIgnore
	@OneToMany(targetEntity = PutawayItemEntity.class, cascade = CascadeType.ALL)
	@JoinColumn(name = "putaway_code", referencedColumnName = "putaway_code", nullable = false)
	private List<PutawayItemEntity> putawayItem = new ArrayList<>();
	@JsonIgnore
	@OneToMany(targetEntity = PutawayReferenceEntity.class, cascade = CascadeType.ALL)
	@JoinColumn(name = "putaway_id", referencedColumnName = "putaway_code", nullable = false)
	private List<PutawayReferenceEntity> putawayReference = new ArrayList<>();
}
