package com.lenskart.nexs.analytics.entities.picking;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


@Data
@Entity
@Table(name = "picking_detail", uniqueConstraints = { @UniqueConstraint(columnNames = { "wms_order_item_id", "picking_source"})})
public class PickingDetail implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "assigned_to", nullable = false)
    private String assignedTo;

    @Column(name = "picking_summary_id", nullable = false)
    private Long pickingSummaryId;

    @Column(name = "increment_id", nullable = false)
    private Integer incrementId;

    @Column(name = "wms_order_item_id", nullable = false)
    private Integer wmsOrderItemId;

    @Column(name = "product_id", nullable = false)
    private Integer productId;

    @Column(name = "product_name", nullable = false) //TODO removing nullable constraint
    private String productName;

    @Column(name = "channel", nullable = false)
    private String channel;

    @Column(name = "product_type", nullable = false)
    private String productType;

    @Column(name = "order_item_count", nullable = false)
    private Integer orderItemCount;

    @Column(name = "item_barcode")
    private String itemBarcode;

    @Column(name = "location_barcode")
    private String locationBarcode;

    @Column(name = "location_hierarchy", columnDefinition = "JSON")
    private String locationHierarchy;

    @Column(name = "shipment_id", nullable = false)
    private String shipmentId;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "detail")
    private String detail;

    @Column(name = "box_code")
    private String boxCode;

    @Column(name = "merged_box_code")
    private String mergedBoxCode;

    @Column(name = "merged_by")
    private Integer mergedBy;

    @Column(name = "picklist_order_item_id")
    private Long picklistOrderItemId;

    @Column(name = "mark_picked_wms")
    private Integer markPickedWms;

    @Column(name = "mark_picked_ims")
    private Integer markPickedIms;

    @Column(name = "product_quantity")
    private Integer productQuantity;

    @Column(name = "order_type")
    private String orderType;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "facility")
    private String facility;

    @Column(name = "hold_reason")
    private String holdReason;

    @Column(name = "hold_reason_submitted_at")
    private Date holdReasonSubmittedAt;

    @Column(name = "skipped_reason")
    private String skippedReason;

    @Column(name = "duplicate")
    private boolean duplicate = false;

    @Column(name = "qc_required")
    private boolean qcRequired = false;

    @Transient
    private Integer newProductId;

    @Column(name = "fitting_id")
    private Integer fittingId;

    @Transient
    private List<String> imageUrl;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false)
    private Date createdAt;

    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="updated_at")
    @Field(value = "updatedDate", type = FieldType.Date, format = DateFormat.custom, pattern = "uuuu-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt = Calendar.getInstance().getTime();

    @Column(name = "product_image")
    private String productImage;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Version
    @Column(name = "version", columnDefinition = "integer DEFAULT 0", nullable = false)
    private Integer version;

    @Column(name = "skipped_count")
    private Integer skippedCount = 0;

    @Column(name = "fitting")
    private String fitting;

    @Column(name = "jit_order")
    @Enumerated(EnumType.ORDINAL)
    private JITType jitOrder;

    @Column(name = "fast_picking")
    private Boolean fastPicking;

    @Column(name = "current_location_code")
    private String currentLocationCode;

    @Column(name = "skipped_by")
    private String skippedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "skipped_date")
    private Date skippedDate;

//    @Column(name = "assigned_to")
//    private String assignedTo;

//    @Transient
//    private List<CustomOption> customOption;

    @Column(name = "repick_status")
    private String repickStatus;


    @Column(name = "secondary_status")
    private String secondaryStatus;


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "picking_cutoff")
    private Date pickingCutoff;

    @Column(name = "group_id")
    private String groupId;


    @Column(name = "picking_source")
    private String pickingSource;

    @Column(name = "asrs_unallocated_count")
    private Integer asrsUnallocatedCount;

    @Column(name = "is_bulk_order")
    private boolean isBulkOrder;
    public PickingDetail(String assignedTo, Long pickingSummaryId, Integer orderId, Integer productId, String productName, String channel, String type, Integer noOfOrderItem, String locationBarcode,
                         String shipmentId, Date createdAt, String status, Integer picklistOrderItemId, String orderType, Integer priority, String facility, Integer wmsOrderItemId,
                         String fitting, Integer jitOrder, boolean fastPicking, Date pickingCutoff, boolean isBulkOrder) {
        this.assignedTo = assignedTo;
        this.pickingSummaryId = pickingSummaryId;
        this.incrementId = orderId;
        this.newProductId = productId;
        this.productName = productName;
        this.channel = channel;
        this.productType = type;
        this.orderItemCount = noOfOrderItem;
        this.locationBarcode = locationBarcode;
        this.shipmentId = shipmentId;
        this.createdAt = createdAt;
        this.status = status;
        this.picklistOrderItemId = Long.valueOf(picklistOrderItemId);
        this.orderType = orderType;
        this.priority = priority;
        this.facility = facility;
        this.wmsOrderItemId = wmsOrderItemId;
        this.productId = productId;
        this.fitting = fitting;
        this.jitOrder = JITType.get(jitOrder);
        this.fastPicking = fastPicking;
        this.pickingCutoff = pickingCutoff;
        this.isBulkOrder = isBulkOrder;
    }

    public PickingDetail(PickingDetail pickingDetail) {
        this.assignedTo = pickingDetail.assignedTo;
        this.pickingSummaryId = pickingDetail.pickingSummaryId;
        this.incrementId = pickingDetail.incrementId;
        //this.itemId = pickingDetail.itemId;
        this.productName = pickingDetail.productName;
        this.channel = pickingDetail.channel;
        this.productType = pickingDetail.productType;
        this.orderItemCount = pickingDetail.orderItemCount;
        this.itemBarcode = pickingDetail.itemBarcode;
        this.locationBarcode = pickingDetail.locationBarcode;
        this.shipmentId = pickingDetail.shipmentId;
        this.status = pickingDetail.status;
        this.detail = pickingDetail.detail;
        this.boxCode = pickingDetail.boxCode;
        this.createdAt = pickingDetail.createdAt;
        this.updatedAt = pickingDetail.updatedAt;
        // this.unicomPicklistId = pickingDetail.unicomPicklistId;
        this.mergedBoxCode = pickingDetail.mergedBoxCode;
        this.mergedBy = pickingDetail.mergedBy;
        this.picklistOrderItemId = pickingDetail.picklistOrderItemId;
        this.markPickedWms = pickingDetail.markPickedWms;
        this.markPickedIms = pickingDetail.markPickedIms;
        this.newProductId = pickingDetail.newProductId;
        this.imageUrl = pickingDetail.imageUrl;
        this.orderType = pickingDetail.orderType;
        this.priority = pickingDetail.priority;
        this.productId = pickingDetail.productId;
        this.fitting = pickingDetail.fitting;
        this.jitOrder = pickingDetail.jitOrder;
        this.fastPicking = pickingDetail.fastPicking;
        this.pickingCutoff = pickingDetail.pickingCutoff;
        this.secondaryStatus=pickingDetail.secondaryStatus;
        this.isBulkOrder = pickingDetail.isBulkOrder;
    }

}
