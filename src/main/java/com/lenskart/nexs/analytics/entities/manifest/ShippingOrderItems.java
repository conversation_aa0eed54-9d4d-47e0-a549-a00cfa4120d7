package com.lenskart.nexs.analytics.entities.manifest;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "shipping_order_items")
public class ShippingOrderItems {

    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    private int id;

    @Column(name="item_name")
    private String itemName;

    @Column(name="quantity")
    private int quantity;

    @Column(name="line_item_identifier")
    private int lineItemIdentifier;

    @ManyToOne(cascade = CascadeType.ALL,fetch = FetchType.LAZY)
    @JoinColumn(
            name = "manifest_shipment_id")
    private ManifestShipments manifestShipmentId;

    @Override
    public String toString(){
        if (this.quantity > 1){
            return this.itemName + "(" + this.quantity + ")" ;
        }
        return this.itemName;
    }

}
