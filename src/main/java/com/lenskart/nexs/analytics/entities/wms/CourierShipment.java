package com.lenskart.nexs.analytics.entities.wms;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "courier_shipment")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")

public class CourierShipment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "shipment_id", nullable = false)
    private Long shipmentId;

    @Column(name = "increment_id", nullable = false)
    private Long incrementId;

    @Column(name = "packet_count", nullable = false)
    private Integer packetCount;

    @Column(name = "priority_level", nullable = false)
    private int priorityLevel;

    @Column(name = "bin_id")
    private String binId;

    @Column(name = "shipment_payment_method", nullable = false)
    private String shipmentPaymentMethod;

    @Column(name = "shipment_amount", nullable = false)
    private Double shipmentAmount = 0.00;

    @Column(name = "shipment_weight", nullable = false)
    private Double shipmentWeight = 0.00;

    @Column(name = "shipping_charges", nullable = false)
    private Double shippingCharges = 0.00;

    @OneToMany(mappedBy = "shipment", fetch = FetchType.EAGER)
 //   @JsonManagedReference
    private List<CourierShipmentDetails> shipmentDetails;

    @Column(name = "created_at", nullable = true)
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date createdAt;

    @Column(name = "shipping_package_id", nullable = true)
    private String shippingPackageId;

    @Column(name = "sync")
    private boolean sync;

    @Column(name = "routing_code")
    private String routingCode;

    @Column(name = "collectible_amount")
    private Double collectibleAmount;
}

