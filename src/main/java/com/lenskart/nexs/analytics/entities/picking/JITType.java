package com.lenskart.nexs.analytics.entities.picking;

import lombok.extern.slf4j.Slf4j;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public enum JITType {
    NON_JIT(0),
    MANUAL(1),
    AUTO(2);

    private static final Map<Integer,JITType> reverseLookup
            = new HashMap<Integer,JITType>();

    static {
        for(JITType jt : EnumSet.allOf(JITType.class))
            reverseLookup.put(jt.getJitType(), jt);
    }
    private final int jitType;

    JITType(int jitType) {
        this.jitType = jitType;
    }

    public int getJitType() {
        return jitType;
    }

    public static JITType get(int code) {
        if(code>3) {
            log.info("[JITType]The code {} provided is not in the range hence making it non_jit by default",code);
            return reverseLookup.get(0);
        }
        return reverseLookup.get(code);
    }
}
