package com.lenskart.nexs.analytics.entities.putaway;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@Table(name = "putaway_reference")
public class PutawayReferenceEntity implements Serializable {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Integer id;
	@JsonIgnore
	@ManyToOne
	@JoinColumn(name = "putaway_id", insertable = false, updatable = false)
	private PutawayEntity putaway;
	@Column(name = "putaway_type")
	private String putawayType;
	@Column(name = "reference_id")
	private String referenceId;
	@Column(name = "unicom_status")
	private int unicomStatus;
	@Column(name = "status")
	private String status;
	@Column(name = "facility_code")
	private String facilityCode;
	@Column(name = "unicom_error")
	private String unicomError;
	@Column(name = "created_at")
	private Date createdAt;
	@Column(name = "updated_at")
	private Date updatedAt;
	@Column(name = "created_by")
	private String createdBy;
	@Column(name = "retry_count")
	private Integer retryCount;

	@JsonProperty("putaway_id")
	public String getPutawayId() {
		if (putaway != null & putaway.getPutawayCode() != null)
			return String.valueOf(this.putaway.getPutawayCode());
		return null;
	}
}
