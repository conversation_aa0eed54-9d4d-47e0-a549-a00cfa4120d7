package com.lenskart.nexs.analytics.entities.putaway;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
//@ToString
@RequiredArgsConstructor
@Entity
@Table(name = "putaway_item")
public class PutawayItemEntity implements Serializable {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	private Integer id;
	@JsonIgnore
	@ManyToOne
	@JoinColumn(name = "putaway_code",  insertable = false, updatable = false)
	private PutawayEntity putaway;
	@Column(name = "old_putaway_code")
	private String oldPutawayCode;
	@Column(name = "putaway_type")
	private String putawayType;
	@Column(name = "putaway_reference_id")
	private String putawayReferenceId;
	@Column(name = "unicom_putaway_code")
	private String unicomPutawayCode;
	@Column(name = "unicom_item_code")
	private String unicomItemCode;
	@Column(name = "is_unicom_processed")
	private String isUnicomProcessed;
	@Column(name = "barcode")
	private String barcode;
	@Column(name = "barcode_location_id")
	private String barcodeLocationId;
	@Column(name = "status")
	private String status;
	@Column(name = "reason")
	private String reason;
	@Column(name = "inventory_type")
	private String inventoryType;
	@Column(name = "created_at")
	private Date createdAt;
	@Column(name = "updated_at")
	private Date updatedAt;
	@Column(name = "created_by")
	private String createdBy;
	@Column(name = "updated_by")
	private String updatedBy;
	@Column(name = "box_barcode")
	private String boxBarcode;
	@Column(name = "pid")
	private String pid;
	@Column(name = "source")
	private String source;
	@Column(name = "unicom_grn_no")
	private String unicomGrnNo;
	@Column(name = "execution_started_by")
	private String executionStartedBy;
	@Column(name = "execution_updated_by")
	private String execution_updated_by;
	@Column(name = "execution_completed_by")
	private String executionCompletedBy;
	@Transient
	private int qty;
	@Transient
	private String productDescription;
	@Transient
	private String action;
	@Transient
	private String oldBarcodeStatus;

	@JsonProperty("putaway_code")
	public String getPutawayCode() {
		if (putaway != null & putaway.getPutawayCode() != null)
			return String.valueOf(this.putaway.getPutawayCode());
		return null;
	}
}
