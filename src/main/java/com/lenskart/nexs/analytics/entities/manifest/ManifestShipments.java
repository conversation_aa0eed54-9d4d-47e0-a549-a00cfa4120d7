package com.lenskart.nexs.analytics.entities.manifest;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "manifest_shipments")
@Data
@Slf4j
public class ManifestShipments {

    @Id
    @GeneratedValue(
            strategy = GenerationType.IDENTITY
    )
    private int id;

    @Column(name="shipping_package_id")
    private String shippingPackageId;

    @Column(name="facility_code")
    private String facilityCode;

    @Column(name="increment_id")
    private Integer incrementId;

    @Column(name="invoice_code")
    private String invoiceCode;

    @Column(name="shipping_provider_code")
    private String shippingProviderCode;

    @Column(name="shipping_method")
    private String shippingMethod;

    @Column(name="shipping_package_type")
    private String shippingPackageType;

    @Column(name="tracking_number")
    private String trackingNumber;

    @Column(name="tracking_status")
    private String trackingStatus;

    @Column(name="shipping_manifest_id")
    private String shippingManifestId;

    @Column(name="channel")
    private String channel;

    @Column(name="account_code")
    private String accountCode;

    @Column(name="created_at")
    private Date createdAt;

    @Column(name="updated_at")
    private Date updatedAt;

    @Column(name="created_by")
    private String createdBy;

    @Column(name="updated_by")
    private String updatedBy;

    @Column(name="dispatched_at")
    private Date dispatchedAt;

    @Column(name="delivered_at")
    private Date deliveredAt;

    @Column(name="number_Of_items")
    private Integer numberOfItems;

    @Column(name="number_of_boxes")
    private Integer numberOfBoxes;

    @Column(name="weight")
    private Double weight;

    @Column(name="length")
    private Double length;

    @Column(name="width")
    private Double width;

    @Column(name="height")
    private Double height;

    @Column(name="total_price")
    private Double totalPrice;

    @Column(name="collectable_amount")
    private Double collectableAmount;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "customer_address")
    private CustomerAddress customerAddress;

    @Column(name="lk_country")
    private String lkCountry;

    @OneToMany(mappedBy = "manifestShipmentId")
    private List<ShippingOrderItems> shippingOrderItems;

    @Column(name="wms_sync_status")
    private String wmsSyncStatus;
}
