package com.lenskart.nexs.analytics.entities.nexs;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table(name = "outward_transfer")
public class Transfer implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "transfer_code")
    private String transferCode;

    @Column(name = "unicom_transfer_code")
    private String unicomTransferCode;

    @Column(name = "source_facility")
    private String sourceFacility;

    @Column(name = "destination_facility")
    private String destinationFacility;

    @Column(name = "status")
    private String status;

    @Column(name = "total_qty")
    private Integer totalQty = 0;

    @Column(name = "total_pid")
    private Integer totalPid = 0;

    @Column(name = "is_status_synced")
    private boolean isStatusSynced;

    @Column(name = "retry_count")
    private int retryCount = 0;

    @Column(name = "eventual_retry_count")
    private int eventualRetryCount = 0;

    @Column(name = "processed_barcode_count")
    private int processedBarcodeCount = 0;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at")
    private Date createdAt;

    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "sale_order_created_at")
    private Date saleOrderCreatedAt;

    @Column(name = "sale_order_code")
    private String saleOrderCode;

    @Column(name = "channel")
    private String channel;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "total_price")
    private Double totalPrice = 0.0;

    @Column(name = "margin")
    private int margin = 0;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "purchase_order")
    private String purchaseOrder;

    @Column(name = "picklist_number")
    private String picklistNumber;

    @Column(name = "awb_number")
    private String awbNumber;

    @Column(name = "shipment_id")
    private String shipmentId;

    @Column(name = "manifest_number")
    private String manifestNumber;

    @Column(name = "transfer_status")
    private Double outwardTransferStatus;

    @Column(name = "fitting_type")
    @Enumerated(EnumType.STRING)
    private FittingType fittingType;

    @Column(name = "grn_code")
    private String grnCode;

    @Column(name = "putway_number")
    private String putawayNumber;

    @Column(name = "unicom_putaway_code")
    private String unicomPutawayCode;

    @Column(name = "is_po_invoice_grn_closed")
    private boolean isPoInvoiceGrnClosed;

    @Column(name = "transfer_type")
    @Enumerated(EnumType.ORDINAL)
    private TransferType transferType;

    @Version
    @Column(name = "version")
    private Integer version;

    @Column(name = "enable_box_barcode")
    private Boolean enableBoxBarcode;

    @Column(name = "is_async_receive_transfer")
    private boolean isAsyncReceiveTransfer;

    @Column(name = "is_finance_system_sync")
    private Boolean isFinanceSystemSync;

    @Column(name = "sub_status")
    private String subStatus;

    @Column(name = "shipping_provider")
    private String shippingProvider;
}
