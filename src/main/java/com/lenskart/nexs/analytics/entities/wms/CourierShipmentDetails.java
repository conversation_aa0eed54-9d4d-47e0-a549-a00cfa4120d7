package com.lenskart.nexs.analytics.entities.wms;


import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "courier_shipment_details")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
public class CourierShipmentDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
            name = "courier_shipment_id",
            referencedColumnName = "id"
    )
    private CourierShipment shipment;

    @Column(name = "shipment_id", nullable = false)
    private Long shipmentId;

    @Column(name = "shipping_charges", nullable = false)
    private Double shippingCharges = 0.00;

    @Column(name = "length", nullable = true)
    private Double length = 0.00;

    @Column(name = "breadth", nullable = true)
    private Double breadth = 0.00;

    @JsonIgnore
    @Column(name = "is_label_printed", nullable = true)
    private Boolean isLabelPrinted = false;

    @Column(name = "height", nullable = true)
    private Double height = 0.00;

    @Column(name = "courier_code", nullable = true)
    private String CourierCode;

    @Column(name = "awb_number", nullable = true)
    private String awbNumber;

    @Column(name = "master_awb", nullable = true)
    private String masterAwb;


    @Column(name = "pickup_facility_code", nullable = true)
    private String pickupFacilityCode;

    @Column(name = "rto_facility_code", nullable = true)
    private String rtoFacilityCode;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    @UpdateTimestamp
    private Date updatedAt;

    @Column(name = "order_id", nullable = true)
    private String orderID;

    @Column(name = "courier_shipping_url")
    private String courierShippingUrl;

    @Column(name = "lk_shipping_url")
    private String lkShippingUrl;

}


