package com.lenskart.nexs.analytics.constants;

public interface SPTConstants {

    String FETCH_SPT_WMS_DATA_QUERY = "SELECT  oih.history_id, oih.barcode, oih.order_item_id, oih.payment_method, oih.wms_order_code, oih.product_id, oih.hold, oih.fulfilled_type,\n" +
            "oih.status as order_item_status, oih.updated_at as order_item_updated_at, oih.fitting_type, oh.increment_id, oh.created_at as order_created_at, oh.payment_status, oih.shipping_package_id, sh.status as shipment_status\n" +
            ",sh.created_at as shipment_created_at, sh.updated_at as shipment_updated_at, pd.picking_summary_id, ps.created_at as picking_summary_created_at\n" +
            "FROM    wms.order_items_history oih\n" +
            "INNER JOIN\n" +
            "        wms.orders_history oh\n" +
            "ON      oh.history_id = \n" +
            "        (\n" +
            "        SELECT  history_id\n" +
            "        FROM    wms.orders_history ohh\n" +
            "        WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                ohh.action_time DESC\n" +
            "        LIMIT 1\n" +
            "        )\n" +
            "INNER JOIN\n" +
            "        wms.shipment_history sh\n" +
            "ON      sh.history_id = \n" +
            "        (\n" +
            "        SELECT  history_id\n" +
            "        FROM    wms.shipment_history shh\n" +
            "        WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                shh.action_time DESC\n" +
            "        LIMIT 1\n" +
            "        ) \n" +
            "LEFT JOIN \n" +
            "        wms.picking_detail pd\n" +
            "ON pd.id = \n" +
            "(\n" +
            "  SELECT  id\n" +
            "        FROM    wms.picking_detail pdd\n" +
            "        WHERE  pdd.wms_order_item_id=oih.id and pdd.updated_at<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                pdd.updated_at DESC\n" +
            "        LIMIT 1\n" +
            ")        \n" +
            "LEFT JOIN \n" +
            "        wms.picking_summary ps\n" +
            "ON ps.id = \n" +
            "(\n" +
            "  SELECT  id\n" +
            "        FROM    wms.picking_summary pss\n" +
            "        WHERE  pss.id=pd.picking_summary_id\n" +
            ")  "+
            "\twhere oih.history_id>= ?" ;
    String FETCH_SPT_WMS_DATA_IN_PAST_FEW_MINUTES = "SELECT  oih.history_id,oih.barcode, oih.order_item_id, oih.payment_method, oih.wms_order_code, oih.product_id, oih.hold, oih.fulfilled_type,\n" +
                    "oih.status as order_item_status, oih.updated_at as order_item_updated_at, oih.fitting_type, oh.increment_id, oh.created_at as order_created_at, oh.payment_status, oih.shipping_package_id, sh.status as shipment_status\n" +
                    ",sh.created_at as shipment_created_at, sh.updated_at as shipment_updated_at, pd.picking_summary_id, ps.created_at as picking_summary_created_at\n" +
                    "FROM    wms.order_items_history oih\n" +
                    "INNER JOIN\n" +
                    "        wms.orders_history oh\n" +
                    "ON      oh.history_id = \n" +
                    "        (\n" +
                    "        SELECT  history_id\n" +
                    "        FROM    wms.orders_history ohh\n" +
                    "        WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time\n" +
                    "        ORDER BY\n" +
                    "                ohh.action_time DESC\n" +
                    "        LIMIT 1\n" +
                    "        )\n" +
                    "INNER JOIN\n" +
                    "        wms.shipment_history sh\n" +
                    "ON      sh.history_id = \n" +
                    "        (\n" +
                    "        SELECT  history_id\n" +
                    "        FROM    wms.shipment_history shh\n" +
                    "        WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time\n" +
                    "        ORDER BY\n" +
                    "                shh.action_time DESC\n" +
                    "        LIMIT 1\n" +
                    "        ) \n" +
                    "LEFT JOIN \n" +
                    "        wms.picking_detail pd\n" +
                    "ON pd.id = \n" +
                    "(\n" +
                    "  SELECT  id\n" +
                    "        FROM    wms.picking_detail pdd\n" +
                    "        WHERE  pdd.wms_order_item_id=oih.id and pdd.updated_at<=oih.action_time\n" +
                    "        ORDER BY\n" +
                    "                pdd.updated_at DESC\n" +
                    "        LIMIT 1\n" +
                    ")        \n" +
                    "LEFT JOIN \n" +
                    "        wms.picking_summary ps\n" +
                    "ON ps.id = \n" +
                    "(\n" +
                    "  SELECT  id\n" +
                    "        FROM    wms.picking_summary pss\n" +
                    "        WHERE  pss.id=pd.picking_summary_id\n" +
                    ")  "+
                    "\twhere oih.action_time >= NOW() - INTERVAL 35 DAY";

    String GET_SPT_SYNC_STATUS_BY_STATUS_AND_HISTORY_ID = "select * from nexs_analytics_shipping_package_timeline_history where status=? and history_id=?";

    String INSERT_SPT_SYNC_STATUS = "INSERT INTO nexs_analytics_shipping_package_timeline_history (`id`, `history_id`, `status`, `reason`" +
            ", `failure_count`) VALUES (?, ?, ?, ?, ?)";

    String GET_SPT_SYNC_STATUS_BY_STATUS = "select * from nexs_analytics_shipping_package_timeline_history where status=?";

    String  GET_HIGHEST_HISTORY_ID_FAILURE_RECORD_WITH_FAILURE_COUNT_GREATER_THAN_1= "select * from nexs_analytics_shipping_package_timeline_history where failure_count > 1 order by history_id desc LIMIT 1";

    String INSERT_SHIPPING_PACKAGE_TIMELINE = "INSERT INTO nexs_shipping_package_timeline (`code`, `displayorderCode`, `cod`,`displayOrderDateTime`," +
            "`saleOrderCode`,`salesWithoutTax`,`sku`,`onhold`,`jitReceivedDate`,`status`,`saleOrderItemStatus`,`paymentCap`,`shippingPackageCode`" +
            ",`shippingPackageStatus`,`picklist`,`picklistCreated`,`shippingPackageShelf`, `saleOrderShelf`, `shippingUpdated`, `soiUpdated`," +
            "`itemCode`, `fittingReqd`, `jitPoCreateTime`,`packetNumber`, `shipmentDate`, `vendorName`,  `grnCreated`, `dualCoFlag`)" +
            " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
}
