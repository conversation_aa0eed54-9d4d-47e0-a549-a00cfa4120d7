package com.lenskart.nexs.analytics.scheduler;

import com.lenskart.nexs.analytics.service.LenskartCompleteReportDailyService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.Date;

@Component
@Slf4j
public class NexsSalesReportSync {
    @Autowired
    private LenskartCompleteReportDailyService lenskartCompleteReportDailyService;

    @Value("${nexs.sales.report.resync.scheduler.enabled}")
    private Boolean nexsReportSchedulerEnable;

    @Scheduled(cron = "${nexs.sales.report.sync.cron}")
    @SchedulerLock(name = "syncMissingOrderScheduledTask")
    public void nexsMissingOrderItemScheduler() {
        if(nexsReportSchedulerEnable) {
            log.info("[syncMissingOrderScheduledTask] schedular started : " + new Date());
            try {
                lenskartCompleteReportDailyService.nexsSalesReportDataSyncScheduler();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("Exception in syncMissingOrderScheduledTask scheduler", e);
            }
            log.info("[syncMissingOrderScheduledTask] schedular completed");
        }
    }
}
