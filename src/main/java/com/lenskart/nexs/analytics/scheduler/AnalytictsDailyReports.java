package com.lenskart.nexs.analytics.scheduler;

import com.lenskart.nexs.analytics.entity.unireports.LcrdSyncStatus;
import com.lenskart.nexs.analytics.service.LenskartCompleteReportDailyService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class AnalytictsDailyReports {

    @Autowired
    private LenskartCompleteReportDailyService lenskartCompleteReportDailyService;

    @Scheduled(cron = "${nexs.analytics.daily.sync.cron}")
    @SchedulerLock(name = "addlenskartCompleteReportDailyScheduledTask")
    public void lenskartCompleteReportDailyScheduler() throws SQLException {
        log.info("LCRD schedular started : " + new Date());
          try {
              lenskartCompleteReportDailyService.lenskartCompleteReportDailyScheduler();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Exception in LenskartCompleteReportDaily scheduler "+ e);
            log.error("lenskartCompleteReportDailyScheduler: exception :" + e.getMessage(), e);

        }
        log.info("LCRD schedular completed");
    }

}
