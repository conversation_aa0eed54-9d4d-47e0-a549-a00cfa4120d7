package com.lenskart.nexs.analytics.monitoring.service;


import com.lenskart.nexs.activity.entities.OrderActivityEvent;
import com.lenskart.nexs.activity.repositories.OrderActivityEventRepository;
import com.lenskart.nexs.analytics.monitoring.mapper.OrderActivityDtoMapper;
import com.lenskart.nexs.analytics.monitoring.pojo.OrderActivityEventDto;
import com.lenskart.nexs.analytics.monitoring.response.OrderActivityEventResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OrderActivityEventService {


    @Autowired
    private OrderActivityEventRepository orderActivityEventRepository;

    public OrderActivityEventResponse getOrderActivityResponse(String wmsOrderCode){
        List<OrderActivityEvent> orderActivityEventList = orderActivityEventRepository.findByReferenceId(wmsOrderCode);
        List<OrderActivityEventDto> orderActivityEventDtoList = OrderActivityDtoMapper.ORDER_ACTIVITY_DTO_MAPPER.mapOrderActivityEvents(orderActivityEventList);
        orderActivityEventDtoList = orderActivityEventDtoList.stream()
                .sorted(Comparator.comparing(OrderActivityEventDto::getItemEventUpdatedAt).reversed())
                .collect(Collectors.toList());
        OrderActivityEventResponse orderActivityEventResponse = new OrderActivityEventResponse();
        orderActivityEventResponse.setOrderActivityEvents(orderActivityEventDtoList);
        return orderActivityEventResponse;
    }

}
