package com.lenskart.nexs.analytics.monitoring.request;

import com.lenskart.nexs.analytics.monitoring.dto.JitOrderStatusDetailsDto;
import com.lenskart.nexs.analytics.monitoring.dto.OrderItemsDto;
import com.lenskart.nexs.analytics.monitoring.dto.PickingDetailDto;
import com.lenskart.nexs.analytics.monitoring.dto.PicklistOrderItemDto;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import lombok.*;


@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RequestPayload {

    Integer orderItemId;
    QueryType queryType;
    String shippingPackageId;
    String status;
    String wmsOrderCode;
    OrderItemsDto orderItemAfterData;
    OrderItemsDto orderItemBeforeData;
    PickingDetailDto pickingDetailData;
    OrderItemsDto orderItems;
    PicklistOrderItemDto picklistOrderItem;
    JitOrderStatusDetailsDto jitOrderStatusDetails;
}
