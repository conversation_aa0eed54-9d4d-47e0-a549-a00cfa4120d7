package com.lenskart.nexs.analytics.monitoring.model;

import com.lenskart.nexs.wms.constants.ErrorType;
import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Setter
@Getter
@ToString
@NoArgsConstructor
@MappedSuperclass
public class MonitorPanelData {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name ="increment_id")
    private Integer incrementId;

    @Column(name ="shipping_package_id")
    private String shippingPackageId;

    @Column(name ="wms_status" )
    private String wmsStatus;

    @Column(name ="mp_status" )
    private String mpStatus;

    @Column(name ="fr_tag" )
    private String frTag;

    @Column(name = "fitting_id")
    private Integer fittingId;

    @Column(name ="order_item_id")
    private Integer orderItemId;

    @Column(name ="product_id")
    private Integer productId;

    @Column(name ="wms_order_code")
    private String wmsOrderCode;

    @Column(name ="ageing_since_created")
    private float ageingSinceCreated;

    @Column(name = "ageing_since_last_update")
    private float ageingSinceLastUpdate;

    @Column(name = "order_channel")
    private String orderChannel;

    @Column(name = "power_type")
    private String powerType;

    @Column(name = "lens_type")
    private String lensType;

    @Column(name = "country")
    private String country;

    @Column(name = "order_item_last_updated_at")
    private Date orderItemLastUpdatedAt;

    @Column(name = "order_item_created_at")
    private Date orderItemCreatedAt;

    @Column(name = "processing_type")
    private String processingType;

    @Column(name = "item_type")
    private String itemType;

    @Column(name = "barcode")
    private String barcode;

    @Column(name = "location_id")
    private String trayId;

    @Column(name = "manifest_no")
    private String manifestNo;
    @Column(name = "awb_number")
    private String awbNumber;
    @Column(name = "is_jit")
    private Boolean isJit;

    @Column(name = "is_true_last_piece")
    private Boolean isTrueLastPiece;

    @Column(name = "qc_status")
    private String qcStatus;

    @Column(name = "ship_to_customer")
    private Boolean shipToCustomer;

    @Column(name = "payment_not_captured")
    private Boolean paymentNotCaptured;

    @Column(name = "exchange_flag")
    private Boolean exchangeFlag;

    @Column(name = "power_follow_up_flag")
    private Boolean powerFollowUpFlag;

    @Column(name = "vsm_hold_flag")
    private Boolean vsmHoldFlag;

    @Column(name = "lens_package_type")
    private String lensPackageType;

    @Column(name = "associate_flag")
    private String associateFlag;

    @Column(name = "is_fulfillable")
    private Boolean isFulfillable;

    @Column(name = "facility_code")
    private String facilityCode;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at")
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "group_type")
    private String groupType;
    @Column(name = "isAsrsOrder")
    private Boolean isAsrsOrder;

    //ToDo: Add error_code column
    @Column(name = "error_type")
    @Enumerated(EnumType.STRING)
    private ErrorType errorType;

    @Column(name = "group_id")
    private String groupId;

    @Column(name = "group_effective_status")
    private String groupEffectiveStatus;

    @Column(name = "effective_group_update_time")
    private Date effectiveGroupUpdateTime;

    @Column(name = "v3_fr_tag")
    private String v3FrTag;
    @Column(name = "jit_status")
    private String jitStatus;
    @Column(name = "jit_group_status")
    private String jitGroupStatus;
    @Column(name = "jit_type")
    private String jitType;
    @Column(name = "picking_priority")
    private Integer pickingPriority;
    @Column(name = "is_allocated")
    private Boolean isAllocated;
    @Column(name = "courier_code")
    private String courierCode;
    @Column(name = "blank_pid")
    private String blankPid;
    @Column(name = "is_super_order")
    private Boolean isSuperOrder;

    @Column(name = "invoice_number")
    private String invoiceNumber;
    @Column(name = "lens_only_order")
    private Boolean isLensOnlyOrder;
    @Column(name = "is_mp_order")
    private boolean isMarketPlaceOrder;

    @Column(name = "is_owndays_order")
    private boolean isOwndaysOrder;

    @Column(name = "is_international")
    private boolean isInternationalOrder;
}
