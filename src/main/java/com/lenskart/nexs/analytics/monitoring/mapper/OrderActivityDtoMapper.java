package com.lenskart.nexs.analytics.monitoring.mapper;


import com.lenskart.nexs.activity.entities.OrderActivityEvent;
import com.lenskart.nexs.analytics.monitoring.pojo.OrderActivityEventDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OrderActivityDtoMapper {

    OrderActivityDtoMapper ORDER_ACTIVITY_DTO_MAPPER = Mappers.getMapper(OrderActivityDtoMapper.class);
    List<OrderActivityEventDto> mapOrderActivityEvents(List<OrderActivityEvent> orderActivityEventList);
}
