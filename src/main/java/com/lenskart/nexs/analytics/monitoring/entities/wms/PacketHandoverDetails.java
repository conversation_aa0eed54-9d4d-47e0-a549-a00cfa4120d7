package com.lenskart.nexs.analytics.monitoring.entities.wms;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Data
@Table(name = "packet_handover_details")
@JsonSerialize
public class PacketHandoverDetails {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", nullable = false, unique = true)
    private Long id;

    @Column(name = "packet_id", nullable = false, unique = true)
    private String packetId;

    @Column(name = "shipment_id")
    private Long shipmentId;

    @Column(name = "courier_code")
    private String courierCode;

    @Column(name = "allocation_status")
    private int allocationStatus;

    @Column(name = "shipment_type", nullable = false)
    private String shipmentType;

    @Column(name = "is_eligible_for_consolidation")
    private boolean isEligibleForConsolidation;

    @Column(name = "assigned_at")
    private Date assignedAt;

    @Column(name = "created_at")
    @CreatedDate
    private Date createdAt;
}

