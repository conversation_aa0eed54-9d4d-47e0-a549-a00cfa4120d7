package com.lenskart.nexs.analytics.monitoring.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.analytics.monitoring.pojo.DetailPageDto;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetailPageResponse {
    private int count;
    private long total;
    List<DetailPageDto> data;
    private String reportAsOn;
}
