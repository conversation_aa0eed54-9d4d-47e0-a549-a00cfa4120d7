package com.lenskart.nexs.analytics.monitoring.processor;

import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.processor.impl.JitOrderStatusDetailsProcessor;
import com.lenskart.nexs.analytics.monitoring.processor.impl.OrderItemsProcessor;
import com.lenskart.nexs.analytics.monitoring.processor.impl.PickingDetailOrderActivityProcessor;
import com.lenskart.nexs.analytics.monitoring.processor.impl.PicklistOrderItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MonitorPanelProcessorFactory {
    @Autowired
    MonitorPanelExecutor monitorPanelExecutor;

    @Value("#{'${nexs.terminal.status}'.split(',')}")
    private List<String> terminalStatusList;

    public MonitorPanelAbstractProcessor getEventProcessor(DebeziumMessage debeziumMessage){
        if(Constants.ORDER_ITEMS_TABLE.equalsIgnoreCase(debeziumMessage.getPayload().getSource().getTable())){
            return new OrderItemsProcessor();
        }else if (Constants.PICKLIST_ORDER_ITEMS_TABLE.equalsIgnoreCase(debeziumMessage.getPayload().getSource().getTable())){
            return new PicklistOrderItemProcessor();
        } else if (Constants.PICKING_DETAIL_TABLE.equalsIgnoreCase(debeziumMessage.getPayload().getSource().getTable())) {
            return new PickingDetailOrderActivityProcessor();
        }
        return new JitOrderStatusDetailsProcessor();
    }

    public List<String> getTerminalStatusList() {
        return terminalStatusList;
    }
}
