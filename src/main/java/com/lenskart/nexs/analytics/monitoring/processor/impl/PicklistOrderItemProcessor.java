package com.lenskart.nexs.analytics.monitoring.processor.impl;

import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.dto.PicklistOrderItemDto;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class PicklistOrderItemProcessor extends MonitorPanelAbstractProcessor {
    @Override
    protected RequestPayload doValidate(RequestPayload requestPayload) throws Exception {

        if(ObjectUtils.isNotEmpty(requestPayload.getPicklistOrderItem())){
            return requestPayload;
        }else {
            log.error("[PicklistOrderItemProcessor] : Invalid picklist order item status details");
            throw new Exception("Exception caught while validating picklist order item event message");
        }

    }

    @Override
    protected RequestPayload prepareEventMessage(DebeziumMessage debeziumMessage) throws Exception {
        log.debug("[PicklistOrderItemEvent] : debizium message received is : {}", debeziumMessage );
        RequestPayload requestPayload = new RequestPayload();
        try {
            if (null != debeziumMessage && Constants.PICKLIST_ORDER_ITEMS_TABLE.equalsIgnoreCase(debeziumMessage.getPayload().getSource().getTable())
                    && debeziumMessage.getPayload().getBefore() != debeziumMessage.getPayload().getAfter()) {
                String text = super.gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                PicklistOrderItemDto picklistOrderItemDto = ObjectHelper.getObjectMapper().readValue(text,PicklistOrderItemDto.class);
                requestPayload.setOrderItemId(picklistOrderItemDto.getScmOrderItemId());
                QueryType queryType = super.getQueryType(picklistOrderItemDto.getOrderState());
                requestPayload.setQueryType(queryType);
                requestPayload.setShippingPackageId(picklistOrderItemDto.getShipmentId());
                requestPayload.setWmsOrderCode(picklistOrderItemDto.getWmsOrderCode());
                requestPayload.setStatus(picklistOrderItemDto.getOrderState());
                requestPayload.setPicklistOrderItem(picklistOrderItemDto);
            }
        }catch (Exception e){
            log.error("Exception caught while creating picklist order item event with error : "+ e);
            throw new Exception("Event message conversion to picklist order item failed with exception : "+ e);
        }
        log.info("PicklistOrderItemEvent.payload generated is : {}", requestPayload.getOrderItemId());
        return requestPayload;
    }

    @Override
    protected void executePopulateMonitorPanelData(RequestPayload requestPayload) throws Exception {
        super.executor.doExecute(requestPayload, requestPayload.getQueryType());
    }
}

