package com.lenskart.nexs.analytics.monitoring.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Component
@Slf4j
public class KafkaProducerUtils {

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Value("${monitor.panel.kafka.timeout:2000}")
    private long kafkaTimeOut;

    public void sendAndGet(String orderItemTemTopic, String key, String message) throws Exception {
        try {
            kafkaProducerTemplate.send(orderItemTemTopic, key, message).get(kafkaTimeOut, TimeUnit.MICROSECONDS);
        } catch (InterruptedException e) {
            log.error("sendAndGet InterruptedException error: {}",e.getMessage(),e);
            throw e;
        } catch (ExecutionException e) {
            log.error("sendAndGet ExecutionException error: {}",e.getMessage(),e);
            throw e;
        } catch (TimeoutException e) {
            log.error("sendAndGet TimeoutException error: {}",e.getMessage(),e);
            throw e;
        } catch (Exception e){
            log.error("sendAndGet exception error: {}",e.getMessage(),e);
            throw e;
        }
    }
}
