package com.lenskart.nexs.analytics.monitoring.repository.wms;


import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import org.joda.time.DateTime;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import javax.persistence.Tuple;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;


public interface OrderItemRepository extends CrudRepository<OrderItems, Integer> , JpaSpecificationExecutor<OrderItems>, PagingAndSortingRepository<OrderItems,Integer> {
    @Query("select o from OrderItems o where o.status in ?1 and o.createdAt > ?2 and o.id <= ?3")
    List<OrderItems> findByStatusInAndCreatedAtGreaterThanAndIdLessThanEqual(Collection<String> statuses, Date createdAt, long id);

    @Query(value = "SELECT  concat(processing_type, ':::', item_type)\n" +
            "         as 'tag',status,\n" +
            "       count(*) as 'totalCount' FROM wms.order_items where status not in ?1  and fulfilled_type =?2 and processing_type is not null and  processing_type!='' and facility_code =?3 group by  status,processing_type,item_type ;", nativeQuery = true)
    List<Tuple> findOrderItemCountByFRTag(List<String> unwntedStatusList,int fullfillable, String facilityCode);

    OrderItems findByOrderItemId(Integer orderItemId);

    @Query(value = "select * from wms.order_items where order_item_id = ?1", nativeQuery = true)
    OrderItems getOrderItemsByOrderItemId(Integer orderItemId);

    @Query(value = "SELECT max(id) FROM OrderItems")
    long getMaxId();

    List<OrderItems> findAllByStatusInAndIdLessThanEqual(List<String> unwantedStatusList,long maxid, Pageable pageable);

//    @Query(value = "SELECT * FROM wms.order_items WHERE status IN ?1 AND id <= ?2 AND created_at >= NOW() - INTERVAL ?3 DAY;", nativeQuery = true)
    List<OrderItems> findAllByStatusInAndIdLessThanEqualAndCreatedAtGreaterThanEqual(List<String> unwantedStatusList, long maxid, Date interval, Pageable pageable);

    List<OrderItems> findAllByShippingPackageId(String shippingPackageId);
    List<OrderItems> findAllByShippingPackageIdIn(List<String> shippingPackageIds);
}
