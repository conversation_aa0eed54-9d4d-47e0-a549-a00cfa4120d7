package com.lenskart.nexs.analytics.monitoring.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.analytics.model.request.MonitorPanelFilters;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomePageRequest {
    MonitorPanelFilters monitorPanelFilters;
    CustomRangeFilter customRangeFilter;
}
