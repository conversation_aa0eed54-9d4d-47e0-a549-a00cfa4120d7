package com.lenskart.nexs.analytics.monitoring.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.wms.constants.ErrorType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MonitorPanelNewDto {
    private Integer increment_id;
    private String shipping_package_id;
    private String wms_status;
    private String mp_status;
    private String fr_tag;
    private Integer fitting_id;
    private Integer order_item_id;
    private Integer product_id;
    private String wms_order_code;
    private float ageing_since_created;
    private float ageing_since_last_update;
    private String order_channel;
    private String power_type;
    private String lens_type;
    private String country;
    private Date order_item_last_updated_at;
    private Date order_item_created_at;
    private String processing_type;
    private String item_type;
    private String barcode;
    private String location_id;
    private String manifest_no;
    private String awb_number;
    private Boolean is_jit;
    private Boolean is_true_last_piece;
    private String qc_status;
    private Boolean ship_to_customer;
    private Boolean payment_not_captured;
    private Boolean exchange_flag;
    private Boolean power_follow_up_flag;
    private Boolean vsm_hold_flag;
    private String lens_package_type;
    private String associate_flag;
    private Boolean is_fulfillable;
    private String facility_code;
    private Date created_at;
    private Date updated_at;
    private String group_type;
    private Boolean isAsrsOrder;
    private ErrorType error_type;
    private String group_id;
    private String group_effective_status;
    private Date effective_group_update_time;
    private String v3_fr_tag;
    private String jit_status;
    private String jit_group_status;
    private String jit_type;
    private Integer picking_priority;
    private Boolean is_allocated;
    private String courier_code;
    private String blank_pid;

}
