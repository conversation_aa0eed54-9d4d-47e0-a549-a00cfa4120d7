package com.lenskart.nexs.analytics.monitoring.service;

import com.lenskart.nexs.analytics.model.request.MonitorPanelFilters;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.MonitorPanelDataCoulmnNameConstants;
import com.lenskart.nexs.analytics.monitoring.request.CustomRangeFilter;
import com.lenskart.nexs.analytics.monitoring.request.DetailPageRequest;
import com.lenskart.nexs.analytics.monitoring.request.HomePageRequest;
import com.lenskart.nexs.wms.constants.ErrorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MonitorPanelDataReadAccessService<T> {

    @Value("${monitor.panel.slave.db.view.enabled:true}")
    private Boolean isSlaveDbViewEnabled;

    @Autowired
    @Qualifier("monitorPanelDataEntityManagerFactory")
    LocalContainerEntityManagerFactoryBean monitorPanelDataEntityManagerFactory;

    @Autowired
    @Qualifier("monitorPanelDataEntityReadManagerFactory")
    LocalContainerEntityManagerFactoryBean monitorPanelDataEntityReadManagerFactory;



    public List<Tuple> getMonitorPanelHomePageDataSpecV3(HomePageRequest homePageRequest, int isFulfillable, Class monitorPanelDataClass, String facilityCode) throws Exception {
        EntityManager entityManager;
        if(isSlaveDbViewEnabled){
            log.info("isSlaveDbViewEnabled : {}",isSlaveDbViewEnabled);
            entityManager = monitorPanelDataEntityReadManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        else {
            entityManager = monitorPanelDataEntityManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        log.info("getMonitorPanelHomePageDataSpecV3 EntityManager: {}",entityManager);
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cr = cb.createQuery(Tuple.class);
        Root<T> root = cr.from(monitorPanelDataClass);
        cr.multiselect((root.get( MonitorPanelDataCoulmnNameConstants.V3_FR_TAG )).alias(Constants.LISTING_PAGE_TAG),
                (root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)),
                (root.get(MonitorPanelDataCoulmnNameConstants.GROUP_EFFECTIVE_STATUS)).alias(Constants.LISTING_PAGE_STATUS),
                cb.countDistinct(root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)).alias(Constants.LISTING_PAGE_TOTAL_COUNT));
        cr.groupBy((root.get( MonitorPanelDataCoulmnNameConstants.V3_FR_TAG )),(root.get( MonitorPanelDataCoulmnNameConstants.GROUP_EFFECTIVE_STATUS)), (root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)));
        List<Predicate> predicates = new ArrayList<>();
        if(homePageRequest.getMonitorPanelFilters() != null) {
            predicates = getMonitorPanelFilters(homePageRequest.getMonitorPanelFilters(), homePageRequest.getCustomRangeFilter(), cb, root);
        }
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE ),isFulfillable));
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.FACILITY_CODE ),facilityCode));
        cr.where(cb.and(predicates.toArray(new Predicate[0])));
        log.info("creating the query for monitor panel data....: ");
        TypedQuery<Tuple> query = entityManager.createQuery(cr);
        log.info("exiting home page request v3 detail page spec, returning the response list");
        return query.getResultList();
    }


    public List<Tuple> getMonitorPanelHomePageDataSpecV3SeverityFilters(Class monitorPanelDataClass, int isFulfillable, String facilityCode, HomePageRequest homePageRequest) throws Exception {
        EntityManager entityManager;
        if(isSlaveDbViewEnabled){
            log.info("isSlaveDbViewEnabled : {}",isSlaveDbViewEnabled);
            entityManager = monitorPanelDataEntityReadManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        else {
            entityManager = monitorPanelDataEntityManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cr = cb.createQuery(Tuple.class);
        Root<T> root = cr.from(monitorPanelDataClass);
        cr.multiselect((root.get( MonitorPanelDataCoulmnNameConstants.V3_FR_TAG )).alias(Constants.LISTING_PAGE_TAG),
                (root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)),
                (root.get(MonitorPanelDataCoulmnNameConstants.GROUP_EFFECTIVE_STATUS)).alias(Constants.LISTING_PAGE_STATUS),
                cb.countDistinct(root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)).alias(Constants.LISTING_PAGE_TOTAL_COUNT));
        cr.groupBy((root.get( MonitorPanelDataCoulmnNameConstants.V3_FR_TAG )),(root.get( MonitorPanelDataCoulmnNameConstants.GROUP_EFFECTIVE_STATUS)), (root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)));
        List<Predicate> predicates = new ArrayList<>();
        if(homePageRequest.getMonitorPanelFilters() != null) {
            predicates = getMonitorPanelFilters(homePageRequest.getMonitorPanelFilters(), homePageRequest.getCustomRangeFilter(), cb, root);
        }
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE ),isFulfillable));
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.FACILITY_CODE ),facilityCode));
        cr.where(cb.and(predicates.toArray(new Predicate[0])));
        TypedQuery<Tuple> query = entityManager.createQuery(cr);
        log.info("returning the list of critical or severe order items...");
        return query.getResultList();
    }

    public List<Tuple> getJitMonitorPanelHomePage(HomePageRequest homePageRequest, String facilityCode, Class monitorPanelData, int isJit) throws Exception {
        EntityManager entityManager;
        if(isSlaveDbViewEnabled){
            log.info("isSlaveDbViewEnabled : {}",isSlaveDbViewEnabled);
            entityManager = monitorPanelDataEntityReadManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        else {
            entityManager = monitorPanelDataEntityManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cr = cb.createQuery(Tuple.class);
        Root<T> root = cr.from(monitorPanelData);
        cr.multiselect((root.get( MonitorPanelDataCoulmnNameConstants.JIT_TYPE)).alias(MonitorPanelDataCoulmnNameConstants.JIT_TYPE),
                (root.get(MonitorPanelDataCoulmnNameConstants.JIT_GROUP_STATUS)).alias(Constants.LISTING_PAGE_STATUS),
                cb.countDistinct(root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)).alias(Constants.LISTING_PAGE_TOTAL_COUNT));
        cr.groupBy((root.get( MonitorPanelDataCoulmnNameConstants.JIT_TYPE)),(root.get( MonitorPanelDataCoulmnNameConstants.JIT_GROUP_STATUS )));
        List<Predicate> predicates = new ArrayList<>();
        if(homePageRequest.getMonitorPanelFilters() != null) {
            predicates = getMonitorPanelFilters(homePageRequest.getMonitorPanelFilters(), homePageRequest.getCustomRangeFilter(), cb, root);
        }
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.IS_JIT ),isJit));
        predicates.add(root.get(MonitorPanelDataCoulmnNameConstants.JIT_GROUP_STATUS).isNotNull());
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.FACILITY_CODE ),facilityCode));
        cr.where(cb.and(predicates.toArray(new Predicate[0])));
        log.info("creating the query for monitor panel data....");
        TypedQuery<Tuple> query = entityManager.createQuery(cr);
//        log.info("exiting home page request v3 detail page spec, returning the response list");
        return query.getResultList();
    }

    public List<Tuple> getMonitorPanelHomePageDataSpec(HomePageRequest homePageRequest, int isFulfillable, Class monitorPanelDataClass, String facilityCode) throws Exception {
        EntityManager entityManager;
        if(isSlaveDbViewEnabled){
            log.info("isSlaveDbViewEnabled : {}",isSlaveDbViewEnabled);
            entityManager = monitorPanelDataEntityReadManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        else {
            entityManager = monitorPanelDataEntityManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cr = cb.createQuery(Tuple.class);
        Root<T> root = cr.from(monitorPanelDataClass);
        cr.multiselect((root.get( MonitorPanelDataCoulmnNameConstants.FR_TAG )).alias(Constants.LISTING_PAGE_TAG),
                (root.get( MonitorPanelDataCoulmnNameConstants.MP_STATUS )).alias(Constants.LISTING_PAGE_STATUS),
                cb.count(root).alias(Constants.LISTING_PAGE_TOTAL_COUNT));
        cr.groupBy((root.get( MonitorPanelDataCoulmnNameConstants.FR_TAG )),(root.get( MonitorPanelDataCoulmnNameConstants.MP_STATUS )));
        List<Predicate> predicates = new ArrayList<>();
        if(homePageRequest.getMonitorPanelFilters() != null) {
            predicates = getMonitorPanelFilters(homePageRequest.getMonitorPanelFilters(), homePageRequest.getCustomRangeFilter(), cb, root);
        }
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE ),isFulfillable));
        predicates.add(cb.equal(root.get( MonitorPanelDataCoulmnNameConstants.FACILITY_CODE ),facilityCode));
        cr.where(cb.and(predicates.toArray(new Predicate[0])));
        TypedQuery<Tuple> query = entityManager.createQuery(cr);
        log.info("The query is {}",query);
        log.info("The query result is {}",query.getResultList());
        return query.getResultList();
    }

    public List<Predicate> getMonitorPanelFilters(MonitorPanelFilters monitorPanelFilters, CustomRangeFilter customRangeFilter, CriteriaBuilder criteriaBuilder, Root<T> root) throws Exception {
        List<Predicate> predicates = new ArrayList<>();
        if(monitorPanelFilters.getBinaryFilter() != null) {
            for (Map.Entry<String, Boolean> binaryFilter : monitorPanelFilters.getBinaryFilter().entrySet()) {
                if(Constants.binaryFiltersList.contains(binaryFilter.getKey())) {
                    predicates.add(criteriaBuilder.equal(root.get(binaryFilter.getKey()), binaryFilter.getValue()));
                } else{
                    log.error("Incorrect filter name: {} - {}", binaryFilter.getKey(), monitorPanelFilters);
                }
            }
        }

        if(monitorPanelFilters.getMultiSelectFilters() != null) {
            for (Map.Entry<String, List<String>> multiSelectFilter : monitorPanelFilters.getMultiSelectFilters().entrySet()) {
                if(Constants.multiSelectFiltersList.contains(multiSelectFilter.getKey()) && multiSelectFilter.getValue() != null && !multiSelectFilter.getValue().isEmpty()) {
                    if(multiSelectFilter.getKey().equalsIgnoreCase("pickingPriority")){
                        List<Integer>  priorityList = multiSelectFilter.getValue().stream().map(Integer::parseInt).collect(Collectors.toList());
                        predicates.add(criteriaBuilder.in(root.get(multiSelectFilter.getKey())).value(priorityList));
                    }else{
                        predicates.add(criteriaBuilder.in(root.get(multiSelectFilter.getKey())).value(multiSelectFilter.getValue()));
                    }
                } else {
                    log.error("Incorrect filter name or empty filter: {} - {}", multiSelectFilter.getKey(), monitorPanelFilters);
                }
            }
        }

        if(monitorPanelFilters.getSingleSelectFilters() !=null) {
            for(Map.Entry<String, String> singleSelectFilter: monitorPanelFilters.getSingleSelectFilters().entrySet()) {
                if(Constants.singleSelectFilterList.contains(singleSelectFilter.getKey()) &&
                        singleSelectFilter.getValue()!=null) {
                    if(MonitorPanelDataCoulmnNameConstants.ERROR_TYPE.equalsIgnoreCase(singleSelectFilter.getKey()) && Constants.ERROR_TYPE.contains(singleSelectFilter.getValue()) ) {
                        log.info("The single select filter is error type and the value is {}",singleSelectFilter.getValue());
                        predicates.add(criteriaBuilder.equal(root.get(singleSelectFilter.getKey()),ErrorType.valueOf(singleSelectFilter.getValue())));
                    }
                    else {
                        log.error("The single select filter {} and value {} cannot be used",singleSelectFilter.getKey(), singleSelectFilter.getValue());
                    }
                }
                else {
                    log.error("The key {} or the value {} cannot be used for filters",singleSelectFilter.getKey(), singleSelectFilter.getValue());
                }
            }
        }

        //TO-DO changing the MonitorPanelRangeFilters type to map
        if(monitorPanelFilters.getMonitorPanelRangeFilters() != null) {
            if(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated() != null &&
                    !StringUtils.isBlank( monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getStartValue() ) &&
                    !StringUtils.isBlank( monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getEndValue() )) {

                if(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getEndValue().equals("-1")) {
                    monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().setEndValue(String.valueOf(Float.MAX_VALUE));
                }
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get( MonitorPanelDataCoulmnNameConstants.AGEING_SINCE_CREATED ),
                        Float.valueOf(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getStartValue())));
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get( MonitorPanelDataCoulmnNameConstants.AGEING_SINCE_CREATED ),
                        Float.valueOf(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getEndValue())));
            }
            if(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate() != null &&
                    !StringUtils.isBlank( monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate().getStartValue() ) &&
                    !StringUtils.isBlank( monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate().getEndValue() )) {

                if(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate().getEndValue().equals("-1")) {
                    monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate().setEndValue(String.valueOf(Float.MAX_VALUE));
                }
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get( MonitorPanelDataCoulmnNameConstants.AGEING_SINCE_LAST_UPDATE ),
                        Float.valueOf(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate().getStartValue())));
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get( MonitorPanelDataCoulmnNameConstants.AGEING_SINCE_LAST_UPDATE ),
                        Float.valueOf(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceLastUpdate().getEndValue())));

            }
            if(monitorPanelFilters.getMonitorPanelRangeFilters().getDate() != null &&
                    !StringUtils.isBlank( monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue() ) &&
                    !StringUtils.isBlank( monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue() )) {

                SimpleDateFormat dateFormatter = new SimpleDateFormat( Constants.DATE_FILTER_FORMAT );
                Date startDate = dateFormatter.parse(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue());
                Date endDate = dateFormatter.parse(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());

                predicates.add(criteriaBuilder.between(root.get( MonitorPanelDataCoulmnNameConstants.ORDER_ITEM_CREATED_AT ),
                        startDate, endDate));

            }

            if(customRangeFilter != null &&
                    !StringUtils.isBlank(customRangeFilter.getStartValue()) &&
                    !StringUtils.isBlank(customRangeFilter.getEndValue())){
                SimpleDateFormat dateFormatter = new SimpleDateFormat( Constants.DATE_FILTER_FORMAT );
                Date startDate = dateFormatter.parse(customRangeFilter.getStartValue());
                Date endDate = dateFormatter.parse(customRangeFilter.getEndValue());
                predicates.add(criteriaBuilder.between(root.get( MonitorPanelDataCoulmnNameConstants.ORDER_ITEM_CREATED_AT ),
                        startDate, endDate));
            }

            if(monitorPanelFilters.getProductDetailsFilter() != null &&
                    ObjectUtils.isNotEmpty(monitorPanelFilters.getProductDetailsFilter().getProductId())){
                predicates.add(criteriaBuilder.equal(root.get(MonitorPanelDataCoulmnNameConstants.PRODUCT_ID), monitorPanelFilters.getProductDetailsFilter().getProductId()));
            }
        }
        log.info("range filters for monitor panel data received....");
        return predicates;
    }

    public Specification<T> getMonitorPanelDetailPageSpec(DetailPageRequest detailPageRequest, int fulfillability, String facilityCode) throws Exception{
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.FR_TAG ), detailPageRequest.getFrTag()));
            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.MP_STATUS), detailPageRequest.getStatus()));
            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE ),fulfillability));
            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.FACILITY_CODE ),facilityCode));

            if(detailPageRequest.getMonitorPanelFilters() != null) {
                try {
                    predicates.addAll(getMonitorPanelFilters(detailPageRequest.getMonitorPanelFilters(), detailPageRequest.getCustomRangeFilter(), criteriaBuilder, root));
                } catch (Exception e) {
                    log.error("Parse exception in MonitorPanelFilters: {} - {}", detailPageRequest.getMonitorPanelFilters(), e.getMessage());
                }
            }
            query.orderBy(criteriaBuilder.desc(root.get("id")));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }


    public Specification<T> getMonitorPanelDetailPageSpecV3(DetailPageRequest detailPageRequest, int fulfillability, String facilityCode) throws Exception {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (ObjectUtils.isEmpty(detailPageRequest.getMonitorPanelFilters().getProductDetailsFilter())) {
                predicates.add(criteriaBuilder.equal(root.get(MonitorPanelDataCoulmnNameConstants.GROUP_EFFECTIVE_STATUS), detailPageRequest.getStatus()));
            }
            if(!detailPageRequest.getCategory().equalsIgnoreCase(Constants.TOTAL_KEY)){
                predicates.add(criteriaBuilder.equal(root.get(Constants.monitorPanelPredicates.get(detailPageRequest.getCategory())), fulfillability));
                if (detailPageRequest.getCategory().equalsIgnoreCase(Constants.FulfillableTypeV3.ALLOCATED_ORDERS.name())) {
                    predicates.add(criteriaBuilder.equal(root.get(MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE), 0));
                }
            }
            predicates.add(criteriaBuilder.in(root.get(MonitorPanelDataCoulmnNameConstants.V3_FR_TAG)).value(detailPageRequest.getMonitorPanelFilters().getProductDetailsFilter() != null ?
                    detailPageRequest.getMonitorPanelFilters().getProductDetailsFilter().getFrtagsList() : Arrays.asList(detailPageRequest.getFrTag())));

            predicates.add(criteriaBuilder.equal(root.get(MonitorPanelDataCoulmnNameConstants.FACILITY_CODE),
                    ObjectUtils.isNotEmpty(detailPageRequest.getMonitorPanelFilters().getProductDetailsFilter()) ?
                            detailPageRequest.getMonitorPanelFilters().getProductDetailsFilter().getFacilityCode() : facilityCode));


            if (detailPageRequest.getMonitorPanelFilters() != null) {
                try {
                    predicates.addAll(getMonitorPanelFilters(detailPageRequest.getMonitorPanelFilters(), detailPageRequest.getCustomRangeFilter(), criteriaBuilder, root));
                } catch (Exception e) {
                    log.error("Parse exception in MonitorPanelFilters: {} - {}", detailPageRequest.getMonitorPanelFilters(), e.getMessage());
                }
            }
            query.orderBy(criteriaBuilder.desc(root.get("id")));
            log.info("query created is :{} ", criteriaBuilder.createQuery().toString());
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public Specification<T> getJitMonitorPanelDetailPageSpec(DetailPageRequest detailPageRequest, boolean jitFlag, String facilityCode) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.JIT_TYPE), detailPageRequest.getFrTag()));
            predicates.add(criteriaBuilder.equal(root.get(MonitorPanelDataCoulmnNameConstants.JIT_GROUP_STATUS), detailPageRequest.getStatus()));
            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.IS_JIT ),jitFlag));
            predicates.add(criteriaBuilder.equal(root.get( MonitorPanelDataCoulmnNameConstants.FACILITY_CODE ),facilityCode));
            if(detailPageRequest.getMonitorPanelFilters() != null) {
                try {
                    predicates.addAll(getMonitorPanelFilters(detailPageRequest.getMonitorPanelFilters(), detailPageRequest.getCustomRangeFilter(), criteriaBuilder, root));
                } catch (Exception e) {
                    log.error("Parse exception in MonitorPanelFilters: {} - {}", detailPageRequest.getMonitorPanelFilters(), e.getMessage());
                }
            }
            query.orderBy(criteriaBuilder.desc(root.get("id")));
            log.info("query created is :{} ", criteriaBuilder.createQuery().toString());
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public List<Tuple> getMonitorPanelSummaryDataSepc(Class monitorPanelDataClass, int isFulfillable, String facilityCode, HomePageRequest homePageRequest) throws Exception {
        EntityManager entityManager;
        if (isSlaveDbViewEnabled) {
            log.info("isSlaveDbViewEnabled : {}", isSlaveDbViewEnabled);
            entityManager = monitorPanelDataEntityReadManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        } else {
            entityManager = monitorPanelDataEntityManagerFactory.getNativeEntityManagerFactory().createEntityManager();
        }
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> cr = cb.createQuery(Tuple.class);
        Root<T> root = cr.from(monitorPanelDataClass);
        cr.multiselect((root.get(MonitorPanelDataCoulmnNameConstants.V3_FR_TAG)).alias(Constants.LISTING_PAGE_TAG),
                (root.get(MonitorPanelDataCoulmnNameConstants.PICKING_PRIORITY)).alias(Constants.PICKING_PRIORITY),
                cb.countDistinct(root.get(MonitorPanelDataCoulmnNameConstants.GROUP_ID)).alias(Constants.LISTING_PAGE_TOTAL_COUNT));
        cr.groupBy((root.get(MonitorPanelDataCoulmnNameConstants.V3_FR_TAG)), (root.get(MonitorPanelDataCoulmnNameConstants.PICKING_PRIORITY)));
        List<Predicate> predicates = new ArrayList<>();
        if (homePageRequest.getMonitorPanelFilters() != null) {
            predicates = getMonitorPanelFilters(homePageRequest.getMonitorPanelFilters(), homePageRequest.getCustomRangeFilter(), cb, root);
        }
        predicates.add(cb.equal(root.get(MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE), isFulfillable));
        predicates.add(cb.equal(root.get(MonitorPanelDataCoulmnNameConstants.FACILITY_CODE), facilityCode));
        cr.where(cb.and(predicates.toArray(new Predicate[0])));
        TypedQuery<Tuple> query = entityManager.createQuery(cr);
        return query.getResultList();
    }

}
