package com.lenskart.nexs.analytics.monitoring.response;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.math.BigInteger;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductDetailsPageResponse {
    private BigInteger fulfillable;
    private BigInteger unfulfillable;
    private BigInteger allocated;
    private String facilityCode;
    private BigInteger total;
}
