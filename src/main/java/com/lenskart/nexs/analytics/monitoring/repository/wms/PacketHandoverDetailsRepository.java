package com.lenskart.nexs.analytics.monitoring.repository.wms;


import com.lenskart.nexs.analytics.monitoring.entities.wms.PacketHandoverDetails;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface PacketHandoverDetailsRepository extends CrudRepository<PacketHandoverDetails, Integer>, JpaSpecificationExecutor<PacketHandoverDetails>, PagingAndSortingRepository<PacketHandoverDetails,Integer> {
    @Query(value = "select courier_code from wms.packet_handover_details where packet_id = ?1 and is_eligible_for_consolidation is true and shipment_id is null;", nativeQuery = true)
    Optional<String> getCourierByPackageId(String shippingPackageId);

    @Query(value = "select * from wms.packet_handover_details where packet_id IN :shipments and is_eligible_for_consolidation is true and shipment_id is null;", nativeQuery = true)
    List<PacketHandoverDetails> getCourierByPacketList(@Param("shipments") List<String> shippingPackageIds);
}
