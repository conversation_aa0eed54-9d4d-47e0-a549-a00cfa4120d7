package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = SqlTableConstants.SHIPMENTS)
public class ShipmentDetails extends BaseEntity {


    @Column(name ="shipping_package_id", columnDefinition = "varchar(30)")
    private String shippingPackageId;

    @Column(name ="shipment_partner_code")
    private Integer courier_id;

    @Column(name ="invoice_id")
    private Long invoice_id;

    @Column(name ="dispatch_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dispatchDate;

    @Column(name ="delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date deliveryDate;

    @Column(name ="expected_delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expectedDeliveryDate;

    @Column(name ="facility_code")
    private String facilityCode;

    @Column(name ="status")
    private String status;

    @Version
    private Integer version;

    @Column(name ="manifest_id")
    private String manifestId;
}
