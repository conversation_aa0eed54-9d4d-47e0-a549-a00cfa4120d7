package com.lenskart.nexs.analytics.monitoring.pojo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderActivityEventDto {

    @JsonProperty(value="wmsOrderCode",index=1)
    private String referenceId;
    @JsonProperty(value="eventName",index=2)
    private String event;
    @JsonProperty(value="eventMessage",index=3)
    private String message;
    @JsonProperty(value = "facilityCode", index = 4)
    private String facilityCode;
    @JsonProperty(value = "itemUpdatedAt", index = 5)
    private Date itemEventUpdatedAt;
    @JsonProperty(value = "eventUpdatedAt", index = 6)
    private Date createdAt;
    @JsonProperty(value = "eventUpdatedBy", index = 7)
    private String updatedBy;

}
