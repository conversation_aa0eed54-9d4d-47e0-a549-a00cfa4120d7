package com.lenskart.nexs.analytics.monitoring.entities.monitorpanel;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Setter
@Getter
@Entity
@ToString(callSuper=true)
@Table(name = "order_data_snapshot1")
public class MonitorPanelData1 extends MonitorPanelData {
}
