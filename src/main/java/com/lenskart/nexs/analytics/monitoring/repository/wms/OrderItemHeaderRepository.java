package com.lenskart.nexs.analytics.monitoring.repository.wms;

import com.lenskart.nexs.analytics.monitoring.entities.wms.Orders;
import org.springframework.data.jpa.repository.JpaRepository;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemHeader;
import org.springframework.stereotype.Repository;


@Repository
public interface OrderItemHeaderRepository extends JpaRepository<OrderItemHeader,Integer > {

    public OrderItemHeader findByWmsOrderCodeAndEnabled(String wmsOrderCode,  boolean enabled);
}
