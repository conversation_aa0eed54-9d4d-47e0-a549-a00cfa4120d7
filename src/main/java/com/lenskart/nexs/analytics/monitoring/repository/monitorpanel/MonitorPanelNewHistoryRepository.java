package com.lenskart.nexs.analytics.monitoring.repository.monitorpanel;

import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNewHistory;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface MonitorPanelNewHistoryRepository extends CrudRepository<MonitorPanelNewHistory, Long>, JpaSpecificationExecutor<MonitorPanelNewHistory>, PagingAndSortingRepository<MonitorPanelNewHistory,Long> {
}
