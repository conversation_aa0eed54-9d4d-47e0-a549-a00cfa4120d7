package com.lenskart.nexs.analytics.monitoring.service;

import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.repository.wms.OrderItemRepository;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.Predicate;

@Service
public class DataAccessService {

    @Autowired
    private OrderItemRepository orderItemRepository;

    public Specification<OrderItems> getOrderItemsSpec(List<Pair<String,String>> frtagList, List<String> statusList, int fulfillability, String facilityCode) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            List<Predicate> tagPredicates = new ArrayList<>();

            for(Pair tagPair: frtagList){
                tagPredicates.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("processingType"),tagPair.getLeft()),criteriaBuilder.equal(root.get("itemType"),tagPair.getRight())));

            }
           // predicates.addAll(tagPredicates);
            predicates.add(criteriaBuilder.in(root.get("status")).value(statusList));
            predicates.add(criteriaBuilder.equal(root.get("fulfillableType"),fulfillability));
            predicates.add(criteriaBuilder.equal(root.get("facilityCode"), facilityCode));
            predicates.add(criteriaBuilder.or(tagPredicates.toArray(new Predicate[0])));
            query.orderBy(criteriaBuilder.desc(root.get("id")));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public List<OrderItems> getOrderItems(List<Pair<String,String>> taglist,List<String> statusList,int fulfillability,String facilityCode,int page,int size){
        Pageable pageable = PageRequest.of(page, size);
        List<OrderItems> items= orderItemRepository.findAll(getOrderItemsSpec(taglist,statusList,fulfillability, facilityCode),pageable).getContent();
        return items;
    }

    public long getTotalCount(List<Pair<String,String>> taglist,List<String> statusList,int fulfillability,String facilityCode){
        long count= orderItemRepository.count(getOrderItemsSpec(taglist,statusList,fulfillability, facilityCode));
        return count;
    }
}
