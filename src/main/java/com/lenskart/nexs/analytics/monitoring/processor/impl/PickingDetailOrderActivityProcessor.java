package com.lenskart.nexs.analytics.monitoring.processor.impl;

import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.dto.PickingDetailDto;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Slf4j
@Component
public class PickingDetailOrderActivityProcessor extends MonitorPanelAbstractProcessor {
    @Override
    protected RequestPayload doValidate(RequestPayload requestPayload) throws Exception {
        log.debug("[OrderItemsOrderActivityProcessor] : doValidate,  payload received is : {}", requestPayload.getPickingDetailData().toString());
        if (ObjectUtils.isNotEmpty(requestPayload.getPickingDetailData())) {
            return requestPayload;
        } else {
            log.error("[OrderItemsOrderActivityProcessor] : Invalid order item status details");
            throw new Exception(" Exception caught while validating order item event message");
        }
    }

    @Override
    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    protected RequestPayload prepareEventMessage(DebeziumMessage debeziumMessage) throws Exception {
        RequestPayload requestPayload = new RequestPayload();
        String afterData = super.gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
        PickingDetailDto afterPickingDetail = ObjectHelper.getObjectMapper().readValue(afterData,PickingDetailDto.class);
        requestPayload.setPickingDetailData(afterPickingDetail);
        requestPayload.setShippingPackageId(afterPickingDetail.getShipmentId());
        requestPayload.setOrderItemId(afterPickingDetail.getWmsOrderItemId());
        requestPayload.setStatus("PICKING_SUMMARY");
        requestPayload.setWmsOrderCode(this.fetchWmsOrderCode(afterPickingDetail.getShipmentId()));
        requestPayload.setQueryType(QueryType.INSERT_ORDER_ACTIVITY);
        return requestPayload;
    }

    @Override
    protected void executePopulateMonitorPanelData(RequestPayload requestPayload) throws Exception {
        log.debug("[OrderItemsOrderActivityProcessor] : executePopulateMonitorPanelData,  calling db strategy for request : {}", requestPayload.getPickingDetailData());
        super.executor.doExecute(requestPayload, requestPayload.getQueryType());
    }

    private String fetchWmsOrderCode(String shippingPackageId){
        return super.orderItemsReporitory.findAllByShippingPackageId(shippingPackageId).get(0).getWmsOrderCode();
    }
}
