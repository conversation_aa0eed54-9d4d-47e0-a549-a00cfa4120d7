package com.lenskart.nexs.analytics.monitoring.service;

import com.lenskart.core.model.HubMaster;
import com.lenskart.fds.dto.DocumentDetailsDto;
import com.lenskart.nexs.analytics.dao.CacheDAO;
import com.lenskart.nexs.analytics.entity.wms.Invoice;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import com.lenskart.nexs.analytics.monitoring.entities.mongodb.ShipmentDetails;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData1;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData2;
import com.lenskart.nexs.analytics.monitoring.entities.picking.PicklistOrderItem;
import com.lenskart.nexs.analytics.monitoring.entities.wms.Address;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemHeader;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemMetaData;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.model.*;
import com.lenskart.nexs.analytics.monitoring.pojo.ShipmentDetailsDto;
import com.lenskart.nexs.analytics.monitoring.repository.jitdb.JitDbRepository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelData1Repository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelData2Repository;
import com.lenskart.nexs.analytics.monitoring.repository.picking.PicklistOrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.repository.scm.ShipmentDetailsRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.AddressRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.OrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.PacketHandoverDetailsRepository;
import com.lenskart.nexs.analytics.monitoring.utils.MonitorPanelUtils;
import com.lenskart.nexs.analytics.repository.inventory.HubMasterRepository;
import com.lenskart.nexs.analytics.repository.wms.InvoiceItemRepository;
import com.lenskart.nexs.analytics.repository.wms.InvoiceRepository;
import com.lenskart.nexs.analytics.service.imp.FdsServiceImpl;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.wms.enums.FulfillableType;
import com.lenskart.nexs.wms.enums.ItemType;
import com.lenskart.nexs.wms.enums.OrderItemStatus;
import com.lenskart.nexs.wms.enums.OrderItemType;
import com.lenskart.nexs.wms.enums.ProcessingType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MonitorPanelDataService {
    @Autowired
    private InvoiceRepository invoiceRepository;
    @Autowired
    private InvoiceItemRepository invoiceItemRepository;
    @Autowired
    PicklistOrderItemRepository picklistOrderItemRepository;
    @Autowired
    MonitorPanelUtils monitorPanelUtils;

    @Autowired
    MonitorPanelData1Repository monitorPanelData1Repository;

    @Autowired
    MonitorPanelData2Repository monitorPanelData2Repository;
    @Autowired
    OrderItemRepository orderItemRepository;

    @Autowired
    JitDbRepository jitDbRepository;
    @Autowired
    ShipmentDetailsRepository shipmentDetailsRepository;
    @Autowired
    MonitorPanelDataService monitorPanelDataService;

    @Autowired
    PacketHandoverDetailsRepository packetHandoverDetailsRepository;
    @Autowired
    FdsServiceImpl fdsService;

    @Autowired
    HubMasterRepository hubMasterRepository;

    @Autowired
    AddressRepository addressRepository;

    @Value("${nexs.monitor.mongo.query.enable:false}")
    private boolean isMongoDBQueryEnable;

    @Value("${nexs.monitor.jit.panel.data.process:false}")
    private boolean isJitPanelProcessEnable;

    @Value("${nexs.monitor.data.interval:90}")
    private int interval;

    @Value("${nexs.monitor.data.fetch.invoice}")
    private boolean fetchInvoice;

    @Value("#{'${nexs.owndays.channel.list}'.split(',')}")
    private List<String> owndaysOrderChannelList;

    @Autowired
    private CacheDAO cacheDAO;

    @Value("#{'${nexs.monitoring.invoice.eligible.status}'.split(',')}")
    List<String> invoiceEligibleStatus;

    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    public void populateMonitorPanelData(String redisKey,List<OrderItems> orderItemsList,Map<Integer, MonitorPanelPickingData> orderItemIdToPickingMap, Map<String,OrderItemData> groupStatusMap,List<Integer> jitOrderItemIds, Map<Integer, Integer> pickingPriorityMap) throws Exception {

        if(redisKey != null) {
            long startTime= System.currentTimeMillis();


            String[] tableNameAndUpdatedAtList = redisKey.split(Constants.DELIMITER);
            if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {
                log.info("populating order_data_snapshot2 table , redis key : {} , tableNameAndUpdatedAtList :{}.", redisKey, tableNameAndUpdatedAtList);
                List<MonitorPanelData2> monitorPanelData2List = getMonitorPanelData2ListFromOrderItemsList(orderItemsList, orderItemIdToPickingMap, groupStatusMap,jitOrderItemIds, pickingPriorityMap);
                monitorPanelData2Repository.saveAll(monitorPanelData2List);
                long timeTaken = System.currentTimeMillis() - startTime;
                log.info(" time taken in saving data ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
//                monitorPanelData1Repository.truncateTable();
                timeTaken=System.currentTimeMillis() - startTime;
                log.info(" time taken in deleting data ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
                log.info("populated order_data_snapshot2 table , redis key : {} , tableNameAndUpdatedAtList :{}.", redisKey, tableNameAndUpdatedAtList);
//                return Constants.MONITOR_TABLE_2 + Constants.DELIMITER + System.currentTimeMillis();
            } else {
                log.info("populating order_data_snapshot1 table , redis key : {} , tableNameAndUpdatedAtList :{}.", redisKey, tableNameAndUpdatedAtList);
                List<MonitorPanelData1> monitorPanelData1List = getMonitorPanelData1ListFromOrderItemsList(orderItemsList, orderItemIdToPickingMap, groupStatusMap,jitOrderItemIds, pickingPriorityMap);
                monitorPanelData1Repository.saveAll(monitorPanelData1List);
                long timeTaken = System.currentTimeMillis() - startTime;
                log.info(" time taken in saving data ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
//                monitorPanelData2Repository.truncateTable();
                timeTaken = System.currentTimeMillis() - startTime;
                log.info(" time taken in deleting data ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
                log.info("populated order_data_snapshot1 table , redis key : {} , tableNameAndUpdatedAtList :{}.", redisKey, tableNameAndUpdatedAtList);
//                return Constants.MONITOR_TABLE_1 + Constants.DELIMITER + System.currentTimeMillis();
            }
        } else {
            log.info("in else - populating order_data_snapshot1 table , redis key : {}. ", redisKey);
            List<MonitorPanelData1> monitorPanelData1List = getMonitorPanelData1ListFromOrderItemsList(orderItemsList, orderItemIdToPickingMap, groupStatusMap,jitOrderItemIds, pickingPriorityMap);
            monitorPanelData1Repository.saveAll(monitorPanelData1List);
//            monitorPanelData2Repository.truncateTable();
            log.info("in else - populated order_data_snapshot1 table , redis key : {}.", redisKey);
//            return Constants.MONITOR_TABLE_1 + Constants.DELIMITER + System.currentTimeMillis();
        }

    }
    public List<MonitorPanelData1> getMonitorPanelData1ListFromOrderItemsList(List<OrderItems> orderItemsList, Map<Integer, MonitorPanelPickingData> orderItemIdToPickingMap, Map<String,OrderItemData> groupStatusMap,List<Integer> jitOrderItemIds, Map<Integer,Integer> pickingPriorityMap) {
        List<MonitorPanelData1> monitorPanelData1List = new ArrayList<>();
        Map<String,JitOrderStatusDetails> jitOrderStatusDetailsMap = createJitOrderItemMap(jitOrderItemIds);
        Map<String,CourierDetails> courierDetailsMap = new HashMap<>();
        getCourierDetailsMap(orderItemsList, courierDetailsMap);
        log.info("jit order status map received : {}", jitOrderStatusDetailsMap);
        Map<String,JitOrderItem> jitOrderItemStatusMap = new HashMap<>();
        for(OrderItems orderItems : orderItemsList) {
            MonitorPanelData1 monitorPanelData1 = new MonitorPanelData1();
            if(createMonitorPanelDataFromOrderItem(orderItems, monitorPanelData1, orderItemIdToPickingMap ,groupStatusMap, jitOrderStatusDetailsMap,jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap)) {
                log.info("updating the values of the group status map for group id : {}",monitorPanelData1.getGroupId());
                monitorPanelData1List.add(monitorPanelData1);
            }
        }
        long startTime= System.currentTimeMillis();

        for(Map.Entry<String,OrderItemData> entry : groupStatusMap.entrySet()){
            deriveGroupEffectiveStatus(entry.getValue());
        }
        monitorPanelData1List=updateGroupEffectiveStatusForGroupIdTable1(groupStatusMap,monitorPanelData1List);
        monitorPanelData1List = updateJitGroupEffectiveStatusForTable1(jitOrderItemStatusMap,monitorPanelData1List);
        long timeTaken = System.currentTimeMillis() - startTime;
        log.info("updating group effective status ended  and time taken in ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
        return monitorPanelData1List;
    }

    public List<MonitorPanelData2> getMonitorPanelData2ListFromOrderItemsList(List<OrderItems> orderItemsList, Map<Integer, MonitorPanelPickingData> orderItemIdToPickingMap, Map<String,OrderItemData>groupStatusMap,List<Integer> jitOrderItemIds, Map<Integer,Integer> pickingPriorityMap) {
        List<MonitorPanelData2> monitorPanelData2List = new ArrayList<>();
        Map<String,JitOrderStatusDetails> jitOrderStatusDetailsMap = createJitOrderItemMap(jitOrderItemIds);
        Map<String,CourierDetails> courierDetailsMap = new HashMap<>();
        getCourierDetailsMap(orderItemsList, courierDetailsMap);
        log.info("jit order status map received : {}", jitOrderStatusDetailsMap);
        Map<String,JitOrderItem> jitOrderItemStatusMap = new HashMap<>();
        for(OrderItems orderItems : orderItemsList) {
            MonitorPanelData2 monitorPanelData2 = new MonitorPanelData2();
            if(createMonitorPanelDataFromOrderItem(orderItems, monitorPanelData2, orderItemIdToPickingMap, groupStatusMap, jitOrderStatusDetailsMap,jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap)) {
                log.info("updating the values of the group status map for group id : {}",monitorPanelData2.getGroupId());
                monitorPanelData2List.add(monitorPanelData2);
            }
        }
        long startTime= System.currentTimeMillis();

        for(Map.Entry<String,OrderItemData> entry : groupStatusMap.entrySet()){
            deriveGroupEffectiveStatus(entry.getValue());
        }
        monitorPanelData2List=updateGroupEffectiveStatusForGroupIdTable2(groupStatusMap,monitorPanelData2List);
        monitorPanelData2List = updateJitGroupEffectiveStatusForTable2(jitOrderItemStatusMap,monitorPanelData2List);
        long timeTaken = System.currentTimeMillis() - startTime;
        log.info("updating group effective status ended  and time taken in ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
        return monitorPanelData2List;
    }

    public Map<String, CourierDetails> getCourierDetailsMap(List<OrderItems> orderItemsList, Map<String,CourierDetails> courierDetailsMap){
        log.info("fetching courier details :{}",isMongoDBQueryEnable);
        try{
            if(isMongoDBQueryEnable) {
                List<String> shippingPackgeIds = orderItemsList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getShippingPackageId())).map(orderItems -> orderItems.getShippingPackageId()).distinct().collect(Collectors.toList());
                log.info("fetching courier details for shipment ids in list : {}", shippingPackgeIds);
                List<ShipmentDetails> shipmentDetailsList = shipmentDetailsRepository.findByShippingPackageIdIn(shippingPackgeIds);
                log.info("list of shipment details received is : {}", shipmentDetailsList);
                for (ShipmentDetails shipmentDetails : shipmentDetailsList) {
                    CourierDetails courierDetails = new CourierDetails();
                    courierDetails.setManifestNumber(shipmentDetails.getShippingManifestId());
                    courierDetails.setCourierCode(shipmentDetails.getShippingProviderCode());
                    courierDetails.setAwbNumber(shipmentDetails.getTrackingNumber());
                    courierDetails.setInvoiceNumber(shipmentDetails.getInvoiceCode());
                    courierDetailsMap.put(shipmentDetails.getShippingPackageId(), courierDetails);
                }
            }
        }catch (Exception e){
            log.error("Exception caught while creating map of shipment details");
        }
        return courierDetailsMap;
    }

    public List<MonitorPanelData1> updateGroupEffectiveStatusForGroupIdTable1(Map<String, OrderItemData> groupStatusMap,List<MonitorPanelData1> monitorPanelData1List){

        for(MonitorPanelData1 monitorPanelData : monitorPanelData1List){
            if(groupStatusMap.containsKey(monitorPanelData.getGroupId())){
                log.info("updating group effective status for the items corresponding to group id :{}", monitorPanelData.getGroupId());
                if(monitorPanelData.getShippingPackageId() == null){
                    monitorPanelData.setGroupEffectiveStatus(Constants.SHIPMENT_NOT_GENERATED_STATUS);
                }else{
                    monitorPanelData.setGroupEffectiveStatus(groupStatusMap.get(monitorPanelData.getGroupId()).getEffectiveStatus());
                }
                monitorPanelData.setV3FrTag(groupStatusMap.get(monitorPanelData.getGroupId()).getV3FrTag());
                monitorPanelData.setPickingPriority(groupStatusMap.get(monitorPanelData.getGroupId()).getPickingPriority());
                monitorPanelData.setIsTrueLastPiece(groupStatusMap.get(monitorPanelData.getGroupId()).getTrueLastPiece());
                monitorPanelData.setIsLensOnlyOrder(groupStatusMap.get(monitorPanelData.getGroupId()).getLensOnlyOrder());
            } else {
                log.error("No entry found in map for group id : {}", monitorPanelData.getGroupId());
            }
        }
        return monitorPanelData1List;
    }

    public List<MonitorPanelData2> updateGroupEffectiveStatusForGroupIdTable2(Map<String, OrderItemData> groupStatusMap,List<MonitorPanelData2> monitorPanelData2List) {
        for (MonitorPanelData2 monitorPanelData : monitorPanelData2List) {
            if (groupStatusMap.containsKey(monitorPanelData.getGroupId())){
                log.info("updating group effective status for the items corresponding to group id :{}", monitorPanelData.getGroupId());
                if(monitorPanelData.getShippingPackageId() == null){
                    monitorPanelData.setGroupEffectiveStatus(Constants.SHIPMENT_NOT_GENERATED_STATUS);
                }else{
                    monitorPanelData.setGroupEffectiveStatus(groupStatusMap.get(monitorPanelData.getGroupId()).getEffectiveStatus());
                }
                monitorPanelData.setV3FrTag(groupStatusMap.get(monitorPanelData.getGroupId()).getV3FrTag());
                monitorPanelData.setPickingPriority(groupStatusMap.get(monitorPanelData.getGroupId()).getPickingPriority());
                monitorPanelData.setIsTrueLastPiece(groupStatusMap.get(monitorPanelData.getGroupId()).getTrueLastPiece());
                monitorPanelData.setIsLensOnlyOrder(groupStatusMap.get(monitorPanelData.getGroupId()).getLensOnlyOrder());
            } else {
                log.error("No entry found in map for group id : {}", monitorPanelData.getGroupId());
            }
        }
        return monitorPanelData2List;
    }

//     tray making
//    tray making
//    MEI
    /**
     *
     * @param monitorPanelData
     * @param groupStatusMap
     *
     * This method creates the map of the effective status and the group id of the order items.
     */
    public void createGroupEffectiveStatusMap(MonitorPanelData monitorPanelData,Map<String,OrderItemData> groupStatusMap, Map<Integer, Integer> pickingPriorityMap) {
        log.info("creating map of the order item based on group id");
        String groupId = monitorPanelData.getGroupId();

        if (!groupStatusMap.containsKey(groupId)) {
            log.info("no entry found for the provided group id, creating a new entry in the groupStatusMap for group " +
                    "id : {} and monitorPanelData {}", groupId,monitorPanelData);
            OrderItemData orderItemData = new OrderItemData(monitorPanelData.getOrderItemLastUpdatedAt(),
                    monitorPanelData.getMpStatus(), monitorPanelData.getItemType(), monitorPanelData.getQcStatus(),
                    monitorPanelData.getGroupId(), monitorPanelData.getV3FrTag(),
                    pickingPriorityMap.containsKey(monitorPanelData.getOrderItemId()) ?
                            pickingPriorityMap.get(monitorPanelData.getOrderItemId()) :
                            Constants.DEFAULT_PICKING_PRIORITY, monitorPanelData.getIsTrueLastPiece(),
                    monitorPanelData.getIsLensOnlyOrder(),monitorPanelData.getIsAsrsOrder());
            groupStatusMap.put(groupId, orderItemData);

        } else {
            log.info("entry for the given group id found in the map, updating the effective group status for group id" +
                    " : {}", groupId);
            OrderItemData orderItemDataInMap = groupStatusMap.get(groupId);
            Date recentUpdateTime =
                    monitorPanelData.getOrderItemLastUpdatedAt().after(orderItemDataInMap.getRecentUpdateTime()) ?
                            monitorPanelData.getOrderItemLastUpdatedAt() : orderItemDataInMap.getRecentUpdateTime();
            orderItemDataInMap.setOrderItemType(getOrderItemType(monitorPanelData, orderItemDataInMap));
            String effectiveStatus =
                    Constants.statusPriorityMap.get(monitorPanelData.getMpStatus()) > Constants.statusPriorityMap.get(orderItemDataInMap.getEffectiveStatus()) ? monitorPanelData.getMpStatus() : orderItemDataInMap.getEffectiveStatus();
            log.info("Derived group effective status for item with MP status : {} , is : {}",
                    monitorPanelData.getMpStatus(), effectiveStatus);
            String v3FrTag = monitorPanelData.getFrTag().equalsIgnoreCase(Constants.FR2_TAG_PREFIX) ?
                    monitorPanelData.getFrTag() : orderItemDataInMap.getV3FrTag();
            Integer pickingPriority = Constants.DEFAULT_PICKING_PRIORITY;
            if (pickingPriorityMap.containsKey(monitorPanelData.getOrderItemId())) {
                pickingPriority =
                        pickingPriorityMap.get(monitorPanelData.getOrderItemId()) < orderItemDataInMap.getPickingPriority() ? pickingPriorityMap.get(monitorPanelData.getOrderItemId()) : orderItemDataInMap.getPickingPriority();
            }
            orderItemDataInMap.setPickingPriority(pickingPriority);
            orderItemDataInMap.setV3FrTag(v3FrTag);
            orderItemDataInMap.setEffectiveStatus(effectiveStatus);
            orderItemDataInMap.setRecentUpdateTime(recentUpdateTime);
            if (monitorPanelData.getQcStatus() != null) {
                orderItemDataInMap.setQcStatus(Constants.FAIL.equalsIgnoreCase(monitorPanelData.getQcStatus()) ?
                        monitorPanelData.getQcStatus() : orderItemDataInMap.getQcStatus());
            }
            if(monitorPanelData.getIsAsrsOrder())
                orderItemDataInMap.setIsAsrsOrder(Boolean.TRUE);
            orderItemDataInMap.setTrueLastPiece(Boolean.TRUE.equals(monitorPanelData.getIsTrueLastPiece()) ?
                    Boolean.TRUE.equals(monitorPanelData.getIsTrueLastPiece()) :
                    Boolean.TRUE.equals(orderItemDataInMap.getTrueLastPiece()));
            orderItemDataInMap.setLensOnlyOrder(Boolean.TRUE.equals(monitorPanelData.getIsLensOnlyOrder()) ?
                    monitorPanelData.getIsLensOnlyOrder() : Boolean.TRUE.equals(orderItemDataInMap.getLensOnlyOrder()));

            log.info("final order item data updated is  group id {} : {}", groupId, orderItemDataInMap);
        }
    }

    private static String getOrderItemType(MonitorPanelData monitorPanelData, OrderItemData orderItemDataInMap) {
        List<String> itemType = Arrays.asList(ItemType.FRAME.name(),ItemType.SUNGLASS.name());
        if( itemType.contains(monitorPanelData.getItemType()) || itemType.contains(orderItemDataInMap.getOrderItemType()) ) {
            if(orderItemDataInMap.getEffectiveStatus().equalsIgnoreCase(monitorPanelData.getMpStatus()))
                return ItemType.FRAME.name().equalsIgnoreCase(monitorPanelData.getItemType())? ItemType.FRAME.name() : ItemType.SUNGLASS.name();
        }
        return Constants.statusPriorityMap.get(monitorPanelData.getMpStatus()) > Constants.statusPriorityMap.get(orderItemDataInMap.getEffectiveStatus())
                ? monitorPanelData.getItemType() : orderItemDataInMap.getOrderItemType();
    }

    /**
     *
     * @param groupStatusMap
     * @return
     *
     *
     * This method updates the group effective status based on the mp status of the group.
     * new effective status would be represented as suggested by Product.
     */

    /*
    public List<MonitorPanelGroup> createMonitorPanelGroupFromGroupStatusMap(Map<String,OrderItemData> groupStatusMap){
        log.info("creating list monitor panel group data using the groupStatusMap");
        List<MonitorPanelGroup> monitorPanelGroupList = new ArrayList<>();
        for(Map.Entry<String, OrderItemData> entry : groupStatusMap.entrySet()){
            MonitorPanelGroup monitorPanelGroup = new MonitorPanelGroup();
            monitorPanelGroup.setGroupId(entry.getKey());
            monitorPanelGroup.setGroupEffectiveStatus(deriveGroupEffectiveStatus(entry.getValue()));
            monitorPanelGroup.setGroupEffectiveUpdateTime(entry.getValue().getRecentUpdateTime());
            monitorPanelGroupList.add(monitorPanelGroup);
        }
        log.info("monitor panel group list created to be persisted to the db.");
        return monitorPanelGroupList;
    }
    */

    /**
     *
     * @param orderItemData
     * @return
     *
     * deriving the group effective status for a given order item data in the group status map
     */

    public String deriveGroupEffectiveStatus(OrderItemData orderItemData){
        log.info("deriving group effective status for order item data in group status map");
        String groupEffectiveStatus = orderItemData.getEffectiveStatus();
        if(orderItemData.getOrderItemType().equalsIgnoreCase(Constants.LEFT_LENS) || orderItemData.getOrderItemType().equalsIgnoreCase(Constants.RIGHT_LENS)){
            if(orderItemData.getEffectiveStatus().equalsIgnoreCase(Constants.IN_PICKING) || orderItemData.getEffectiveStatus().equalsIgnoreCase(Constants.TRAY_MAKING)){
                groupEffectiveStatus = Constants.LENS+" "+orderItemData.getEffectiveStatus();
                orderItemData.setEffectiveStatus(groupEffectiveStatus);
            }
        }
        if(orderItemData.getQcStatus() != null && orderItemData.getQcStatus().equalsIgnoreCase(Constants.FAIL) && !Constants.statusToSkip.contains(orderItemData.getEffectiveStatus())){
            groupEffectiveStatus = Constants.QC+" "+Constants.FAIL+Constants.DELIMITER+groupEffectiveStatus;
            orderItemData.setEffectiveStatus(groupEffectiveStatus);
        }
        log.info("group effective status for group id : {} , is : {}", orderItemData.getGroupId(), orderItemData.getEffectiveStatus());
        return groupEffectiveStatus;
    }

    public boolean createMonitorPanelDataFromOrderItem(OrderItems orderItem, MonitorPanelData monitorPanelData,
                                                       Map<Integer, MonitorPanelPickingData> orderItemIdToPickingMap, Map<String,OrderItemData>groupStatusMap,
                                                       Map<String,JitOrderStatusDetails>jitOrderStatusDetailsMap,  Map<String,JitOrderItem> jitOrderItemStatusMap,
                                                       Map<Integer, Integer> pickingPriorityMap, Map<String,CourierDetails> courierDetailsMap) {

        try {
            if(StringUtils.isBlank(orderItem.getProcessingType()) || StringUtils.isBlank(orderItem.getItemType())) {
                log.error("Insufficient data to calculate fr tag: {}", orderItem.getOrderItemId());
                return false;
            }
            if(ObjectUtils.isNotEmpty(orderItem.getNexsOrderId()) && ObjectUtils.isNotEmpty(orderItem.getNexsOrderId())){
                monitorPanelData.setIncrementId(orderItem.getNexsOrderId().getIncrementId());
            } else {
                // in case null value
                monitorPanelData.setIncrementId(1);
            }



            if (orderItem.getShippingPackageId() != null ) {
                monitorPanelData.setShippingPackageId(orderItem.getShippingPackageId());
                if(courierDetailsMap.containsKey(orderItem.getShippingPackageId())){
                    CourierDetails courierDetails = courierDetailsMap.get(orderItem.getShippingPackageId());
                    monitorPanelData.setAwbNumber(courierDetails.getAwbNumber());
                    monitorPanelData.setCourierCode(courierDetails.getCourierCode());
                    monitorPanelData.setManifestNo(courierDetails.getManifestNumber());
                    monitorPanelData.setInvoiceNumber(courierDetails.getInvoiceNumber());
                }
            }

            monitorPanelData.setWmsStatus(orderItem.getStatus());

            if (orderItem.getStatus() != null && orderItem.getProcessingType() != null) {

                monitorPanelData.setMpStatus(deriveMpStatus(orderItem.getStatus(), orderItem.getProcessingType(),
                         orderItemIdToPickingMap, orderItem.getOrderItemId()));
            }

            if(monitorPanelData.getMpStatus() == null) {
                log.error("Monitor panel status is null for order item: {}", orderItem.getOrderItemId());
                monitorPanelData.setMpStatus(Constants.OTHERS);

            }

            /**
             *  fr tags for v3
             */
            if(Constants.FRANCHISE_BULK.equalsIgnoreCase(orderItem.getChannel())){
                monitorPanelData.setV3FrTag(Constants.BULK);
            }else if (Constants.frTagsMap.containsKey(orderItem.getProcessingType() + Constants.DELIMITER + orderItem.getItemType())){
                monitorPanelData.setV3FrTag(Constants.frTagsMap.get(orderItem.getProcessingType() + Constants.DELIMITER + orderItem.getItemType()));
            }else{
                monitorPanelData.setV3FrTag(Constants.OTHERS);
            }
            /**
             * fr tags for v2
             */
            if (Constants.tagsMap.containsKey(orderItem.getProcessingType() + Constants.DELIMITER + orderItem.getItemType())) {
                monitorPanelData.setFrTag(Constants.tagsMap.get(orderItem.getProcessingType() + Constants.DELIMITER + orderItem.getItemType()));
            } else {
                log.error("FR tag not found for order item: {}", orderItem.getOrderItemId());
                monitorPanelData.setFrTag(Constants.OTHERS);
            }

            monitorPanelData.setMarketPlaceOrder(Constants.MARKET_PLACE_ORDER.equalsIgnoreCase(orderItem.getNavChannel()));
            monitorPanelData.setFittingId(orderItem.getFittingId());
            monitorPanelData.setOrderItemId(orderItem.getOrderItemId());
            monitorPanelData.setProductId(orderItem.getProduct_id());
            monitorPanelData.setWmsOrderCode(orderItem.getWmsOrderCode());

            long orderItemUpdateTime = orderItem.getUpdatedAt().getTime();
            long orderItemCreatedTime = orderItem.getCreatedAt().getTime();
            long currentTime = new Date().getTime();
            float ageingInHours = epochToHours(currentTime - orderItemCreatedTime);
            float updateAgeingInHours = epochToHours(currentTime - orderItemUpdateTime);

            monitorPanelData.setAgeingSinceCreated(ageingInHours);
            monitorPanelData.setAgeingSinceLastUpdate(updateAgeingInHours);

            monitorPanelData.setOrderChannel(orderItem.getChannel());

            if (orderItem.getPower() != null) {

                monitorPanelData.setLensType(orderItem.getPower().getLensType());
                monitorPanelData.setPowerType(derivePowerType(orderItem.getPower().getWithAp()));
                monitorPanelData.setLensPackageType(orderItem.getPower().getLensPackageType());

            }

            if (ObjectUtils.isEmpty(monitorPanelData.getInvoiceNumber()) || ObjectUtils.isEmpty(monitorPanelData.getCourierCode()) && ObjectUtils.isNotEmpty(monitorPanelData.getShippingPackageId()) && fetchInvoice && invoiceEligibleStatus.contains(monitorPanelData.getWmsStatus())) {
                log.info("fetching shipment details for shipment id : {}", monitorPanelData.getShippingPackageId());
                ShipmentDetailsDto shipmentDetailsDto = getShipmentDetails(monitorPanelData.getShippingPackageId(), monitorPanelData);
                if (ObjectUtils.isNotEmpty(shipmentDetailsDto)) {
                    log.info("updating monitor panel data with shipment details : {}", shipmentDetailsDto.toString());
                    monitorPanelData.setInvoiceNumber(shipmentDetailsDto.getInvoiceNumber());
                    monitorPanelData.setCourierCode(shipmentDetailsDto.getCourierCode());
                    log.info("monitor panel data successfully updated with shipment details for shipment id : {}", monitorPanelData.getShippingPackageId());
                }else {
                    log.error("failed to update courier code and invoice for shipment : {}", monitorPanelData.getShippingPackageId());
                }
            }


            monitorPanelData.setCountry(orderItem.getNexsOrderId().getCountryCode());

            updateOrderItemCreatedAt(monitorPanelData, orderItem);

            monitorPanelData.setOrderItemLastUpdatedAt(orderItem.getUpdatedAt());
            monitorPanelData.setProcessingType(orderItem.getProcessingType());
            monitorPanelData.setItemType(orderItem.getItemType());
            monitorPanelData.setBarcode(orderItem.getBarcode());
            monitorPanelData.setTrayId(orderItem.getLocationId());
            boolean isJit = false;
            OrderItemHeader orderItemHeader = orderItem.getOrder_item_header_id();
            if(null != orderItemHeader) {
                isJit = Constants.JIT.equalsIgnoreCase(orderItemHeader.getOrderItemType().name());
            }

            monitorPanelData.setOwndaysOrder(owndaysOrderChannelList.contains(orderItem.getNavChannel()));
            monitorPanelData.setInternationalOrder(isInternationalOrder(orderItem));

            monitorPanelData.setIsJit(isJit);

            if(orderItem.getStatus().equals(OrderItemStatus.QC_DONE.toString())) {
                monitorPanelData.setQcStatus( Constants.PASS );
            } else if(orderItem.getStatus().equals(OrderItemStatus.QC_HOLD.toString())) {
                monitorPanelData.setQcStatus( Constants.HOLD );
            }

            if(orderItem.getQcFailCount() > 0) {
                monitorPanelData.setQcStatus(Constants.FAIL);
            }

            monitorPanelData.setIsAllocated(deriveIsAllocated(monitorPanelData.getWmsStatus(), monitorPanelData.getProcessingType(), monitorPanelData.getBarcode()));
            monitorPanelData.setShipToCustomer( !orderItem.getOrder_item_header_id().getShipToStoreRequired() );
            monitorPanelData.setPaymentNotCaptured( !orderItem.getNexsOrderId().getPaymentStatus() );
            monitorPanelData.setExchangeFlag( orderItem.getChannel().equals(Constants.EXCHANGE) ? true : false);

            monitorPanelData.setVsmHoldFlag(orderItem.getHold() != null? orderItem.getHold() == 1 : false);
            monitorPanelData.setIsAsrsOrder(deriveIsAsrsOrder(orderItem.getOrderItemId(), orderItemIdToPickingMap));
            monitorPanelData.setIsFulfillable(FulfillableType.NON_FULFILLABLE.equals(orderItem.getFulfillableType()) || FulfillableType.JIT.equals(orderItem.getFulfillableType()));
            monitorPanelData.setFacilityCode(orderItem.getFacilityCode());
            monitorPanelData.setCreatedAt(new Date());
            monitorPanelData.setUpdatedAt(new Date());
            //@TODO
            // need to be revisited to make changes based on fr tags
            String groupId = "";
            if(orderItem.getFittingId() == 0 && orderItem.getShippingPackageId() == null){
                groupId = monitorPanelData.getIncrementId().toString();
                monitorPanelData.setGroupType(Constants.GroupType.INCREMENT_ID.name());
            }else{
                groupId = orderItem.getFittingId()==0?orderItem.getShippingPackageId():Integer.toString(orderItem.getFittingId());
                monitorPanelData.setGroupType(orderItem.getFittingId()==0?Constants.GroupType.SHIPPING_ID.name() : Constants.GroupType.FITTING_ID.name());
            }
            monitorPanelData.setGroupId(groupId);
            if (ObjectUtils.isEmpty(monitorPanelData.getShippingPackageId())) {
                monitorPanelData.setGroupEffectiveStatus(Constants.SHIPMENT_NOT_GENERATED_STATUS);
            }else{
                monitorPanelData.setGroupEffectiveStatus(monitorPanelData.getMpStatus());
            }

            if(ObjectUtils.isEmpty(monitorPanelData.getGroupEffectiveStatus())){
                monitorPanelData.setGroupEffectiveStatus(Constants.JIT_UN_CLASSIFIED);
            }
            if(null != orderItem.getOrderItemMetaDataList()){
                log.info("order item meta list for shipping id  : {},  formed is : {}",monitorPanelData.getShippingPackageId(), orderItem.getOrderItemMetaDataList());

                boolean isSuperOrder = false;

                for(OrderItemMetaData metaData : orderItem.getOrderItemMetaDataList()){
                    if(Constants.SUPER_ORDER.equalsIgnoreCase(metaData.getPairKey())){
                        isSuperOrder = metaData.getValue().equalsIgnoreCase(Boolean.TRUE.toString());
                    }
                    if(Constants.STORE_INVENTORY_TYPE.equalsIgnoreCase(metaData.getPairKey())){
                        if(Constants.LENS_ONLY_ORDER.equalsIgnoreCase(metaData.getValue())){
                            monitorPanelData.setIsLensOnlyOrder(Boolean.TRUE);
                            monitorPanelData.setIsTrueLastPiece(Boolean.FALSE);
                        }else if(Constants.TRUE_LAST_PIECE.equalsIgnoreCase(metaData.getValue()) || Constants.LAST_PIECE.equalsIgnoreCase(metaData.getValue())){
                            monitorPanelData.setIsTrueLastPiece(Boolean.TRUE);
                        }
                    }
                }
                monitorPanelData.setIsSuperOrder(isSuperOrder);
            }
            createGroupEffectiveStatusMap(monitorPanelData, groupStatusMap,pickingPriorityMap);
            log.info("[{}, createMonitorPanelDataFromOrderItem] Checking for jit map for orderItemID {}",this.getClass().getName(),orderItem.getOrderItemId());
            if(isJit && jitOrderStatusDetailsMap.containsKey(monitorPanelData.getOrderItemId().toString())){
                JitOrderStatusDetails jitOrderStatusDetail = jitOrderStatusDetailsMap.get(monitorPanelData.getOrderItemId().toString());
                String jitType = jitOrderStatusDetail.getOrderSubType() == null ? Constants.JIT_LENS_LAB :  Constants.JIT_MANUAL.equalsIgnoreCase(jitOrderStatusDetail.getOrderSubType()) ? Constants.JIT_EXTERNAL_VENTOR : Constants.JIT_LENS_LAB;
                String jitStatus = getJitStatus(jitOrderStatusDetail, monitorPanelData,jitType);;
                monitorPanelData.setJitGroupStatus(jitStatus);
                monitorPanelData.setJitStatus(jitStatus);
                if(Constants.INVENTORY_NOT_FOUND_STATUS.equalsIgnoreCase(jitOrderStatusDetail.getSubStatus())){
                    monitorPanelData.setBlankPid(jitOrderStatusDetail.getBlankPid());
                }
                monitorPanelData.setJitType(jitType);
                createJitGroupEffectiveStatusMap(monitorPanelData,jitOrderItemStatusMap);
            }
            monitorPanelData.setErrorType(orderItem.getErrorType());
            return true;
        } catch (Exception e) {
            log.error("[createMonitorPanelDataFromOrderItem] error while populating monitor panel data for order item id: "+orderItem.getOrderItemId()
                    +" - message: "+
                    e);
            return false;
        }
    }

    private void updateOrderItemCreatedAt(MonitorPanelData monitorPanelData, OrderItems orderItem) throws ParseException {
        if(Constants.MARKET_PLACE_ORDER.equals(orderItem.getNavChannel()) && !Constants.CREATED_STATUS.equalsIgnoreCase(orderItem.getStatus())) {
            for(OrderItemMetaData itemMetaData : orderItem.getOrderItemMetaDataList()){
                if(itemMetaData.getPairKey().equals(Constants.NEW_CREATED_AT)){
                    monitorPanelData.setOrderItemCreatedAt(itemMetaData.getCreatedAt());
                }
            }
        } else {
            monitorPanelData.setOrderItemCreatedAt(orderItem.getCreatedAt());
        }
    }


    public boolean isInternationalOrder(OrderItems orderItems){

        try{
            Object cacheDAOVal = cacheDAO.getVal(orderItems.getFacilityCode());

            Address address = addressRepository.findByOrderItemHeaderIdAndAddressType(Long.valueOf(orderItems.getOrder_item_header_id().getId()), Constants.SHIPMENT_ADDRESS_TYPE);

            String expectedFacilityCountryCode;
            String currentFacilityCountryCode = address.getCountry();

            if(ObjectUtils.isNotEmpty(cacheDAOVal)){
                expectedFacilityCountryCode = cacheDAOVal.toString();
                log.info("expectedFacilityCountryCode from cacheDAOVal - {}",expectedFacilityCountryCode);
            }else{
                HubMaster hubMaster = hubMasterRepository.findByFacilityCode(orderItems.getFacilityCode());
                if(ObjectUtils.isEmpty(hubMaster)){
                    return false;
                }
                expectedFacilityCountryCode = hubMaster.getCountry();
                cacheDAO.putVal(orderItems.getFacilityCode(),expectedFacilityCountryCode, 48L, TimeUnit.HOURS);
            }

            if(!expectedFacilityCountryCode.equals(currentFacilityCountryCode)){
                return true;
            }

        }catch (Exception e){
            log.error("Exception occurred while checking isInternationalOrder as e - "+e);
            log.error("exception caught while identifying international order for shipment : {} , setting false as default", orderItems.getShippingPackageId());
        }
        return false;
    }

    public ShipmentDetailsDto getShipmentDetails(String shipmentId, MonitorPanelData monitorPanelData) throws Exception {
        log.info("fetching shipment details for shipment id : {}", shipmentId);
        ShipmentDetailsDto shipmentDetailsDto = new ShipmentDetailsDto();
        try{
            Object cacheDAOVal = cacheDAO.getVal(shipmentId);
            if(ObjectUtils.isNotEmpty(cacheDAOVal)) {
                shipmentDetailsDto = ObjectHelper.getObjectMapper().readValue(cacheDAOVal.toString(), ShipmentDetailsDto.class);
                log.info("shipment details object received for shipment id : {}  ,  is : {}", shipmentId, shipmentDetailsDto.toString());
            }
            // if in any case null value is present in the Shipment detail object, update the value
            if(ObjectUtils.isEmpty(shipmentDetailsDto.getCourierCode())){
                Optional<String> softCourier = packetHandoverDetailsRepository.getCourierByPackageId(shipmentId);
                log.info("courier code  for shipment id : {} ,  is : {}", shipmentId,softCourier);
                shipmentDetailsDto.setCourierCode(softCourier.orElse(null));
            }

            if(ObjectUtils.isEmpty(shipmentDetailsDto.getInvoiceNumber())){
                String invoiceNum = getInvoiceNumber(shipmentId);
                shipmentDetailsDto.setInvoiceNumber(invoiceNum);
                log.info("invoice number for shipment id : {} ,  is : {}", shipmentId,invoiceNum);
            }

            cacheDAO.putVal(shipmentId,shipmentDetailsDto, 48L, TimeUnit.HOURS);
        }catch (Exception e){
            log.error("failed to fetch invoice or courier for shipment : {}", shipmentId);
        }
        return  shipmentDetailsDto;
    }

    public String getInvoiceNumber(String shippingPackageId) throws Exception {
        log.info("fetching invoice number for shipping id : {}", shippingPackageId);
        DocumentDetailsDto documentDetailsDto = new DocumentDetailsDto();
        try{
            documentDetailsDto = fdsService.getDocumentDetails(shippingPackageId);
            if(ObjectUtils.isEmpty(documentDetailsDto)){
                log.info("no invoice found in FDS, checking wms for shipment : {}", shippingPackageId);
                Invoice invoice = invoiceRepository.findByShipmentPackageId(shippingPackageId);
                return invoice.getId();
            }
        }catch (Exception e){
            log.info("No invoice found for shipment id : {}", shippingPackageId);
        }

        return documentDetailsDto.getDocumentNo();
    }

    public String getJitStatus(JitOrderStatusDetails jitOrderStatusDetail, MonitorPanelData monitorPanelData, String jitType){

        log.info("[{}, getJitStatus] The jit stauts detais is {} for orderITemId {}",this.getClass().getSimpleName(),
                jitOrderStatusDetail,monitorPanelData.getOrderItemId());
        String jitStatus = Constants.JIT_UN_CLASSIFIED;
        if(Constants.outOFLensLabWmsStatus.contains(monitorPanelData.getWmsStatus())) {
            jitStatus  =  Constants.JIT_EXTERNAL_VENTOR.equalsIgnoreCase(jitType)? Constants.JIT_OUT_OF_MANUAL :Constants.JIT_OUT_OF_LENSLAB;
        }
        else {
            if(Constants.JIT_EXTERNAL_VENTOR.equalsIgnoreCase(jitType)){
                log.info("[{} ,getJitStatus ] Using new flow of jit status being updated for manual jit orderItemId {}", this.getClass().getSimpleName(),
                        jitOrderStatusDetail.getSourceReferenceId());
                jitStatus = determineManualJitStatus(monitorPanelData,jitStatus);
            } else {
                log.info("[{} ,getJitStatus ] Using new flow of jit status being updated for auto jit for orderItemId {}", this.getClass().getSimpleName(),
                        jitOrderStatusDetail.getSourceReferenceId());
                jitStatus = determineNewJitStatusForAutoJit(jitOrderStatusDetail, monitorPanelData, jitStatus);
            }
        }

        log.info("[{} ,getJitStatus ] The jitStatus is {} for  orderItemId {}",this.getClass().getSimpleName(),jitStatus,
                jitOrderStatusDetail.getSourceReferenceId());
        return jitStatus;
    }

    private String determineManualJitStatus(MonitorPanelData monitorPanelData, String jitStatus) {
        StringBuffer jitKey = new StringBuffer();
        if (Constants.FAIL.equalsIgnoreCase(monitorPanelData.getQcStatus())) {
            jitKey.append("QC_FAIL");
            jitKey.append(Constants.DELIMITER);
        }
        if(null!=monitorPanelData.getWmsStatus()) {
            jitKey.append(monitorPanelData.getWmsStatus());
        }
        log.info("[{},determineManualJitStatus] the jit key for orderItemId {} is {}",this.getClass().getSimpleName(),monitorPanelData.getOrderItemId(),jitKey.toString());
        jitStatus = Constants.manualJitOrderStatusMap.containsKey(jitKey.toString())?Constants.manualJitOrderStatusMap.get(jitKey.toString()):Constants.JIT_UN_CLASSIFIED;
        return jitStatus;
    }

    /**
     *
     * @param jitOrderStatusDetail
     * @param monitorPanelData
     * @param jitStatus
     * @return
     */
    private String determineNewJitStatusForAutoJit(JitOrderStatusDetails jitOrderStatusDetail, MonitorPanelData monitorPanelData, String jitStatus) {
        StringBuffer jitKey = new StringBuffer();
        if (jitOrderStatusDetail.getQcFailCount()>0) {
            jitKey.append("QC_FAIL");
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getRxString())) {
            jitKey.append(jitOrderStatusDetail.getRxString());
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getOrderStatus())) {
            jitKey.append(jitOrderStatusDetail.getOrderStatus());
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getSubStatus())) {
            jitKey.append(jitOrderStatusDetail.getSubStatus());
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getLkStatus())) {
            jitKey.append(jitOrderStatusDetail.getLkStatus());
        }
        log.info("[] the jit key for orderItemId {} is {}",jitOrderStatusDetail.getSourceReferenceId(),jitKey.toString());
        jitStatus = Constants.autoJitOrderStatusMap.containsKey(jitKey.toString())?Constants.autoJitOrderStatusMap.get(jitKey.toString()):Constants.JIT_UN_CLASSIFIED;
        return jitStatus;
    }

    public Map<String, JitOrderItem> createJitGroupEffectiveStatusMap(MonitorPanelData monitorPanelData, Map<String,JitOrderItem> jitOrderItemMap){
        log.info("[{}, createJitGroupEffectiveStatusMap] getting group effective status for jit order with id : {}",
                this.getClass().getSimpleName(),monitorPanelData.getFittingId());
        if(jitOrderItemMap.containsKey(monitorPanelData.getFittingId().toString())){
            JitOrderItem existingOrderItem = jitOrderItemMap.get(monitorPanelData.getFittingId().toString());
            String jitGroupStatus;
            if(monitorPanelData.getJitStatus() == null && existingOrderItem.getJitGroupStatus() == null ){
                jitGroupStatus = Constants.JIT_UN_CLASSIFIED;
            }else{
                jitGroupStatus =
                        Constants.jitGroupStatusPriorityMap.get(existingOrderItem.getJitGroupStatus())>Constants.jitGroupStatusPriorityMap.get(monitorPanelData.getJitGroupStatus()) ?
                                existingOrderItem.getJitGroupStatus() : monitorPanelData.getJitGroupStatus();
            }
            if (monitorPanelData.getQcStatus() != null) {
                existingOrderItem.setQcStatus(Constants.FAIL.equalsIgnoreCase(monitorPanelData.getQcStatus()) ?
                        monitorPanelData.getQcStatus() : existingOrderItem.getQcStatus());
            }

            log.info("[{}, createJitGroupEffectiveStatusMap] getting group effective status as : {},  for jit order with id : {}",
                    this.getClass().getSimpleName(), jitGroupStatus, monitorPanelData.getFittingId());
            existingOrderItem.setJitGroupStatus(jitGroupStatus);
        }else {
            JitOrderItem jitOrderItem = new JitOrderItem();
            jitOrderItem.setQcStatus(monitorPanelData.getQcStatus());
            jitOrderItem.setJitType(monitorPanelData.getJitType());
            jitOrderItem.setJitGroupStatus(monitorPanelData.getJitGroupStatus());
            jitOrderItem.setItemType(monitorPanelData.getItemType());
            jitOrderItemMap.put(monitorPanelData.getFittingId().toString(),jitOrderItem);
        }

        return jitOrderItemMap;
    }

    private float epochToHours(long time){

        return Float.parseFloat(String.format("%.2f",(float)time/(1000*60*60)));
    }

    private String derivePowerType(String isAp){
        String powerType=Constants.PowerType.SINGLE_VISION.getLabel();
        if(!StringUtils.isEmpty(isAp)){
            if(isAp.equalsIgnoreCase("yes"))
                powerType=Constants.PowerType.BIFOCAL.getLabel();
        }
        return powerType;
    }

    /**
     *
     * @param orderItemId
     * @param orderItemIdToPickingMap
     * @return
     *
     *  method to derive if the given order item is an ASRS order
     */
    private boolean deriveIsAsrsOrder(int orderItemId,Map<Integer, MonitorPanelPickingData> orderItemIdToPickingMap){
        log.info("deriving if the order item is an asrs order for orderItemId : {}",orderItemId);
        if(orderItemIdToPickingMap.containsKey(orderItemId)){
            return orderItemIdToPickingMap.get(orderItemId).getPriority()==3 ||  ( Constants.nonAdverbPckingAndSummaryStatus.contains(orderItemIdToPickingMap.get(orderItemId).getStatus()) && "ASRS".equalsIgnoreCase(orderItemIdToPickingMap.get(orderItemId).getLocation()));
        }
        return false;
    }

    private String deriveMpStatus(String wmsStatus, String processingType, Map<Integer, MonitorPanelPickingData> orderItemIdToPickingMap, int orderItemId){

        if(orderItemIdToPickingMap.containsKey(orderItemId)){
//Incorporating pending picking status
            if(wmsStatus.equals(OrderItemStatus.IN_PICKING.toString())) {
                if(orderItemIdToPickingMap.get(orderItemId).getStatus() < 2) {
                    wmsStatus = OrderItemStatus.PENDING_PICKING.toString();
                }
            }
        } else {
            log.info("orderItemIdToPickingStatusMap does not contains key :{} ans status is : {}", orderItemId, wmsStatus);

        }

        String mpStatus = null;
        if(ProcessingType.FR0.toString().equals(processingType)) {
            if(Constants.fr0StatusMap.containsKey(wmsStatus)) {
                mpStatus = Constants.fr0StatusMap.get(wmsStatus);
            }
        } else {
            if(Constants.fr1_fr2StatusMap.containsKey(wmsStatus)) {
                mpStatus = Constants.fr1_fr2StatusMap.get(wmsStatus);
            }
        }
        return mpStatus;
    }


    private boolean deriveIsAllocated(String wmsStatus, String processingType, String barcode){
        String mpStatus = null;
        if(ProcessingType.FR0.toString().equals(processingType)) {
            if(Constants.fr0StatusMap.containsKey(wmsStatus)) {
                mpStatus = Constants.fr0StatusMap.get(wmsStatus);
            }
        } else {
            if(Constants.fr1_fr2StatusMap.containsKey(wmsStatus)) {
                mpStatus = Constants.fr1_fr2StatusMap.get(wmsStatus);
            }
        }
        return Constants.statusPriorityMap.get(mpStatus)<Constants.statusPriorityMap.get(Constants.IN_PICKING) && !StringUtils.isEmpty(barcode);
    }

    public void getStatusInPickListOrderItemTable(List<OrderItems> orderItemsList, Map<Integer, MonitorPanelPickingData> orderItemIdToPickingStatusMap, List<Integer> jitOrderItemIds, Map<Integer, Integer> pickingPriorityMap) {
        for(int start=0 ; start<=orderItemsList.size(); start += Constants.PICK_LIST_ORDER_ITEM_PAGE_SIZE) {
            int end = start + (Math.min((orderItemsList.size() - start), Constants.PICK_LIST_ORDER_ITEM_PAGE_SIZE));
            List<Integer> orderItemIds = new ArrayList<>();
            int var = start;
            for(;var<end;var++) {
                OrderItems orderItem = orderItemsList.get(var);
                orderItemIds.add(orderItem.getOrderItemId());
                OrderItemHeader orderItemHeader = orderItem.getOrder_item_header_id();
                if(null!=orderItemHeader && OrderItemType.JIT.equals(orderItemHeader.getOrderItemType())) {
                    jitOrderItemIds.add(orderItem.getOrderItemId());
                }
            }
            List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepository.findByScmOrderItemIdIn(orderItemIds);
            for(PicklistOrderItem picklistOrderItem : picklistOrderItemList) {
                MonitorPanelPickingData monitorPanelPickingData = new MonitorPanelPickingData(picklistOrderItem.getPriority(),picklistOrderItem.getStatus(),picklistOrderItem.getLocationType());
                orderItemIdToPickingStatusMap.put(picklistOrderItem.getScmOrderItemId(), monitorPanelPickingData);
                pickingPriorityMap.put(picklistOrderItem.getScmOrderItemId(), picklistOrderItem.getPriority());
            }
        }
        log.info("order item picking"+orderItemIdToPickingStatusMap);
    }

    public void getOrderItemsAndCorrespondingPickingStatus(List<OrderItems> orderItemsList , Map<Integer, MonitorPanelPickingData> orderItemIdToPickingStatusMap,List<Integer> jitOrderItemIds, Map<Integer,Integer> pickingPriorityMap, Pageable pageable,long maxId) throws Exception {
            List<OrderItems> currentOrderItemsList = new ArrayList<>();
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.DAY_OF_MONTH, -interval);
            currentOrderItemsList = orderItemRepository.findAllByStatusInAndIdLessThanEqualAndCreatedAtGreaterThanEqual(new ArrayList<>(Constants.fr1_fr2StatusMap.keySet()),maxId, cal.getTime(), pageable);
            orderItemsList.addAll(currentOrderItemsList);
            getStatusInPickListOrderItemTable(currentOrderItemsList, orderItemIdToPickingStatusMap,jitOrderItemIds, pickingPriorityMap);
//            pageNumber++;
        }


    /**
     *  derive jit group effective status and update map
     * @param monitorPanelData
     * @param jitOrderItemMap
     * @return
     */



    /**
     *  returns jit key to be used to fetch the status from map
     * @param jitOrderStatusDetail
     * @return
     */



    /**
     *  create a map of jit order items
     * @param jitOrderIds
     * @return
     */
    public Map<String, JitOrderStatusDetails> createJitOrderItemMap(List<Integer> jitOrderIds){
        Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap = new HashMap<>();
        if(isJitPanelProcessEnable) {
            List<String> jitIds = new ArrayList<>();
            for (Integer jitOrderId : jitOrderIds) {
                if (jitOrderId != null) {
                    jitIds.add(jitOrderId.toString());
                }
            }
            List<JitOrderStatusDetails> jitOrderItemStatusList = jitDbRepository.findBySourceReferenceIdInAndOrderTypeEquals(jitIds, Constants.JIT);
            log.info("checking if the list received from jitdb is empty : {}", jitOrderItemStatusList == null);
            for (JitOrderStatusDetails jitOrderStatusDetail : jitOrderItemStatusList) {
                jitOrderStatusDetailsMap.put(jitOrderStatusDetail.getSourceReferenceId(), jitOrderStatusDetail);
//            jitOrderStatusDetailsMap.put(jitOrderStatusDetail.getFittingId().toString(), jitOrderStatusDetail);
            }
        }
        log.info("jit status map created : {}", jitOrderStatusDetailsMap);
        return jitOrderStatusDetailsMap;
    }


    /**
     *  updating jit group status for monitorPanelData1List
     * @param jitOrderItemMap
     * @param monitorPanelData1List
     * @return
     */

    public List<MonitorPanelData1> updateJitGroupEffectiveStatusForTable1(Map<String,JitOrderItem> jitOrderItemMap, List<MonitorPanelData1> monitorPanelData1List){
        log.info("updating jit group status for monitor panel data list");
        for(MonitorPanelData1 monitorPanelData : monitorPanelData1List){
            if(jitOrderItemMap.containsKey(monitorPanelData.getFittingId().toString())){
                log.info("updating group effective status for jit order with id : {}", monitorPanelData.getFittingId());
                monitorPanelData.setJitGroupStatus(jitOrderItemMap.get(monitorPanelData.getFittingId().toString()).getJitGroupStatus());
                monitorPanelData.setJitType(jitOrderItemMap.get(monitorPanelData.getFittingId().toString()).getJitType());
            }else{
                log.error("No entry found in map for fitting id : {}", monitorPanelData.getFittingId());
            }
        }

        return monitorPanelData1List;
    }

    /**
     *  updating jit group status for monitorPanelData2List
     * @param jitOrderItemMap
     * @param monitorPanelData2List
     * @return
     */
    public List<MonitorPanelData2> updateJitGroupEffectiveStatusForTable2(Map<String, JitOrderItem> jitOrderItemMap, List<MonitorPanelData2> monitorPanelData2List){
        log.info("updating jit group status for monitor panel data list");
        for(MonitorPanelData2 monitorPanelData : monitorPanelData2List){
            if(jitOrderItemMap.containsKey(monitorPanelData.getFittingId().toString())){
                log.info("updating group effective status for jit order with id : {}", monitorPanelData.getFittingId());
                monitorPanelData.setJitGroupStatus(jitOrderItemMap.get(monitorPanelData.getFittingId().toString()).getJitGroupStatus());
                monitorPanelData.setJitType(jitOrderItemMap.get(monitorPanelData.getFittingId().toString()).getJitType());
            }else{
                log.error("No entry found in map for fitting id : {}", monitorPanelData.getFittingId());
            }
        }
        return monitorPanelData2List;
    }

    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    public void deleteTableAndUpdateKey(String[] tableNameAndUpdatedAtList) throws Exception {
        if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {
            log.info("[MonitorPanelDataCron] truncating data from table : {}", tableNameAndUpdatedAtList[0]);
            monitorPanelData1Repository.truncateTable();
            cacheDAO.putVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY, Constants.MONITOR_TABLE_2 + Constants.DELIMITER + System.currentTimeMillis());
        }else{
            log.info("[MonitorPanelDataCron] truncating data from table : {}", tableNameAndUpdatedAtList[0]);
            monitorPanelData2Repository.truncateTable();
            cacheDAO.putVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY, Constants.MONITOR_TABLE_1 + Constants.DELIMITER + System.currentTimeMillis());
        }
    }
}
