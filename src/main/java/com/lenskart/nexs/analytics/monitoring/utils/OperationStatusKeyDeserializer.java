package com.lenskart.nexs.analytics.monitoring.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.KeyDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.analytics.activity.model.OperationStatus;
import com.lenskart.nexs.ims.model.Transitions;

import java.io.IOException;

public class OperationStatusKeyDeserializer extends KeyDeserializer {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public OperationStatus deserializeKey(final String key, final DeserializationContext ctxt ) throws IOException, JsonProcessingException {
        return objectMapper.readValue(key, OperationStatus.class);
    }
}