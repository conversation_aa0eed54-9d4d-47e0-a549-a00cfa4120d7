package com.lenskart.nexs.analytics.monitoring.utils;

import com.lenskart.nexs.analytics.monitoring.MonitorPanelDataCoulmnNameConstants;
import com.lenskart.nexs.analytics.monitoring.response.ProductDetailsPageResponse;

import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProductDetailsUtils {
    public static List<ProductDetailsPageResponse> detailsPageMapper(List<Tuple> tuples, List<Tuple> total, List<ProductDetailsPageResponse> productDetailsPageResponseList){
        Map<String, BigInteger> totalCountMap = new HashMap<>();

        for(Tuple tuple : total){
            totalCountMap.put((String) tuple.get(MonitorPanelDataCoulmnNameConstants.FACILITY_CODE),(BigInteger) tuple.get(MonitorPanelDataCoulmnNameConstants.TOTAL));
        }

        if(tuples.isEmpty()){
            return productDetailsPageResponseList;
        }
        for(Tuple tuple : tuples){
            ProductDetailsPageResponse response = new ProductDetailsPageResponse();
            response.setAllocated((BigInteger) tuple.get(MonitorPanelDataCoulmnNameConstants.ALLOCATED));
            response.setFulfillable((BigInteger)tuple.get(MonitorPanelDataCoulmnNameConstants.FULFILLABLE));
            response.setUnfulfillable((BigInteger)tuple.get(MonitorPanelDataCoulmnNameConstants.UNFULFILLABLE));
            response.setFacilityCode((String) tuple.get(MonitorPanelDataCoulmnNameConstants.FACILITY_CODE));
            response.setTotal(totalCountMap.get((String) tuple.get(MonitorPanelDataCoulmnNameConstants.FACILITY_CODE)));
            productDetailsPageResponseList.add(response);
        }
        return productDetailsPageResponseList;
    }
}
