package com.lenskart.nexs.analytics.monitoring.request;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import com.lenskart.nexs.analytics.monitoring.entities.mongodb.ShipmentDetails;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.entities.picking.PicklistOrderItem;
import com.lenskart.nexs.analytics.monitoring.model.CourierDetails;
import com.lenskart.nexs.analytics.monitoring.model.JitOrderItem;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData;
import com.lenskart.nexs.analytics.monitoring.model.OrderItemData;
import com.lenskart.nexs.analytics.monitoring.repository.picking.PicklistOrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.repository.scm.ShipmentDetailsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MonitorPanelMaps {

    public Map<Integer,Integer> orderItemToPickingStatusMap;
    public Map<String, OrderItemData> groupStatusMap;
    public Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap;
    public Map<String, JitOrderItem> jitOrderItemStatusMap;
    public Map<Integer,Integer> pickingPriorityMap;
    public Map<String, CourierDetails> courierDetailsMap;

    public void initialiseMaps(){
        orderItemToPickingStatusMap = new HashMap<>();
        groupStatusMap = new HashMap<>();
        jitOrderStatusDetailsMap = new HashMap<>();
        jitOrderItemStatusMap = new HashMap<>();
        pickingPriorityMap = new HashMap<>();
        courierDetailsMap = new HashMap<>();
    }

}
