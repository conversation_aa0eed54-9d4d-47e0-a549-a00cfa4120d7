package com.lenskart.nexs.analytics.monitoring.entities.monitorpanel;


import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Setter
@Getter
@Entity
@Table(name = "monitor_panel_data_aud")
@IdClass(MonitorPanelCompositeKey.class)
public class MonitorPanelNewHistory extends MonitorPanelData {

//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @Column(name = "history_id", nullable = false)
//    private Long historyId;
    @Id
    @Column(name = "rev", nullable = false)
    private Long rev;

    @Column(name = "rev_type")
    private boolean revType;

    @Column(name = "picking_status")
    private Integer pickingStatus;
}
