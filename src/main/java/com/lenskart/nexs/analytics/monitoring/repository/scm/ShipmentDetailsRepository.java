package com.lenskart.nexs.analytics.monitoring.repository.scm;

import org.springframework.data.mongodb.repository.MongoRepository;
import com.lenskart.nexs.analytics.monitoring.entities.mongodb.ShipmentDetails;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ShipmentDetailsRepository extends MongoRepository<ShipmentDetails, String> {
    List<ShipmentDetails> findByShippingPackageIdIn (List<String> shippingPackageIds);
}