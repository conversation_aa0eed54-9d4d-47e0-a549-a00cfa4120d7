package com.lenskart.nexs.analytics.monitoring.processor.impl;

import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.dto.JitOrderStatusDetailsDto;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class JitOrderStatusDetailsProcessor extends MonitorPanelAbstractProcessor {

    @Override
    protected RequestPayload doValidate(RequestPayload requestPayload) throws Exception {

        if(ObjectUtils.isNotEmpty(requestPayload.getJitOrderStatusDetails())){
            return requestPayload;
        }else {
            log.error("[JitOrderStatusDetailsProcessor] : Invalid Jit order item status details");
            throw new Exception("Exception caught while validating jit event message");
        }

    }

    @Override
    protected RequestPayload prepareEventMessage(DebeziumMessage debeziumMessage) throws Exception {
        log.debug("[JitOrderStatusDetails] : debizium message received is : {}", debeziumMessage );
        RequestPayload requestPayload = new RequestPayload();
        try{
            if(null != debeziumMessage && Constants.JIT_ORDER_STATUS_DETAILS_TABLE.equalsIgnoreCase(debeziumMessage.getPayload().getSource().getTable())
                    && debeziumMessage.getPayload().getBefore() != debeziumMessage.getPayload().getAfter()){
                String text = super.gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                JitOrderStatusDetailsDto jitOrderStatusDetailsDto = ObjectHelper.getObjectMapper().readValue(text,JitOrderStatusDetailsDto.class);
                requestPayload.setStatus(jitOrderStatusDetailsDto.getOrderStatus());
                requestPayload.setQueryType(super.getQueryType(jitOrderStatusDetailsDto.getOrderStatus()));
                requestPayload.setOrderItemId(Integer.valueOf(jitOrderStatusDetailsDto.getSourceReferenceId()));
                requestPayload.setWmsOrderCode(jitOrderStatusDetailsDto.getOrderCode().toString());
                requestPayload.setJitOrderStatusDetails(jitOrderStatusDetailsDto);
            }
        }catch (Exception e){
            log.error("Exception caught while creating jit order item event with error : "+ e.getStackTrace());
            throw new Exception("Event message conversion to jit order item failed with exception : "+ e.getStackTrace());
        }
        log.info("[JitOrderStatusDetails] : payload generated is : {}", requestPayload);
        return requestPayload;
    }

    @Override
    protected void executePopulateMonitorPanelData(RequestPayload requestPayload) throws Exception {
        super.executor.doExecute(requestPayload, requestPayload.getQueryType());
    }
}
