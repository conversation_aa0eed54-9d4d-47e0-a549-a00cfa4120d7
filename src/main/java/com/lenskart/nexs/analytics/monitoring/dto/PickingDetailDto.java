package com.lenskart.nexs.analytics.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.analytics.entities.picking.JITType;
import lombok.*;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PickingDetailDto {

    @JsonProperty("assigned_to")
    private String assignedTo;

    @JsonProperty("picking_summary_id")
    private Long pickingSummaryId;

    @JsonProperty("increment_id")
    private Integer incrementId;

    @JsonProperty("wms_order_item_id")
    private Integer wmsOrderItemId;

    @JsonProperty("product_id")
    private Integer productId;

    @JsonProperty("product_name") //TODO removing nullable constraint
    private String productName;

    @JsonProperty("channel")
    private String channel;

    @JsonProperty("product_type")
    private String productType;

    @JsonProperty("order_item_count")
    private Integer orderItemCount;

    @JsonProperty("item_barcode")
    private String itemBarcode;

    @JsonProperty("location_barcode")
    private String locationBarcode;

    @JsonProperty("location_hierarchy")
    private String locationHierarchy;

    @JsonProperty("shipment_id")
    private String shipmentId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("detail")
    private String detail;

    @JsonProperty("box_code")
    private String boxCode;

    @JsonProperty("merged_box_code")
    private String mergedBoxCode;

    @JsonProperty("merged_by")
    private Integer mergedBy;

    @JsonProperty("picklist_order_item_id")
    private Long picklistOrderItemId;

    @JsonProperty("mark_picked_wms")
    private Integer markPickedWms;

    @JsonProperty("mark_picked_ims")
    private Integer markPickedIms;

    @JsonProperty("product_quantity")
    private Integer productQuantity;

    @JsonProperty("order_type")
    private String orderType;

    @JsonProperty("priority")
    private Integer priority;

    @JsonProperty("facility")
    private String facility;

    @JsonProperty("hold_reason")
    private String holdReason;

    @JsonProperty("hold_reason_submitted_at")
    private Date holdReasonSubmittedAt;

    @JsonProperty("skipped_reason")
    private String skippedReason;

    @JsonProperty("duplicate")
    private boolean duplicate = false;

    @JsonProperty("qc_required")
    private boolean qcRequired = false;

    @JsonProperty("fitting_id")
    private Integer fittingId;

    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createdAt;

    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String updatedAt;

    @JsonProperty("product_image")
    private String productImage;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("updated_by")
    private String updatedBy;


    @JsonProperty("skipped_count")
    private Integer skippedCount = 0;

    @JsonProperty("fitting")
    private String fitting;

    @JsonProperty("jit_order")
    @Enumerated(EnumType.ORDINAL)
    private JITType jitOrder;

    @JsonProperty("fast_picking")
    private Boolean fastPicking;

    @JsonProperty("current_location_code")
    private String currentLocationCode;

    @JsonProperty("skipped_by")
    private String skippedBy;

    @JsonProperty("skipped_date")
    private Date skippedDate;
}
