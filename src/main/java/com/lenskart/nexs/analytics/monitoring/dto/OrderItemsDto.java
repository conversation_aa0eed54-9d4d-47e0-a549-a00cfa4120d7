package com.lenskart.nexs.analytics.monitoring.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemHeader;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemMetaData;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemPower;
import com.lenskart.nexs.analytics.monitoring.entities.wms.Orders;
import com.lenskart.nexs.wms.constants.ErrorType;
import com.lenskart.nexs.wms.enums.FulfillableType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class OrderItemsDto{

    @JsonProperty("nexs_order_id")
    private Integer nexsOrderId;

    @JsonProperty("order_item_id")
    private Integer orderItemId;

    @JsonProperty("item_id")
    private Integer itemId;

    @JsonProperty("product_id")
    private Integer productId;


    @JsonProperty("fulfilled_at")
    private String fulfilledAt;


    @JsonProperty("barcode" )
    private String barcode;


    @JsonProperty("holded" )
    private Integer holded;

    @JsonProperty("status" )
    private String status;

    @JsonProperty("wms_order_code")
    private String wmsOrderCode;


    @JsonProperty("expected_dispatch_date")
    private String expectedDispatchDate;

    @JsonProperty("expected_delivery_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String expectedDeliveryDate;

    @JsonProperty("shipping_package_id")
    private String shippingPackageId;

    @JsonProperty("facility_code")
    private String facilityCode;

    @JsonProperty("channel")
    private String channel;

    @JsonProperty("payment_method")
    private String paymentMethod;

    @JsonProperty("nav_channel")
    private String navChannel;

    @JsonProperty("fulfilled_type")
    @Enumerated(EnumType.STRING)
    private FulfillableType fulfillableType;
//    @JsonProperty("item_amount")
//    private BigDecimal itemAmount;

//    @JsonProperty("order_item_header_id")
//    private OrderItemHeader order_item_header_id;

//    @JsonProperty("order_item_id")
//    private List<OrderItemMetaData> orderItemMetaDataList= new ArrayList<OrderItemMetaData>();
    @NotNull
    @JsonProperty("product_delivery_type")
    private String productDeliveryType;

    @JsonProperty("fitting_id")
    private Integer fittingId;

    @JsonProperty("location_id")
    private String locationId;

    @JsonProperty("location_type")
    private String locationType;

    @JsonProperty("fitting_type")
    private String fittingType;

    @JsonProperty("hold")
    private Integer hold;

    @JsonProperty("hold_reason")
    private String holdReason;
    @JsonProperty("power_id")
    private Integer power;

    @JsonProperty("processing_type")
    private String processingType;

    @JsonProperty("item_type")
    private String itemType;

    @JsonProperty("comments")
    private String comments;

    @Version
    private Integer version;

    @JsonProperty("qc_fail_count")
    private int qcFailCount;

    @JsonProperty("operation")
    private String operation;

    //ToDo: Add error Type
    @JsonProperty("error_type")
    @Enumerated(EnumType.STRING)
    private ErrorType errorType;
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    protected String created_at;
    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    protected String updated_at;
    @JsonProperty("enabled")
    protected boolean enabled = true;
    @JsonProperty("created_by")
    protected String created_by = "System";
    @JsonProperty("updated_by")
    protected String updated_by = "System";
}
