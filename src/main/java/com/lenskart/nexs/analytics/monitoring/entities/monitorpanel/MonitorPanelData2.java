package com.lenskart.nexs.analytics.monitoring.entities.monitorpanel;

import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Setter
@Getter
@Entity
@Table(name = "order_data_snapshot2")
public class MonitorPanelData2 extends com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData {



}
