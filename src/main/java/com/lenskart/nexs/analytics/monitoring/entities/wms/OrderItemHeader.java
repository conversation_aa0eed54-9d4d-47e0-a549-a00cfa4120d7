package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;

import com.lenskart.nexs.wms.enums.OrderItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = SqlTableConstants.ORDER_ITEMS_HEADER)
public class OrderItemHeader extends BaseEntity {

    @NotNull
    @Column(name ="wms_order_code",unique = true, columnDefinition = "varchar(20)")
    private String wmsOrderCode;

    @Column(name ="shipping_package_id", columnDefinition = "varchar(30)")
    private String shippingPackageId;

    @Column(name = "verification_required")
    private boolean verificationRequired;

    @Column(name ="status")
    private String status;

    @OneToMany(mappedBy = "order_item_header_id",cascade = {CascadeType.PERSIST})
    private List<OrderItems> orderItemList = new ArrayList<OrderItems>();


    @OneToMany(mappedBy = "order_item_header_id",cascade = {CascadeType.PERSIST})
    private List<Address> addressList= new ArrayList<Address>();

    @Column(name = "ship_to_store_required")
    private Boolean shipToStoreRequired;

    @Column(name = "local_fitting_required")
    private boolean localFittingRequired;

    @Column(name = "order_type_flag")
    private String orderTypeFlag;

    @Column(name = "order_item_type")
    @Enumerated(EnumType.STRING)
    private OrderItemType orderItemType;

    @Version
    private Integer version;

}
