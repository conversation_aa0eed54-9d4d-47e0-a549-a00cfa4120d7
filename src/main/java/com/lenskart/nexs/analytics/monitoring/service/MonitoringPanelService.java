package com.lenskart.nexs.analytics.monitoring.service;

import com.lenskart.nexs.analytics.dao.CacheDAO;
import com.lenskart.nexs.analytics.model.request.MonitorPanelFilters;
import com.lenskart.nexs.analytics.model.request.MonitorPanelRangeFilters;
import com.lenskart.nexs.analytics.model.request.RangeFilter;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.MonitorPanelDataCoulmnNameConstants;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData1;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData2;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.mapper.*;
import com.lenskart.nexs.analytics.monitoring.pojo.DetailPageDto;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelData1Repository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelData2Repository;
import com.lenskart.nexs.analytics.monitoring.repository.read.MonitorPanelNewReadRepository;
import com.lenskart.nexs.analytics.monitoring.request.CustomRangeFilter;
import com.lenskart.nexs.analytics.monitoring.request.DetailPageRequest;
import com.lenskart.nexs.analytics.monitoring.request.HomePageRequest;
import com.lenskart.nexs.analytics.monitoring.request.ProductDetailsPageRequest;
import com.lenskart.nexs.analytics.monitoring.response.DetailPageResponse;
import com.lenskart.nexs.analytics.monitoring.repository.wms.OrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.response.ManualAsrsWaveResponse;
import com.lenskart.nexs.analytics.monitoring.response.MonitorPanelSummaryResponse;
import com.lenskart.nexs.analytics.monitoring.response.MonitorPanelTagCountResponse;
import com.lenskart.nexs.analytics.monitoring.response.ProductDetailsPageResponse;
import com.lenskart.nexs.analytics.monitoring.utils.DetailsPageUtils;
import com.lenskart.nexs.analytics.monitoring.utils.ListingPageUtils;
import com.lenskart.nexs.analytics.monitoring.utils.ProductDetailsUtils;
import com.lenskart.nexs.analytics.util.MonitorPanelUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import javax.persistence.Tuple;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class MonitoringPanelService {
    @Autowired
    OrderItemRepository orderItemRepository;

    @Autowired
    @Qualifier(Constants.FRTAG_REVERSE_LOOKUP)
    private Map<String, List<String>> tagLookupMap;


    @Autowired
    @Qualifier(Constants.FR1_STATUS_REVERSE_LOOKUP)
    private Map<String, List<String>> fr1statusLookupMap;

    @Autowired
    @Qualifier(Constants.FR0_STATUS_REVERSE_LOOKUP)
    private Map<String, List<String>> fr0statusLookupMap;

    @Autowired
    @Qualifier(Constants.FULFILLABILITY_REVERSE_LOOKUP)
    private Map<String, Integer> fbLookupMap;

    @Autowired
    @Qualifier(Constants.FULFILLABILITY_REVERSE_LOOKUP_V3)
    private Map<String, Integer> fbLookupMapV3;

//    @Autowired
//    @Qualifier(Constants.OTHER_FR_TAG_LIST)
//    public List<String> otherFrTagList;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MonitorPanelDataAccessService<MonitorPanelData1> monitorPanelData1AccessService;

    @Autowired
    private MonitorPanelDataAccessService<MonitorPanelData2> monitorPanelData2AccessService;

//    @Autowired
//    private MonitorPanelDataAccessService<MonitorPanelNew> monitorPanelDataAccessService;

    @Autowired
    private MonitorPanelDataAccessService<MonitorPanelNew> monitorPanelDataAccessService;

    @Autowired
    private MonitorPanelDataReadAccessService<MonitorPanelNew> monitorPanelDataReadAccessService;

    @Autowired
    private CacheDAO cacheDAO;

    @Autowired
    private MonitorPanelData1Repository monitorPanelData1Repository;

    @Autowired
    private MonitorPanelData2Repository monitorPanelData2Repository;

    @Autowired
    @Qualifier("monitorPanelNewReadRepository")
    private MonitorPanelNewReadRepository monitorPanelNewReadRepository;


    /**
     * root method to generate the monitoring panel listing page
     * @return
     */

    public Map<String,Object> generateMonitoringPanelListingPage(String facilityCode){

        Map<String,Object> monitoringPanelMap=new HashMap<>();

        List<Map<String,Object>> allflattenedEntries=new ArrayList<>();
        Map<String,Map<String,Long>> fbStatusMap=prepareFbStatusMap();
        Map<String,Long> headerMap=prepareHeadersMap();
        Map<String,List<Map<String,Object>>> fMap=prepareFullfilabilityMaps();



        //lets first figure out fulfillable ones
        allflattenedEntries.addAll(getOrderItemCountByFrTag(0,fbStatusMap, facilityCode));
        //lets first figure non fulfillable ones
        allflattenedEntries.addAll(getOrderItemCountByFrTag(1,fbStatusMap, facilityCode));

        allflattenedEntries.forEach(entry->{

            //check for fulfillabillity tag
            if(entry.containsKey(Constants.FULLFILLABLE_CATEGORY)) {

                //update the fullfilable category received
                String ftag = entry.get(Constants.FULLFILLABLE_CATEGORY).toString();
                Long total = Long.parseLong(entry.get(Constants.TOTAL_KEY).toString());
                Long newTotal= headerMap.get(ftag)+total;
                headerMap.put(ftag,newTotal);

                //update the overall total orders
                String totalorderTag=Constants.FulfillableType.TOTAL_ORDERS.name();
                Long newTotalOrders= headerMap.get(totalorderTag)+total;
                headerMap.put(totalorderTag,newTotalOrders);

                //eventually remove the FULLFILLABLE_CATEGORY key and add to the given panel
                entry.remove(Constants.FULLFILLABLE_CATEGORY);
                fMap.get(ftag).add(entry);

            }


        });

        //appending top level details at fulfillability level

        fbStatusMap.forEach((k,v)->{

            Map<String,Object> tempMap=new HashMap<>();
            if(k.equals(Constants.FulfillableType.TOTAL_ORDERS.name())){

                tempMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
            }
            else{
                tempMap.put(Constants.FR_TAG,k);
                //also add fr tag total to fmap rag
                Map<String,Object> tagMap=new HashMap<>();
                tagMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
                tagMap.putAll(v);
                fMap.get(k).add(tagMap);

            }
            tempMap.putAll(v);
            fMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()).add(tempMap);

        });

        //update header map with columns list
        Set<String> columnList=new LinkedHashSet<>(Constants.fr1_fr2StatusMap.values());

        monitoringPanelMap.put(Constants.COLUMNS_NODE,columnList);
        monitoringPanelMap.put(Constants.HEADERS_NODE,headerMap);
        monitoringPanelMap.put(Constants.REPORT_AS_ON, new Date().toString());
        monitoringPanelMap.putAll(fMap);

        return monitoringPanelMap;

    }

    /**
     * root method to powet the details page of monitoring panel
     * @param detailPageRequest
     * @return
     * @throws Exception
     */
    public DetailPageResponse generateDetailsPage(DetailPageRequest detailPageRequest,String facilityCode) throws Exception {

        List<Pair<String,String>> queryTagsList=new ArrayList<>();
        List<Pair<String,String>> queryNegativeTagsList= new ArrayList<>();
        List<String> queryStatusList=new ArrayList<>();

        //prepare the list of fr tags to be sent to details page query
        if(tagLookupMap.containsKey(detailPageRequest.getFrTag())){
            List<String> tags=tagLookupMap.get(detailPageRequest.getFrTag());
            queryTagsList=DetailsPageUtils.deduceFrTag(tags);
        }else{
            List<String> tempList=new ArrayList();
            //try to make sense of the tag by replacing the space with delimiter
            tempList.add(detailPageRequest.getFrTag().replace(" ",Constants.DELIMITER));
            queryTagsList=DetailsPageUtils.deduceFrTag(tempList);

        }


        //prepare the list of status  to be sent to details page query
        Map<String, List<String>> statusLookupMap= fr1statusLookupMap;
        if(detailPageRequest.getFrTag().toLowerCase().contains(Constants.FR0_TAG_PREFIX)){
            statusLookupMap=fr0statusLookupMap;
        }


        if(statusLookupMap.containsKey(detailPageRequest.getStatus())){
            queryStatusList=statusLookupMap.get(detailPageRequest.getStatus());
        }

        int fbLevel=DetailsPageUtils.fetchFbLevel(fbLookupMap,detailPageRequest.getCategory());
        long totalCount=dataAccessService.getTotalCount(queryTagsList,queryStatusList,fbLevel, facilityCode);
        List<OrderItems> items=dataAccessService.getOrderItems(queryTagsList,queryStatusList,fbLevel,facilityCode, detailPageRequest.getPage(),detailPageRequest.getPageSize());
        List<DetailPageDto> detailPagePojoList= prepareDetailsMap(detailPageRequest.getFrTag(),items);
        return new DetailPageResponse(detailPagePojoList.size(),totalCount,detailPagePojoList, new Date().toString());

    }

    public DetailPageResponse generateJitDetailsPage(DetailPageRequest detailPageRequest , String facilityCode, String source) throws Exception {

        boolean isDateRangeReceived = StringUtils.isBlank(detailPageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().getDate().getStartValue());
        if(!isDateRangeReceived) {
            CustomRangeFilter customRangeFilter = convertDateFromIstToGmt(detailPageRequest.getMonitorPanelFilters());
            detailPageRequest.setCustomRangeFilter(customRangeFilter);
        }

        if (StringUtils.isNotBlank(detailPageRequest.getSeverity())) {
            if (detailPageRequest.getSeverity().equalsIgnoreCase(Constants.Severity.CRITICAL.name())) {
                RangeFilter criticalRangeFilters = generateCriticalStaticFilters();
                detailPageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalRangeFilters);
            } else if (detailPageRequest.getSeverity().equalsIgnoreCase(Constants.Severity.SEVERE.name())) {
                RangeFilter severeRangeFilters = generateSevereStaticFilters();
                detailPageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeRangeFilters);
            }
            log.info("generating date for severity received : {}", detailPageRequest.getSeverity());
            if (detailPageRequest.getSeverity().equalsIgnoreCase(Constants.Severity.CURRENT.name())) {
                detailPageRequest.setMonitorPanelFilters(generateCurrentDateRange(detailPageRequest.getMonitorPanelFilters()));
            } else {
                detailPageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(detailPageRequest.getMonitorPanelFilters()));
            }
        } else {
            log.info("no severity received , generating standard date for standard severity");
            detailPageRequest.setMonitorPanelFilters(generateStandardDateRange(detailPageRequest.getMonitorPanelFilters()));
        }

        String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);

        String[] tableNameAndUpdatedAtList = new String[2];

            if(ObjectUtils.isNotEmpty(tableNameAndUpdatedAt)){
                tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);
            }


        long totalCount;
        List<DetailPageDto> detailPagePojoList = null;
        Pageable pageable = PageRequest.of(detailPageRequest.getPage(), detailPageRequest.getPageSize());

        if (Constants.EVENT_BASED_SOURCE.equalsIgnoreCase(source)) {

            totalCount =
                    monitorPanelNewReadRepository.count(monitorPanelDataReadAccessService.getJitMonitorPanelDetailPageSpec(detailPageRequest,
                    true, facilityCode));
            List<MonitorPanelNew> monitorPanelNewList = monitorPanelNewReadRepository.findAll(monitorPanelDataReadAccessService.getJitMonitorPanelDetailPageSpec(detailPageRequest,
                    true, facilityCode), pageable).getContent();
            detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelDataNew(monitorPanelNewList);
            tableNameAndUpdatedAtList[1]=""+System.currentTimeMillis();
        } else {
            if (cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {

                if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {

                    totalCount = monitorPanelData1Repository.count(monitorPanelData1AccessService.getJitMonitorPanelDetailPageSpec(detailPageRequest,
                            true, facilityCode));
                    List<MonitorPanelData1> monitorPanelData1List = monitorPanelData1Repository.findAll(monitorPanelData1AccessService.getJitMonitorPanelDetailPageSpec(detailPageRequest,
                            true, facilityCode), pageable).getContent();
                    detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelData1(monitorPanelData1List);
                } else {
                    totalCount = monitorPanelData2Repository.count(monitorPanelData2AccessService.getJitMonitorPanelDetailPageSpec(detailPageRequest,
                            true, facilityCode));
                    List<MonitorPanelData2> monitorPanelData2List = monitorPanelData2Repository.findAll(monitorPanelData2AccessService.getJitMonitorPanelDetailPageSpec(detailPageRequest,
                            true, facilityCode), pageable).getContent();
                    detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelData2(monitorPanelData2List);
                }
            } else {
                log.error("Monitor panel data does not exists in tables");
                throw new Exception("Monitor panel data does not exists in tables");
            }
        }

        DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
        Date reportAsOn = new Date(Long.parseLong(tableNameAndUpdatedAtList[1]));

        return new DetailPageResponse(detailPagePojoList.size(), totalCount, detailPagePojoList, formatter.format(reportAsOn));
    }

    /**
     * This method will be used to categorize the data in macro tags Fullfillable,Unfullfillable etc.
     * @param fullfillableFlag
     * @param fbStatusMap
     * @return
     */
    public List<Map<String,Object>> getOrderItemCountByFrTag(int fullfillableFlag,Map<String,Map<String,Long>> fbStatusMap,String facilityCode){
        //get data based on certian status only which are relevant
        List<Tuple> tuples = orderItemRepository.findOrderItemCountByFRTag(Arrays.asList(Constants.UNWANTED_STATUS_LIST.split(",")),fullfillableFlag, facilityCode);
        Map<String , Map<String,Long>> tagStatusMap=new HashMap<>();

        for(int i=0;i<tuples.size();i++) {

            String tag = null;
            if (tuples.get(i).get(Constants.LISTING_PAGE_TAG) != null) {
                String tempTag = tuples.get(i).get(Constants.LISTING_PAGE_TAG).toString();
                if (Constants.tagsMap.containsKey(tempTag)) {
                    tag = Constants.tagsMap.get(tempTag);
                } else {
                    //just replace delimiter with a space and pass on the response
                    tag = tempTag.replace(Constants.DELIMITER, " ");
                }
            }
            if (tag != null) {
                String fbKey = ListingPageUtils.getFulfillabilitytag(tag, fullfillableFlag);

                String status = tuples.get(i).get(Constants.LISTING_PAGE_STATUS).toString();
                String effectiveStatus = ListingPageUtils.getEffectiveStatus(tag, status);
                Long tagStatusCount = (tuples.get(i).get(Constants.LISTING_PAGE_TOTAL_COUNT, BigInteger.class)).longValue();

                if (!StringUtils.isEmpty(effectiveStatus)) {
                    verifyKeyandAppend(tagStatusMap, tag, effectiveStatus, tagStatusCount);
                    //append to fulfillability wise map

                    verifyKeyandAdd(fbStatusMap.get(fbKey), effectiveStatus, tagStatusCount);
                    verifyKeyandAdd(fbStatusMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()), effectiveStatus, tagStatusCount);
                }


            }
        }
        log.info("master map {}",tagStatusMap);
        return flattenandAggregate(tagStatusMap,fullfillableFlag);

    }


    public List<ProductDetailsPageResponse> generatePidDetailsPage(ProductDetailsPageRequest productDetailsPageRequest,String source) throws Exception {
        List<ProductDetailsPageResponse> productDetailsPageResponseList = new ArrayList<>();
        List<Tuple> tuples;
        List<Tuple> total;

        String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
        String[] tableNameAndUpdatedAtList = new String[2];
        if(ObjectUtils.isNotEmpty(tableNameAndUpdatedAt)){
            tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);
        }
        MonitorPanelFilters monitorPanelFilters = new MonitorPanelFilters();
        MonitorPanelRangeFilters monitorPanelRangeFilters = new MonitorPanelRangeFilters();
        RangeFilter rangeFilter = new RangeFilter();
        monitorPanelRangeFilters.setDate(rangeFilter);
        monitorPanelFilters.setMonitorPanelRangeFilters(monitorPanelRangeFilters);
        monitorPanelFilters = generateStandardDateRange(monitorPanelFilters);

        if(Constants.EVENT_BASED_SOURCE.equalsIgnoreCase(source)){
            tuples = monitorPanelNewReadRepository.getFacilityBasedProductDetails(productDetailsPageRequest.getPid(),
                    productDetailsPageRequest.getOrderType(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
            total =  monitorPanelNewReadRepository.getProductCountForAllFacilities(productDetailsPageRequest.getPid(), productDetailsPageRequest.getOrderType(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
            tableNameAndUpdatedAtList[1] = ""+System.currentTimeMillis();
        }else{
            if(cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {
                if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {
                    tuples = monitorPanelData1Repository.getFacilityBasedProductDetails(productDetailsPageRequest.getPid(), productDetailsPageRequest.getOrderType(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
                    total =  monitorPanelData1Repository.getProductCountForAllFacilities(productDetailsPageRequest.getPid(), productDetailsPageRequest.getOrderType(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
                } else {
                    tuples = monitorPanelData2Repository.getFacilityBasedProductDetails(productDetailsPageRequest.getPid(), productDetailsPageRequest.getOrderType(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
                    total =  monitorPanelData2Repository.getProductCountForAllFacilities(productDetailsPageRequest.getPid(), productDetailsPageRequest.getOrderType(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
                }
                log.info("[MonitoringPanelService] -> pidDetailsPage : pid details response received is : ", tuples.toString());
            } else {
                log.error("Monitor panel data does not exists in tables");
                throw new Exception("Monitor panel data does not exists in tables");
            }
        }
        return ProductDetailsUtils.detailsPageMapper(tuples, total, productDetailsPageResponseList);
    }


    /**
     * Prepares the final response for details page once we are done querying order items based on parameters passed by UI.
     * @param frTag
     * @param orderItems
     * @return
     */
    private List<DetailPageDto> prepareDetailsMap(String frTag, List<OrderItems> orderItems){
        //Map<Long, List<DetailPagePojo>> detailsMap=new HashMap<>();
        List<DetailPageDto> detailsList=new ArrayList<>();
        orderItems.forEach(entry->{
//          Long nexsOrderId=entry.getNexsOrderId().getId();
//
//          if(nexsOrderId!=null) {
//              if (!detailsMap.containsKey(nexsOrderId)) {
//                  detailsMap.put(nexsOrderId, new ArrayList<>());
//              }
//              detailsMap.get(nexsOrderId).add(mapOrderItemDetailsPojo(frTag, entry));
//
//          }
            detailsList.add(DetailsPageUtils.mapOrderItemDetailsPojo(frTag, entry));
        });
        return detailsList;
    }



    private Map<String,Long> prepareHeadersMap(){
        Map<String,Long> headerMap=new HashMap<>();
        EnumSet.allOf(Constants.FulfillableType.class)
                .forEach(type -> headerMap.put(type.name(),0L));
        return headerMap;
    }

    private Map<String,Map<String,Long>> prepareFbStatusMap(){
        Map<String,Map<String,Long>> fMap=new HashMap<>();
        EnumSet.allOf(Constants.FulfillableType.class)
                .forEach(type -> fMap.put(type.name(),new HashMap<>()));
        return fMap;
    }

    private Map<String, List<Map<String,Object>>> prepareFullfilabilityMaps(){
        Map<String,List<Map<String,Object>>> fMap=new HashMap<>();
        EnumSet.allOf(Constants.FulfillableType.class)
                .forEach(type -> fMap.put(type.name(),new ArrayList<>()));
        return fMap;
    }

    private Map<String,Long> prepareHeadersMapV3(){
        Map<String,Long> headerMap=new HashMap<>();
        EnumSet.allOf(Constants.FulfillableTypeV3.class)
                .forEach(type -> headerMap.put(type.name(),0L));
        return headerMap;
    }
    private Map<String,Map<String,Long>> prepareFbStatusMapV3(){
        Map<String,Map<String,Long>> fMap=new HashMap<>();
        EnumSet.allOf(Constants.FulfillableTypeV3.class)
                .forEach(type -> fMap.put(type.name(),new HashMap<>()));
        return fMap;
    }

    private Map<String, List<Map<String,Object>>> prepareFullfilabilityMapsV3(){
        Map<String,List<Map<String,Object>>> fMap=new HashMap<>();
        EnumSet.allOf(Constants.FulfillableTypeV3.class)
                .forEach(type -> fMap.put(type.name(),new ArrayList<>()));
        return fMap;
    }

    /**
     * once we have maps grouped by frtag derive the fullfillable category and attach the frtag and fullfillable category to the value json.
     * @param tagStatusMap
     * @param fullfillableFlag
     * @return
     */
    private List<Map<String,Object>> flattenandAggregate(Map<String,Map<String,Long>> tagStatusMap, int fullfillableFlag){

        List<Map<String,Object>> jsonList=new ArrayList<>();
        tagStatusMap.forEach((k,v)->{
            Map<String,Object> jsonMap=new HashMap<>(v);
            String fbCategory=ListingPageUtils.getFulfillabilitytag(k,fullfillableFlag);
            jsonMap.put(Constants.FR_TAG,k);
            jsonMap.put(Constants.FULLFILLABLE_CATEGORY,fbCategory);
            log.info("json {}",jsonMap);
            jsonList.add(jsonMap);

        });
        return jsonList;

    }

    private List<Map<String,Object>> flattenandAggregateV3(Map<String,Map<String,Long>> tagStatusMap, int fullfillableFlag){

        List<Map<String,Object>> jsonList=new ArrayList<>();
        tagStatusMap.forEach((k,v)->{
            Map<String,Object> jsonMap=new HashMap<>(v);
            String fbCategory=ListingPageUtils.getFulfillabilitytagV3(k,fullfillableFlag);
            jsonMap.put(Constants.FR_TAG,k);
            jsonMap.put(Constants.FULLFILLABLE_CATEGORY,fbCategory);
            log.info("json {}",jsonMap);
            jsonList.add(jsonMap);

        });
        return jsonList;

    }

    private void verifyKeyandAppend(Map<String,Map<String,Long>> map,String masterKey,String childKey,Long value){
        Map<String,Long> childMap;
        if(map.containsKey(masterKey)){
            childMap=map.get(masterKey);
        }
        else{
            childMap=new HashMap<>();
            map.put(masterKey,childMap);

        }

        verifyKeyandAdd(childMap,childKey,value);
        verifyKeyandAdd(childMap,Constants.TOTAL_KEY,value);
    }


    private  void verifyKeyandAdd(Map<String,Long> map,String key,Long value){

        if(map.containsKey(key)){
            Long val=map.get(key)+value;
            map.put(key,val);
        }
        else{
            map.put(key,value);
        }
    }

    public Map<String, Object> generateMonitoringPanelListingPage(HomePageRequest homePageRequest, String facilityCode) throws Exception {

        if(cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {
            String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
            String[] tableNameAndUpdatedAtList = new String[2];
            if(ObjectUtils.isNotEmpty(tableNameAndUpdatedAt)) {
                tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);
            }

            Map<String,Object> monitoringPanelMap=new HashMap<>();

            List<Map<String,Object>> allflattenedEntries=new ArrayList<>();
            Map<String,Map<String,Long>> fbStatusMap=prepareFbStatusMap();
            Map<String,Long> headerMap=prepareHeadersMap();
            Map<String,List<Map<String,Object>>> fMap=prepareFullfilabilityMaps();

            if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {
                //lets first figure out fulfillable ones
                List<Tuple> tuples = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpec(homePageRequest, 0, MonitorPanelData1.class, facilityCode);
                allflattenedEntries.addAll(getOrderItemCountByFrTagV2(tuples, fbStatusMap, 0));

                //lets first figure non fulfillable ones
                tuples = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpec(homePageRequest, 1, MonitorPanelData1.class, facilityCode);
                allflattenedEntries.addAll(getOrderItemCountByFrTagV2(tuples, fbStatusMap, 1));
            } else {
                //lets first figure out fulfillable ones
                List<Tuple> tuples = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpec(homePageRequest, 0, MonitorPanelData2.class, facilityCode);
                allflattenedEntries.addAll(getOrderItemCountByFrTagV2(tuples, fbStatusMap, 0));

                //lets first figure non fulfillable ones
                tuples = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpec(homePageRequest, 1, MonitorPanelData2.class, facilityCode);
                allflattenedEntries.addAll(getOrderItemCountByFrTagV2(tuples, fbStatusMap, 1));
            }

            allflattenedEntries.forEach(entry->{

                //check for fulfillabillity tag
                if(entry.containsKey(Constants.FULLFILLABLE_CATEGORY)) {

                    //update the fullfilable category received
                    String ftag = entry.get(Constants.FULLFILLABLE_CATEGORY).toString();
                    Long total = Long.parseLong(entry.get(Constants.TOTAL_KEY).toString());
                    Long newTotal= headerMap.get(ftag)+total;
                    headerMap.put(ftag,newTotal);

                    //update the overall total orders
                    String totalorderTag=Constants.FulfillableType.TOTAL_ORDERS.name();
                    Long newTotalOrders= headerMap.get(totalorderTag)+total;
                    headerMap.put(totalorderTag,newTotalOrders);

                    //eventually remove the FULLFILLABLE_CATEGORY key and add to the given panel
                    entry.remove(Constants.FULLFILLABLE_CATEGORY);
                    fMap.get(ftag).add(entry);

                }


            });

            //appending top level details at fulfillability level

            fbStatusMap.forEach((k,v)->{

                Map<String,Object> tempMap=new HashMap<>();
                if(k.equals(Constants.FulfillableType.TOTAL_ORDERS.name())){

                    tempMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
                }
                else{
                    tempMap.put(Constants.FR_TAG,k);
                    //also add fr tag total to fmap rag
                    Map<String,Object> tagMap=new HashMap<>();
                    tagMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
                    tagMap.putAll(v);
                    fMap.get(k).add(tagMap);

                }
                tempMap.putAll(v);
                fMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()).add(tempMap);

            });

            //update header map with columns list
            Set<String> columnList=new LinkedHashSet<>(Constants.fr1_fr2StatusMap.values());

            monitoringPanelMap.put(Constants.COLUMNS_NODE,columnList);
            monitoringPanelMap.put(Constants.HEADERS_NODE,headerMap);
            DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
            Date reportAsOn = new Date(Long.parseLong(tableNameAndUpdatedAtList[1]));
            monitoringPanelMap.put(Constants.REPORT_AS_ON, formatter.format(reportAsOn));
            monitoringPanelMap.putAll(fMap);

            return monitoringPanelMap;
        } else {
            log.error("Monitor panel data does not exists in tables");
            throw new Exception("Monitor panel data does not exists in tables");
        }
    }

    public List<Map<String,Object>> getOrderItemCountByFrTagV2(List<Tuple> tuples, Map<String,Map<String,Long>> fbStatusMap, int isFulfillable){

        Map<String , Map<String,Long>> tagStatusMap=new HashMap<>();

        for(int i=0;i<tuples.size();i++) {

            String tag = tuples.get(i).get(Constants.LISTING_PAGE_TAG).toString();

            if (tag != null) {
                String fbKey = ListingPageUtils.getFulfillabilitytag(tag, isFulfillable);

                String status = tuples.get(i).get(Constants.LISTING_PAGE_STATUS).toString();

                if (!StringUtils.isEmpty(status)) {
                    Long tagStatusCount = tuples.get(i).get(Constants.LISTING_PAGE_TOTAL_COUNT, Long.class);
                    verifyKeyandAppend(tagStatusMap, tag, status, tagStatusCount);
                    //append to fulfillability wise map

                    verifyKeyandAdd(fbStatusMap.get(fbKey), status, tagStatusCount);
                    verifyKeyandAdd(fbStatusMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()), status, tagStatusCount);
                }


            }
        }
        log.info("master map {}",tagStatusMap);
        return flattenandAggregate(tagStatusMap,isFulfillable);

    }


    public DetailPageResponse generateDetailsPageV2(DetailPageRequest detailPageRequest, String facilityCode) throws Exception {
        if(cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {
            String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
            String[] tableNameAndUpdatedAtList = new String[2];

            if(ObjectUtils.isNotEmpty(tableNameAndUpdatedAt)){
                tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);
            }

            int fbLevel = DetailsPageUtils.fetchFbLevel(fbLookupMap, detailPageRequest.getCategory());
            long totalCount;
            List<DetailPageDto> detailPagePojoList = null;
            Pageable pageable = PageRequest.of(detailPageRequest.getPage(), detailPageRequest.getPageSize());

            if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {

                totalCount = monitorPanelData1Repository.count(monitorPanelData1AccessService.getMonitorPanelDetailPageSpec(detailPageRequest,
                        fbLevel, facilityCode));
                List<MonitorPanelData1> monitorPanelData1List = monitorPanelData1Repository.findAll(monitorPanelData1AccessService.getMonitorPanelDetailPageSpec(detailPageRequest,
                        fbLevel, facilityCode), pageable).getContent();
                detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelData1(monitorPanelData1List);
            } else {
                totalCount = monitorPanelData2Repository.count(monitorPanelData2AccessService.getMonitorPanelDetailPageSpec(detailPageRequest,
                        fbLevel, facilityCode));
                List<MonitorPanelData2> monitorPanelData2List = monitorPanelData2Repository.findAll(monitorPanelData2AccessService.getMonitorPanelDetailPageSpec(detailPageRequest,
                        fbLevel, facilityCode), pageable).getContent();
                detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelData2(monitorPanelData2List);
            }

            DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
            Date reportAsOn = new Date(Long.parseLong(tableNameAndUpdatedAtList[1]));

            return new DetailPageResponse(detailPagePojoList.size(), totalCount, detailPagePojoList, formatter.format(reportAsOn));
        } else {
            log.error("Monitor panel data does not exists in tables");
            throw new Exception("Monitor panel data does not exists in tables");
        }


    }


    /**
     *
     * @param homePageRequest
     * @param facilityCode
     * @return
     * @throws Exception
     *
     *  method for generating monitoring panel listing page
     */
    public Map<String, Object> generateMonitoringPanelListingPageV3(HomePageRequest homePageRequest, String facilityCode,String source) throws Exception {


        Map<String, Object> monitoringPanelMap = new HashMap<>();
        List<Map<String, Object>> allflattenedEntriesForCurrentOrders = new ArrayList<>();
        List<Map<String, Object>> allflattenedEntriesForCriticalOrders = new ArrayList<>();
        List<Map<String, Object>> allflattenedEntriesForSevereOrders = new ArrayList<>();
        List<Map<String, Object>> allflattenedEntriesForStandardMap = new ArrayList<>();
        List<Tuple> allDataFromDb = new ArrayList<>();
        Map<String, Map<String, Long>> fbStatusMap = prepareFbStatusMapV3();
        Map<String, Long> headerMap = prepareHeadersMapV3();
        RangeFilter criticalStaticFilters = generateCriticalStaticFilters();
        RangeFilter severeStaticFilters = generateSevereStaticFilters();

        /**
         *  adding below check to skip default map creation in case of custom date range received , to avoid discrepency in data
         */
        boolean isDateRangeReceived = StringUtils.isBlank(homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().getDate().getStartValue());
        if (!isDateRangeReceived) {
            CustomRangeFilter customRangeFilter = convertDateFromIstToGmt(homePageRequest.getMonitorPanelFilters());
            homePageRequest.setCustomRangeFilter(customRangeFilter);
        }

        String[] tableNameAndUpdatedAtList = new String[2];

        if (Constants.EVENT_BASED_SOURCE.equalsIgnoreCase(source)) {
            generateEventBasedMonitorPanelListingPageV3(homePageRequest, allflattenedEntriesForCurrentOrders, allflattenedEntriesForCriticalOrders, allflattenedEntriesForSevereOrders, allflattenedEntriesForStandardMap, allDataFromDb, fbStatusMap, headerMap, monitoringPanelMap, facilityCode, criticalStaticFilters, severeStaticFilters);
            tableNameAndUpdatedAtList[1]=""+System.currentTimeMillis();
        } else {
            log.info("In Listing page v3 method");
            if (cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {
                String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
                tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);//tableNameAndUpdatedAt.split(Constants.DELIMITER);

                if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {
                    log.info("fetching fulfillable items details...");
                    //lets first figure out fulfillable ones
                    homePageRequest.setMonitorPanelFilters(generateCurrentDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple> tuples = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 0, MonitorPanelData1.class, facilityCode);
                    log.info("fulfillable items items received...");
                    allflattenedEntriesForCurrentOrders.addAll(getOrderItemCountByFrTagV3(tuples, fbStatusMap, 0));
                    log.info("fetching non-fulfillable items details...");
                    //lets first figure non fulfillable ones
                    tuples = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 1, MonitorPanelData1.class, facilityCode);
                    log.info("fetching non-fulfillable items details...");
                    allflattenedEntriesForCurrentOrders.addAll(getOrderItemCountByFrTagV3(tuples, fbStatusMap, 1));
                    createCurrentOrdersMap(allflattenedEntriesForCurrentOrders, monitoringPanelMap, headerMap);

                    log.info("fetching fulfillable items details in critical");
                    // for in critical , fulfillable
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple> inProgressResultList = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData1.class, 0, facilityCode, homePageRequest);
                    allflattenedEntriesForCriticalOrders.addAll(getOrderItemCountByFrTagV3(inProgressResultList, fbStatusMap, 0));
                    log.info("fetching non-fulfillable items details in critical");
                    // for in critical, unfulfillable
                    List<Tuple> UnFullfillableinProgressResultList = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData1.class, 1, facilityCode, homePageRequest);
                    allflattenedEntriesForCriticalOrders.addAll(getOrderItemCountByFrTagV3(UnFullfillableinProgressResultList, fbStatusMap, 1));
                    createCriticalOrdersMap(monitoringPanelMap, allflattenedEntriesForCriticalOrders, headerMap);

                    log.info("fetching fulfillable items details in severe");
                    // for severe, fulfillable
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple> oneHourProcessedResultList = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData1.class, 0, facilityCode, homePageRequest);
                    allflattenedEntriesForSevereOrders.addAll(getOrderItemCountByFrTagV3(oneHourProcessedResultList, fbStatusMap, 0));
                    log.info("fetching nons-fulfillable items details in severe");
                    // for severe, unfulfillable
                    List<Tuple> UnFullfillableoneHourProcessedResultList = monitorPanelData1AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData1.class, 1, facilityCode, homePageRequest);
                    allflattenedEntriesForSevereOrders.addAll(getOrderItemCountByFrTagV3(UnFullfillableoneHourProcessedResultList, fbStatusMap, 1));
                    createSevereOrdersMap(monitoringPanelMap, allflattenedEntriesForSevereOrders, headerMap);

                    homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
                    log.info("fetching fulfillable items details for standard");
                    allDataFromDb = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 0, MonitorPanelData1.class, facilityCode);
                    allflattenedEntriesForStandardMap.addAll(getOrderItemCountByFrTagV3(allDataFromDb, fbStatusMap, 0));
                    log.info("fetching non-fulfillable items details for standard");
                    allDataFromDb = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 1, MonitorPanelData1.class, facilityCode);
                    allflattenedEntriesForStandardMap.addAll(getOrderItemCountByFrTagV3(allDataFromDb, fbStatusMap, 1));
                    createStandardMap(allflattenedEntriesForStandardMap, monitoringPanelMap);


                } else {
                    log.info("fetching fulfillable items details...");
                    //lets first figure out fulfillable ones
                    homePageRequest.setMonitorPanelFilters(generateCurrentDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple> tuples = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 0, MonitorPanelData2.class, facilityCode);
                    log.info("fulfillable items items received...");
                    allflattenedEntriesForCurrentOrders.addAll(getOrderItemCountByFrTagV3(tuples, fbStatusMap, 0));
                    log.info("fetching non-fulfillable items details...");
                    //lets first figure non fulfillable ones
                    tuples = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 1, MonitorPanelData2.class, facilityCode);
                    log.info("fetching non-fulfillable items details...");
                    allflattenedEntriesForCurrentOrders.addAll(getOrderItemCountByFrTagV3(tuples, fbStatusMap, 1));
                    createCurrentOrdersMap(allflattenedEntriesForCurrentOrders, monitoringPanelMap, headerMap);

                    log.info("fetching fulfillable items details in critical");
                    // for in critical, fulfillable
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple> inProgressResultList = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData2.class, 0, facilityCode, homePageRequest);
                    allflattenedEntriesForCriticalOrders.addAll(getOrderItemCountByFrTagV3(inProgressResultList, fbStatusMap, 0));
                    log.info("fetching non-fulfillable items details in critical");
                    // for in critical, unfulfillable
                    List<Tuple> UnFullfillableinProgressResultList = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData2.class, 1, facilityCode, homePageRequest);
                    allflattenedEntriesForCriticalOrders.addAll(getOrderItemCountByFrTagV3(UnFullfillableinProgressResultList, fbStatusMap, 1));
                    createCriticalOrdersMap(monitoringPanelMap, allflattenedEntriesForCriticalOrders, headerMap);

                    log.info("fetching fulfillable items details in severe");
                    // for severe, fulfillable
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple> oneHourProcessedResultList = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData2.class, 0, facilityCode, homePageRequest);
                    allflattenedEntriesForSevereOrders.addAll(getOrderItemCountByFrTagV3(oneHourProcessedResultList, fbStatusMap, 0));
                    log.info("fetching nons-fulfillable items details in severe");
                    // for severe, unfulfillable
                    List<Tuple> UnFullfillableoneHourProcessedResultList = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelData2.class, 1, facilityCode, homePageRequest);
                    allflattenedEntriesForSevereOrders.addAll(getOrderItemCountByFrTagV3(UnFullfillableoneHourProcessedResultList, fbStatusMap, 1));
                    createSevereOrdersMap(monitoringPanelMap, allflattenedEntriesForSevereOrders, headerMap);

                    homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
                    log.info("fetching fulfillable items details for standard");
                    allDataFromDb = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 0, MonitorPanelData2.class, facilityCode);
                    allflattenedEntriesForStandardMap.addAll(getOrderItemCountByFrTagV3(allDataFromDb, fbStatusMap, 0));
                    log.info("fetching non-fulfillable items details for standard");
                    allDataFromDb = monitorPanelData2AccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 1, MonitorPanelData2.class, facilityCode);
                    allflattenedEntriesForStandardMap.addAll(getOrderItemCountByFrTagV3(allDataFromDb, fbStatusMap, 1));
                    createStandardMap(allflattenedEntriesForStandardMap, monitoringPanelMap);
                }
            } else {
                log.error("Monitor panel data does not exists in tables");
                throw new Exception("Monitor panel data does not exists in tables");
            }
        }
        //update header map with columns list
        Set<String> columnList = new LinkedHashSet<>(Constants.statusList);
        monitoringPanelMap.put(Constants.HEADERS_NODE, headerMap);
        DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
        Date reportAsOn = new Date(Long.parseLong(tableNameAndUpdatedAtList[1]));
        monitoringPanelMap.put(Constants.REPORT_AS_ON, formatter.format(reportAsOn));
        monitoringPanelMap.put(Constants.COLUMNS_NODE, columnList);
        monitoringPanelMap.put(Constants.FR_TAG_NODE, Constants.frTagList);
        log.info("monitor panel map :{}", monitoringPanelMap);
        return monitoringPanelMap;
    }

    /**
     *  generating event based monitor panel listing page
     * @param homePageRequest
     * @param allflattenedEntriesForCurrentOrders
     * @param allflattenedEntriesForCriticalOrders
     * @param allflattenedEntriesForSevereOrders
     * @param allflattenedEntriesForStandardMap
     * @param allDataFromDb
     * @param fbStatusMap
     * @param headerMap
     * @param monitoringPanelMap
     * @param facilityCode
     * @param criticalStaticFilters
     * @param severeStaticFilters
     * @throws Exception
     */

    public void generateEventBasedMonitorPanelListingPageV3(HomePageRequest homePageRequest, List<Map<String,Object>> allflattenedEntriesForCurrentOrders,List<Map<String,Object>> allflattenedEntriesForCriticalOrders,List<Map<String,Object>> allflattenedEntriesForSevereOrders,List<Map<String,Object>> allflattenedEntriesForStandardMap,List<Tuple> allDataFromDb,Map<String,Map<String,Long>> fbStatusMap,Map<String,Long> headerMap,Map<String,Object> monitoringPanelMap,String facilityCode,RangeFilter criticalStaticFilters,RangeFilter severeStaticFilters) throws Exception {

        log.info("fetching fulfillable items details...");
        //lets first figure out fulfillable ones
        homePageRequest.setMonitorPanelFilters(generateCurrentDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple> tuples = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 0,
                MonitorPanelNew.class, facilityCode);
        log.info("fulfillable items items received...");
        allflattenedEntriesForCurrentOrders.addAll(getOrderItemCountByFrTagV3(tuples, fbStatusMap, 0));
        log.info("fetching non-fulfillable items details...");
        //lets first figure non fulfillable ones
        tuples = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 1, MonitorPanelNew.class, facilityCode);
        log.info("fetching non-fulfillable items details...");
        allflattenedEntriesForCurrentOrders.addAll(getOrderItemCountByFrTagV3(tuples, fbStatusMap, 1));
        createCurrentOrdersMap(allflattenedEntriesForCurrentOrders, monitoringPanelMap, headerMap);

        log.info("fetching fulfillable items details in critical");
        // for in critical, fulfillable
        homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalStaticFilters);
        homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple> inProgressResultList = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelNew.class, 0, facilityCode, homePageRequest);
        allflattenedEntriesForCriticalOrders.addAll(getOrderItemCountByFrTagV3(inProgressResultList, fbStatusMap, 0));
        log.info("fetching non-fulfillable items details in critical");
        // for in critical, unfulfillable
        List<Tuple> UnFullfillableinProgressResultList = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelNew.class, 1, facilityCode, homePageRequest);
        allflattenedEntriesForCriticalOrders.addAll(getOrderItemCountByFrTagV3(UnFullfillableinProgressResultList, fbStatusMap, 1));
        createCriticalOrdersMap(monitoringPanelMap, allflattenedEntriesForCriticalOrders, headerMap);

        log.info("fetching fulfillable items details in severe");
        // for severe, fulfillable
        homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeStaticFilters);
        homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple> oneHourProcessedResultList = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelNew.class, 0, facilityCode, homePageRequest);
        allflattenedEntriesForSevereOrders.addAll(getOrderItemCountByFrTagV3(oneHourProcessedResultList, fbStatusMap, 0));
        log.info("fetching nons-fulfillable items details in severe");
        // for severe, unfulfillable
        List<Tuple> UnFullfillableoneHourProcessedResultList = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3SeverityFilters(MonitorPanelNew.class, 1, facilityCode, homePageRequest);
        allflattenedEntriesForSevereOrders.addAll(getOrderItemCountByFrTagV3(UnFullfillableoneHourProcessedResultList, fbStatusMap, 1));
        createSevereOrdersMap(monitoringPanelMap, allflattenedEntriesForSevereOrders, headerMap);

        homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
        log.info("fetching fulfillable items details for standard");
        allDataFromDb = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 0, MonitorPanelNew.class, facilityCode);
        allflattenedEntriesForStandardMap.addAll(getOrderItemCountByFrTagV3(allDataFromDb, fbStatusMap, 0));
        log.info("fetching non-fulfillable items details for standard");
        allDataFromDb = monitorPanelDataReadAccessService.getMonitorPanelHomePageDataSpecV3(homePageRequest, 1, MonitorPanelNew.class, facilityCode);
        allflattenedEntriesForStandardMap.addAll(getOrderItemCountByFrTagV3(allDataFromDb, fbStatusMap, 1));
        createStandardMap(allflattenedEntriesForStandardMap, monitoringPanelMap);
    }

    /**
     *  method for creating jit monitor panel
     * @param homePageRequest
     * @param facilityCode
     * @return
     * @throws Exception
     */

    public Map<String, Object> generateJitMonitorPanelHomePageRequest(HomePageRequest homePageRequest, String facilityCode,String source) throws Exception{
        log.info("Generating home page request for jit");

        String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
        String[] tableNameAndUpdatedAtList = new String[2];

        if(ObjectUtils.isNotEmpty(tableNameAndUpdatedAt)){
            tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);
        }

        Map<String,Object> monitoringPanelMap=new HashMap<>();
        Long headerCount = 0L;
        RangeFilter criticalStaticFilters = generateCriticalStaticFilters();
        RangeFilter severeStaticFilters = generateSevereStaticFilters();

        boolean isDateRangeReceived = StringUtils.isBlank(homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().getDate().getStartValue());
        if(!isDateRangeReceived) {
            CustomRangeFilter customRangeFilter = convertDateFromIstToGmt(homePageRequest.getMonitorPanelFilters());
            homePageRequest.setCustomRangeFilter(customRangeFilter);
        }


        if(Constants.EVENT_BASED_SOURCE.equalsIgnoreCase(source)){
            generateEventBasedJitMonitorPanelHomePageRequest(homePageRequest,monitoringPanelMap,criticalStaticFilters,severeStaticFilters,facilityCode, headerCount);
            tableNameAndUpdatedAtList[1]=""+System.currentTimeMillis();
        }else{
            if(cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {
                if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {


                    log.info("generating current map for standard");
                    homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>standardList = monitorPanelData1AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData1.class,1);
                    generateJitMap(standardList, monitoringPanelMap,Constants.STANDARD, headerCount);

                    log.info("generating current map for current");
                    homePageRequest.setMonitorPanelFilters(generateCurrentDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>currentList = monitorPanelData1AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData1.class,1);
                    generateJitMap(currentList, monitoringPanelMap,Constants.CURRENT, headerCount);


                    log.info("generating current map for critical");
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>criticalList = monitorPanelData1AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData1.class,1);
                    generateJitMap(criticalList, monitoringPanelMap,Constants.CRITICAL, headerCount);

                    log.info("generating current map for severe");
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>severeList = monitorPanelData1AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData1.class,1);
                    generateJitMap(severeList, monitoringPanelMap,Constants.SEVERE, headerCount);

                } else{
                    log.info("generating current map for standard");
                    homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>standardList = monitorPanelData2AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData2.class,1);
                    generateJitMap(standardList, monitoringPanelMap,Constants.STANDARD, headerCount);

                    log.info("generating current map for current");
                    homePageRequest.setMonitorPanelFilters(generateCurrentDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>currentList = monitorPanelData2AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData2.class,1);
                    generateJitMap(currentList, monitoringPanelMap,Constants.CURRENT, headerCount);

                    log.info("generating current map for critical");
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>criticalList = monitorPanelData2AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData2.class,1);
                    generateJitMap(criticalList, monitoringPanelMap,Constants.CRITICAL, headerCount);

                    log.info("generating current map for severe");
                    homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeStaticFilters);
                    homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
                    List<Tuple>severeList = monitorPanelData2AccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelData2.class,1);
                    generateJitMap(severeList, monitoringPanelMap,Constants.SEVERE, headerCount);
                }
            } else {
                log.error("Monitor panel data does not exists in tables");
                throw new Exception("Monitor panel data does not exists in tables");
            }
        }
        Map<String, List<String>> columnMap = new HashMap<>();
        columnMap.put(Constants.LENS_LAB, Constants.lensLabStatusList);
        columnMap.put(Constants.JIT_EXTERNAL_VENTOR, Constants.externalVendorStatusList);
        monitoringPanelMap.put(Constants.COLUMNS_NODE, columnMap);
        DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
        Date reportAsOn = new Date(Long.parseLong(tableNameAndUpdatedAtList[1]));
        monitoringPanelMap.put(Constants.REPORT_AS_ON, formatter.format(reportAsOn));
        monitoringPanelMap.put(Constants.FR_TAG_NODE,Constants.jitTagList);
        log.info("monitor panel map :{}", monitoringPanelMap);
        return monitoringPanelMap;
    }

    /**
     * fetching jit monitor panel data based on event
     * @param homePageRequest
     * @param monitoringPanelMap
     * @param criticalStaticFilters
     * @param severeStaticFilters
     * @param facilityCode
     * @param headerCount
     * @throws Exception
     */
    public void generateEventBasedJitMonitorPanelHomePageRequest(HomePageRequest homePageRequest,Map<String,Object> monitoringPanelMap,RangeFilter criticalStaticFilters,RangeFilter severeStaticFilters,String facilityCode, Long headerCount) throws Exception {
        log.info("generating current map for standard");
        homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple>standardList = monitorPanelDataReadAccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelNew.class,1);
        generateJitMap(standardList, monitoringPanelMap,Constants.STANDARD, headerCount);

        log.info("generating current map for current");
        homePageRequest.setMonitorPanelFilters(generateCurrentDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple>currentList = monitorPanelDataReadAccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelNew.class,1);
        generateJitMap(currentList, monitoringPanelMap,Constants.CURRENT, headerCount);

        log.info("generating current map for critical");
        homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalStaticFilters);
        homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple>criticalList = monitorPanelDataReadAccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelNew.class,1);
        generateJitMap(criticalList, monitoringPanelMap,Constants.CRITICAL, headerCount);

        log.info("generating current map for severe");
        homePageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeStaticFilters);
        homePageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(homePageRequest.getMonitorPanelFilters()));
        List<Tuple>severeList = monitorPanelDataReadAccessService.getJitMonitorPanelHomePage(homePageRequest, facilityCode, MonitorPanelNew.class,1);
        generateJitMap(severeList, monitoringPanelMap,Constants.SEVERE, headerCount);
    }

    public RangeFilter generateCriticalStaticFilters(){
        RangeFilter rangeFilter = new RangeFilter();
        rangeFilter.setStartValue(Constants.CRITICAL_START);
        rangeFilter.setEndValue(Constants.CRITICAL_END);
        return rangeFilter;
    }

    public RangeFilter generateSevereStaticFilters(){
        RangeFilter rangeFilter = new RangeFilter();
        rangeFilter.setStartValue(Constants.SEVERE_START);
        rangeFilter.setEndValue(Constants.SEVERE_END);
        return rangeFilter;
    }

    public CustomRangeFilter convertDateFromIstToGmt(MonitorPanelFilters monitorPanelFilters) throws Exception {


        SimpleDateFormat converter = new SimpleDateFormat(Constants.DATE_FILTER_FORMAT);
        if(StringUtils.isBlank(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone())){
            converter.setTimeZone(TimeZone.getTimeZone(Constants.DEFAULT_TIME_ZONE));
        }else {
            converter.setTimeZone(TimeZone.getTimeZone(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone()));
        }
        Date startDateTime = converter.parse(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue());
        Date endDateTime = converter.parse(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());

        log.info("start date and time :{} ", startDateTime);
        log.info("end date and time :{} ", endDateTime);

        SimpleDateFormat  gmtConverter = new SimpleDateFormat(Constants.DATE_FILTER_FORMAT);

        gmtConverter.setTimeZone(TimeZone.getTimeZone(Constants.GMT_TIME));

        String gmtConvertedStartDate = gmtConverter.format(startDateTime);
        String gmtConvertedEndDate = gmtConverter.format(endDateTime);

        log.info("gmt converted start date and time :{} ", gmtConvertedStartDate);
        log.info("gmt converted end date and time :{} ", gmtConvertedEndDate);

        CustomRangeFilter customRangeFilter = new CustomRangeFilter();
        customRangeFilter.setStartValue(gmtConvertedStartDate);
        customRangeFilter.setEndValue(gmtConvertedEndDate);
        return customRangeFilter;
    }

    /**
     *  below method updates the date range in the monitor panel filter based on the ageing parameters.
     * @param monitorPanelFilters
     * @return
     */
    public MonitorPanelFilters generateCriticalAndSevereDateRange(MonitorPanelFilters monitorPanelFilters){
        String startDateTime ;
        String endDateTime ;

        LocalDate date;
        LocalDate startDate;
        LocalDate endDate;

        if(StringUtils.isBlank(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone())){
            date = LocalDate.now(ZoneId.of(Constants.DEFAULT_TIME_ZONE));
        }else {
            date = LocalDate.now(ZoneId.of(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone()));
        }
        log.info(" current time for zone :{}  is :{}",monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone(), date);

        if(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getEndValue().equals("-1")){
            startDate = date.minusDays(Constants.ARBITRARY_END_DAY);
        }else {
            startDate = date.minusDays(Integer.parseInt(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getEndValue())/24);
        }

        endDate = date.minusDays(Integer.parseInt(monitorPanelFilters.getMonitorPanelRangeFilters().getAgeingSinceCreated().getStartValue())/24);
        startDateTime = startDate+" "+Constants.START_TIME;
        endDateTime =  endDate+" "+Constants.END_TIME;
        log.info("start date :{} and end date :{} for critical and severe range filters", startDateTime, endDateTime);
        monitorPanelFilters.getMonitorPanelRangeFilters().getDate().setEndValue(endDateTime);
        monitorPanelFilters.getMonitorPanelRangeFilters().getDate().setStartValue(startDateTime);
        monitorPanelFilters.getMonitorPanelRangeFilters().setAgeingSinceCreated(null);
        monitorPanelFilters.getMonitorPanelRangeFilters().setAgeingSinceLastUpdate(null);
        log.info(" start date :{} and end date :{} in range filters ", monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue(),monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue());
        return monitorPanelFilters;
    }

    public MonitorPanelFilters generateStandardDateRange(MonitorPanelFilters monitorPanelFilters) throws ParseException {
        log.info("setting date range for standard map");
        String startDateTime ;
        String endDateTime ;

        LocalDate date;
        LocalDate startDate;

        if(StringUtils.isBlank(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone())){
            date = LocalDate.now(ZoneId.of(Constants.DEFAULT_TIME_ZONE));
        }else {
            date = LocalDate.now(ZoneId.of(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone()));
        }
        log.info(" current standard time for zone :{}  is :{}",monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone(), date);

        startDate = date.minusDays(Constants.ARBITRARY_END_DAY);

        startDateTime = startDate+" "+Constants.START_TIME;
        SimpleDateFormat converter = new SimpleDateFormat(Constants.DATE_FILTER_FORMAT);
        Date endDate= converter.parse(converter.format(new Date()));
        SimpleDateFormat  gmtConverter = new SimpleDateFormat(Constants.DATE_FILTER_FORMAT);
        gmtConverter.setTimeZone(TimeZone.getTimeZone(Constants.GMT_TIME));

        endDateTime = gmtConverter.format(endDate);
        log.info("start date :{} and end date :{} for Standard range filters", startDateTime, endDateTime);
        monitorPanelFilters.getMonitorPanelRangeFilters().getDate().setEndValue(endDateTime);
        monitorPanelFilters.getMonitorPanelRangeFilters().getDate().setStartValue(startDateTime);
        monitorPanelFilters.getMonitorPanelRangeFilters().setAgeingSinceCreated(null);
        monitorPanelFilters.getMonitorPanelRangeFilters().setAgeingSinceLastUpdate(null);
        log.info(" start date :{} and end date :{} for standard in range filters ", monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getEndValue(), monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getStartValue());
        return monitorPanelFilters;
    }

    public MonitorPanelFilters generateCurrentDateRange(MonitorPanelFilters monitorPanelFilters) throws ParseException {

        LocalDate date;
        log.info(" generating standard date range");
        SimpleDateFormat converter = new SimpleDateFormat(Constants.DATE_FILTER_FORMAT);
        if(StringUtils.isBlank(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone())){
            date = LocalDate.now(ZoneId.of(Constants.DEFAULT_TIME_ZONE));
        }else {
            date = LocalDate.now(ZoneId.of(monitorPanelFilters.getMonitorPanelRangeFilters().getDate().getTimeZone()));
        }


        String startDate = date.minusDays(1)+" "+Constants.START_TIME;
        Date endDate= converter.parse(converter.format(new Date()));

        SimpleDateFormat  gmtConverter = new SimpleDateFormat(Constants.DATE_FILTER_FORMAT);
        gmtConverter.setTimeZone(TimeZone.getTimeZone(Constants.GMT_TIME));

        String gmtConvertedEndDate = gmtConverter.format(endDate);

        monitorPanelFilters.getMonitorPanelRangeFilters().getDate().setStartValue(startDate);
        monitorPanelFilters.getMonitorPanelRangeFilters().getDate().setEndValue(gmtConvertedEndDate);
        monitorPanelFilters.getMonitorPanelRangeFilters().setAgeingSinceCreated(null);
        monitorPanelFilters.getMonitorPanelRangeFilters().setAgeingSinceLastUpdate(null);
        log.info("start date :{} and end date :{} current for range filters", startDate, gmtConvertedEndDate);
        return monitorPanelFilters;
    }

    /**
     *
     * @param detailPageRequest
     * @param facilityCode
     * @return
     * @throws Exception
     *
     * Method for generating details page response
     */
    public DetailPageResponse generateDetailsPageV3(DetailPageRequest detailPageRequest, String facilityCode,String source) throws Exception {


        boolean isDateRangeReceived = StringUtils.isBlank(detailPageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().getDate().getStartValue());
        if(!isDateRangeReceived) {
            CustomRangeFilter customRangeFilter = convertDateFromIstToGmt(detailPageRequest.getMonitorPanelFilters());
            detailPageRequest.setCustomRangeFilter(customRangeFilter);
        }
        if (StringUtils.isNotBlank(detailPageRequest.getSeverity())) {
            if (detailPageRequest.getSeverity().equalsIgnoreCase(Constants.Severity.CRITICAL.name())) {
                RangeFilter criticalRangeFilters = generateCriticalStaticFilters();
                detailPageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(criticalRangeFilters);
            } else if (detailPageRequest.getSeverity().equalsIgnoreCase(Constants.Severity.SEVERE.name())) {
                RangeFilter severeRangeFilters = generateSevereStaticFilters();
                detailPageRequest.getMonitorPanelFilters().getMonitorPanelRangeFilters().setAgeingSinceCreated(severeRangeFilters);
            }
            log.info("generating date for severity received : {}", detailPageRequest.getSeverity());
            if (detailPageRequest.getSeverity().equalsIgnoreCase(Constants.Severity.CURRENT.name())) {
                detailPageRequest.setMonitorPanelFilters(generateCurrentDateRange(detailPageRequest.getMonitorPanelFilters()));
            } else {
                detailPageRequest.setMonitorPanelFilters(generateCriticalAndSevereDateRange(detailPageRequest.getMonitorPanelFilters()));
            }
        } else {
            log.info("no severity received , generating standard date for standard severity");
            detailPageRequest.setMonitorPanelFilters(generateStandardDateRange(detailPageRequest.getMonitorPanelFilters()));
        }

        String tableNameAndUpdatedAt = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
        String[] tableNameAndUpdatedAtList = new String[2];
        if(ObjectUtils.isNotEmpty(tableNameAndUpdatedAt)){
            tableNameAndUpdatedAtList = tableNameAndUpdatedAt.split(Constants.DELIMITER);
        }

        int fbLevel = DetailsPageUtils.fetchFbLevel(fbLookupMapV3, detailPageRequest.getCategory());
        long totalCount = 0L;
        List<DetailPageDto> detailPagePojoList = null;
        Pageable pageable = PageRequest.of(detailPageRequest.getPage(), detailPageRequest.getPageSize());

        if(Constants.EVENT_BASED_SOURCE.equalsIgnoreCase(source)){
            totalCount = monitorPanelNewReadRepository.count(monitorPanelDataReadAccessService.getMonitorPanelDetailPageSpecV3(detailPageRequest,
                    fbLevel, facilityCode));
            List<MonitorPanelNew> monitorPanelNewList = monitorPanelNewReadRepository.findAll(monitorPanelDataReadAccessService.getMonitorPanelDetailPageSpecV3(detailPageRequest,
                    fbLevel, facilityCode), pageable).getContent();
            detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelDataNew(monitorPanelNewList);
            tableNameAndUpdatedAtList[1]=""+System.currentTimeMillis();
        } else {
            if (cacheDAO.hasKey(Constants.MONITOR_TABLE_UPDATED_AT_KEY)) {

                if (tableNameAndUpdatedAtList[0].equals(Constants.MONITOR_TABLE_1)) {

                    totalCount = monitorPanelData1Repository.count(monitorPanelData1AccessService.getMonitorPanelDetailPageSpecV3(detailPageRequest,
                            fbLevel, facilityCode));
                    List<MonitorPanelData1> monitorPanelData1List = monitorPanelData1Repository.findAll(monitorPanelData1AccessService.getMonitorPanelDetailPageSpecV3(detailPageRequest,
                            fbLevel, facilityCode), pageable).getContent();
                    detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelData1(monitorPanelData1List);
                } else {
                    totalCount = monitorPanelData2Repository.count(monitorPanelData2AccessService.getMonitorPanelDetailPageSpecV3(detailPageRequest,
                            fbLevel, facilityCode));
                    List<MonitorPanelData2> monitorPanelData2List = monitorPanelData2Repository.findAll(monitorPanelData2AccessService.getMonitorPanelDetailPageSpecV3(detailPageRequest,
                            fbLevel, facilityCode), pageable).getContent();
                    detailPagePojoList = DetailPageDtoMapper.INSTANCE.mapMonitorPanelData2(monitorPanelData2List);
                }
            } else {
                log.error("Monitor panel data does not exists in tables");
                throw new Exception("Monitor panel data does not exists in tables");
            }
        }
        DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy");
        Date reportAsOn = new Date(Long.parseLong(tableNameAndUpdatedAtList[1]));

        return new DetailPageResponse(detailPagePojoList.size(), totalCount, detailPagePojoList, formatter.format(reportAsOn));
    }


    /**
     *
     * @param tuples
     * @param fbStatusMap
     * @param isFulfillable
     * @return
     *
     * method for getting order item count by fr tag
     */

    public List<Map<String,Object>> getOrderItemCountByFrTagV3(List<Tuple> tuples, Map<String,Map<String,Long>> fbStatusMap, int isFulfillable){
        Map<String , Map<String,Long>> tagStatusMap=new HashMap<>();

        for(int i=0;i<tuples.size();i++) {

            String tag = tuples.get(i).get(Constants.LISTING_PAGE_TAG).toString();

            if (tag != null) {
                String fbKey = ListingPageUtils.getFulfillabilitytagV3(tag, isFulfillable);

                String status = tuples.get(i).get(Constants.LISTING_PAGE_STATUS).toString();
                if (!StringUtils.isEmpty(status)) {
                    Long tagStatusCount = tuples.get(i).get(Constants.LISTING_PAGE_TOTAL_COUNT, Long.class);
                    verifyKeyandAppend(tagStatusMap, tag, status, tagStatusCount);
                    //append to fulfillability wise map

                    verifyKeyandAdd(fbStatusMap.get(fbKey), status, tagStatusCount);
                    verifyKeyandAdd(fbStatusMap.get(Constants.FulfillableTypeV3.TOTAL_ORDERS.name()), status, tagStatusCount);
                }
            }
        }
        log.info("master map {}",tagStatusMap);
        return flattenandAggregateV3(tagStatusMap,isFulfillable);
    }


    /**
     * @param monitoringPanelMap
     * @param allflattenedEntriesForInProgress
     *
     * method for generating map of items in critical state
     */
    public void createCriticalOrdersMap(Map<String,Object> monitoringPanelMap, List<Map<String,Object>> allflattenedEntriesForInProgress, Map<String,Long> headerMap){
        Map<String,Map<String,Long>> fbStatusMap=prepareFbStatusMapV3();
        Map<String,List<Map<String,Object>>> fMap=prepareFullfilabilityMapsV3();



        allflattenedEntriesForInProgress.forEach(entry->{

            //check for fulfillabillity tag
            if(entry.containsKey(Constants.FULLFILLABLE_CATEGORY)) {

                //update the fullfilable category received
                String ftag = entry.get(Constants.FULLFILLABLE_CATEGORY).toString();
                Long total = Long.parseLong(entry.get(Constants.TOTAL_KEY).toString());
                Long newTotal= headerMap.get(ftag)+total;
                headerMap.put(ftag,newTotal);

//                update the overall total orders
                String totalorderTag=Constants.FulfillableType.TOTAL_ORDERS.name();
                Long newTotalOrders= headerMap.get(totalorderTag)+total;
                headerMap.put(totalorderTag,newTotalOrders);

                //eventually remove the FULLFILLABLE_CATEGORY key and add to the given panel
                entry.remove(Constants.FULLFILLABLE_CATEGORY);
                fMap.get(ftag).add(entry);

            }


        });


        //appending top level details at fulfillability level

        fbStatusMap.forEach((k,v)->{

            Map<String,Object> tempMap=new HashMap<>();
            if(k.equals(Constants.FulfillableType.TOTAL_ORDERS.name())){

//                tempMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
            }
            else{
                tempMap.put(Constants.FR_TAG,k);
                //also add fr tag total to fmap rag
                Map<String,Object> tagMap=new HashMap<>();
                tagMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
//                    tagMap.putAll(v);
                fMap.get(k).add(tagMap);

            }
//                tempMap.putAll(v);
//            fMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()).add(tempMap);
        });
        log.info("critical orders map :{}", fMap);
        monitoringPanelMap.put("critical", fMap);

    }


    /**
     *
     * @param monitoringPanelMap
     * @param allflattenedEntriesForOneHourProgress
     *
     * method for generating map of items in severe state
     */
    public void createSevereOrdersMap(Map<String,Object> monitoringPanelMap, List<Map<String,Object>> allflattenedEntriesForOneHourProgress, Map<String,Long> headerMap){
        Map<String,Map<String,Long>> fbStatusMap=prepareFbStatusMapV3();
        Map<String,List<Map<String,Object>>> fMap=prepareFullfilabilityMapsV3();



        allflattenedEntriesForOneHourProgress.forEach(entry->{

            //check for fulfillabillity tag
            if(entry.containsKey(Constants.FULLFILLABLE_CATEGORY)) {

                //update the fullfilable category received
                String ftag = entry.get(Constants.FULLFILLABLE_CATEGORY).toString();
                Long total = Long.parseLong(entry.get(Constants.TOTAL_KEY).toString());
                Long newTotal= headerMap.get(ftag)+total;
                headerMap.put(ftag,newTotal);

                //update the overall total orders
                String totalorderTag=Constants.FulfillableType.TOTAL_ORDERS.name();
                Long newTotalOrders= headerMap.get(totalorderTag)+total;
                headerMap.put(totalorderTag,newTotalOrders);

                //eventually remove the FULLFILLABLE_CATEGORY key and add to the given panel
                entry.remove(Constants.FULLFILLABLE_CATEGORY);
                fMap.get(ftag).add(entry);

            }


        });


        //appending top level details at fulfillability level

        fbStatusMap.forEach((k,v)->{

            Map<String,Object> tempMap=new HashMap<>();
            if(k.equals(Constants.FulfillableType.TOTAL_ORDERS.name())){

//                tempMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
            }
            else{
                tempMap.put(Constants.FR_TAG,k);
                //also add fr tag total to fmap rag
                Map<String,Object> tagMap=new HashMap<>();
                tagMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
//                    tagMap.putAll(v);
                fMap.get(k).add(tagMap);

            }
//                tempMap.putAll(v);
//            fMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()).add(tempMap);
        });
        log.info("severe orders map :{}", fMap);
        monitoringPanelMap.put("severe", fMap);
    }


    /**
     *
     * @param allflattenedEntries
     * @param monitoringPanelMap
     * @param headerMap
     *
     * method for generating map of items in standard state
     */
    public void createCurrentOrdersMap(List<Map<String,Object>> allflattenedEntries, Map<String,Object> monitoringPanelMap, Map<String,Long> headerMap){
        Map<String,Map<String,Long>> fbStatusMap=prepareFbStatusMapV3();
        Map<String,List<Map<String,Object>>> fMap=prepareFullfilabilityMapsV3();

        allflattenedEntries.forEach(entry->{

            //check for fulfillabillity tag
            if(entry.containsKey(Constants.FULLFILLABLE_CATEGORY)) {

                //update the fullfilable category received
                String ftag = entry.get(Constants.FULLFILLABLE_CATEGORY).toString();
                Long total = Long.parseLong(entry.get(Constants.TOTAL_KEY).toString());
                Long newTotal= headerMap.get(ftag)+total;
                headerMap.put(ftag,newTotal);

                //update the overall total orders
                String totalorderTag=Constants.FulfillableType.TOTAL_ORDERS.name();
                Long newTotalOrders= headerMap.get(totalorderTag)+total;
                headerMap.put(totalorderTag,newTotalOrders);

                //eventually remove the FULLFILLABLE_CATEGORY key and add to the given panel
                entry.remove(Constants.FULLFILLABLE_CATEGORY);
                fMap.get(ftag).add(entry);

            }


        });


        //appending top level details at fulfillability level

        fbStatusMap.forEach((k,v)->{

            Map<String,Object> tempMap=new HashMap<>();
            if(k.equals(Constants.FulfillableType.TOTAL_ORDERS.name())){

//                tempMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
            }
            else {
                tempMap.put(Constants.FR_TAG, k);
                //also add fr tag total to fmap rag
                Map<String, Object> tagMap = new HashMap<>();
                tagMap.put(Constants.FR_TAG, Constants.TOTAL_KEY);
//                    tagMap.putAll(v);
                fMap.get(k).add(tagMap);
            }
//                tempMap.putAll(v);
//            fMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()).add(tempMap);
            monitoringPanelMap.put(Constants.FulfillableType.TOTAL_ORDERS.name(),tempMap);
        });
        log.info("current orders map :{}", fMap);
        monitoringPanelMap.put("current", fMap);

    }


    public void createStandardMap(List<Map<String,Object>> allflattenedEntries, Map<String,Object> monitoringPanelMap){
        Map<String,Map<String,Long>> fbStatusMap=prepareFbStatusMapV3();
        Map<String,List<Map<String,Object>>> fMap=prepareFullfilabilityMapsV3();

        allflattenedEntries.forEach(entry->{

            //check for fulfillabillity tag
            if(entry.containsKey(Constants.FULLFILLABLE_CATEGORY)) {

                //update the fullfilable category received
                String ftag = entry.get(Constants.FULLFILLABLE_CATEGORY).toString();
//                Long total = Long.parseLong(entry.get(Constants.TOTAL_KEY).toString());
//                Long newTotal= headerMap.get(ftag)+total;
//                headerMap.put(ftag,newTotal);
//
//                //update the overall total orders
//                String totalorderTag=Constants.FulfillableType.TOTAL_ORDERS.name();
//                Long newTotalOrders= headerMap.get(totalorderTag)+total;
//                headerMap.put(totalorderTag,newTotalOrders);

                //eventually remove the FULLFILLABLE_CATEGORY key and add to the given panel
                entry.remove(Constants.FULLFILLABLE_CATEGORY);
                fMap.get(ftag).add(entry);

            }


        });


        //appending top level details at fulfillability level

        fbStatusMap.forEach((k,v)->{

            Map<String,Object> tempMap=new HashMap<>();
            if(k.equals(Constants.FulfillableType.TOTAL_ORDERS.name())){

//                tempMap.put(Constants.FR_TAG,Constants.TOTAL_KEY);
            }
            else {
                tempMap.put(Constants.FR_TAG, k);
                //also add fr tag total to fmap rag
                Map<String, Object> tagMap = new HashMap<>();
                tagMap.put(Constants.FR_TAG, Constants.TOTAL_KEY);
//                    tagMap.putAll(v);
                fMap.get(k).add(tagMap);
            }
//                tempMap.putAll(v);
//            fMap.get(Constants.FulfillableType.TOTAL_ORDERS.name()).add(tempMap);
            monitoringPanelMap.put(Constants.FulfillableType.TOTAL_ORDERS.name(),tempMap);
        });
        log.info("standard map :{}", fMap);
        monitoringPanelMap.put("standard", fMap);
    }

    public void generateJitMap(List<Tuple> tuples, Map<String,Object> monitoringPanelMap, String mapType, Long headerCount){
        List<Map<String, Object>> mapList = new ArrayList<>();
        Map<String,Object> objectMap = new HashMap<>();
        Map<String, Object> externalVendorMap = createMapAndInitalise(Constants.JIT_EXTERNAL_VENTOR);
        Map<String,Object> lensLabMap = createMapAndInitalise(Constants.LENS_LAB);

        try {
            for (int i = 0; i < tuples.size(); i++) {
                String tag = tuples.get(i).get(MonitorPanelDataCoulmnNameConstants.JIT_TYPE).toString();
                log.info("tag for jit map : {}", tag);
                String status = tuples.get(i).get(Constants.LISTING_PAGE_STATUS).toString();
                log.info("status for jit map : {}", status);
                Long tagStatusCount = tuples.get(i).get(Constants.LISTING_PAGE_TOTAL_COUNT, Long.class);
                if (tag.equalsIgnoreCase(Constants.JIT_EXTERNAL_VENTOR)) {
                    headerCount = updateExternalVendorMap(externalVendorMap, status, tagStatusCount, headerCount);
                } else {
                    headerCount = updateLensLabMap(lensLabMap, status, tagStatusCount, headerCount);
                }
            }
        }catch (Exception e){
            log.error("generating jit map failed with exception : {}", e.getMessage());
        }
        mapList.add(externalVendorMap);
        mapList.add(lensLabMap);
        objectMap.put(Constants.JitOrder.JIT_ORDERS.name(),mapList);
        monitoringPanelMap.put(mapType,objectMap);
        if(mapType.equalsIgnoreCase(Constants.STANDARD)){
            Map<String,Long> headerMap= new HashMap<>();
            headerMap.put(Constants.JitOrder.JIT_ORDERS.name(), headerCount);
            monitoringPanelMap.put(Constants.HEADERS_NODE,headerMap);
        }
    }


    public Map<String,Object> createMapAndInitalise(String tag){
        Map<String, Object> map = new HashMap<>();
        map.put(Constants.FR_TAG, tag);
        map.put(Constants.TOTAL_KEY,0L);
        return map;
    }

    public Long updateExternalVendorMap(Map<String,Object> externalVendorMap, String status, Long tagStatusCount, Long headerCount){
        log.info("updating external vendor map");
        Long totalCount =((Long) (externalVendorMap.get(Constants.TOTAL_KEY)))+tagStatusCount;
        headerCount+=tagStatusCount;
        if(externalVendorMap.containsKey(status)){
            Long count = ((Long)(externalVendorMap.get(status)))+tagStatusCount;
            externalVendorMap.put(Constants.TOTAL_KEY,totalCount);
            externalVendorMap.put(status, count);
        }else{
            externalVendorMap.put(Constants.TOTAL_KEY,totalCount);
            externalVendorMap.put(status,tagStatusCount);
        }
        log.info("current external vendor map : {}", externalVendorMap);
        return headerCount;
    }

    public Long updateLensLabMap(Map<String,Object>lensLabMap, String status, Long tagStatusCount, Long headerCount){
        log.info("updating lens lab map");
        Long totalCount = ((Long)(lensLabMap.get(Constants.TOTAL_KEY)))+tagStatusCount;
        headerCount+=tagStatusCount;
        if(lensLabMap.containsKey(status)){
            Long count = ((Long)(lensLabMap.get(status)))+tagStatusCount;
            lensLabMap.put(Constants.TOTAL_KEY,totalCount);
            lensLabMap.put(status, count);
        }else{
            lensLabMap.put(Constants.TOTAL_KEY,totalCount);
            lensLabMap.put(status,tagStatusCount);
        }
        log.info("current lens lab map : {}", lensLabMap);
        return headerCount;
    }


    public MonitorPanelSummaryResponse generateMonitorPanelSummaryResponse(HomePageRequest homePageRequest, String facilityCode) throws Exception {
        log.info("[MonitoringPanelService][generateTotalHomePageResponse] : fetching total home page response for facility : {} , with request payload as : {} ", facilityCode, homePageRequest);
        try{
            homePageRequest.setMonitorPanelFilters(generateStandardDateRange(homePageRequest.getMonitorPanelFilters()));
            int isFulfillable =  Integer.parseInt(homePageRequest.getMonitorPanelFilters().getSingleSelectFilters().get(MonitorPanelDataCoulmnNameConstants.IS_FULFILLABLE));
            List<Tuple> homePageDataList = monitorPanelDataReadAccessService.getMonitorPanelSummaryDataSepc(MonitorPanelNew.class, isFulfillable, facilityCode, homePageRequest);
             MonitorPanelSummaryResponse monitorPanelSummaryResponse = new MonitorPanelSummaryResponse();
             monitorPanelSummaryResponse.setMonitorPanelTagCountResponseList(MonitorPanelUtil.buildMonitorPanelSummaryResponse(homePageDataList));
             monitorPanelSummaryResponse.setFrTagsList(Constants.frTagList);
             return  monitorPanelSummaryResponse;
        } catch (Exception e) {
            log.error("exception caught while generating total home page response , with exception : {}", e.getMessage(), e);
            throw new Exception("Failed to generate total home page response");
        }
    }

    public List<ManualAsrsWaveResponse> fetchCountDetails(String facility, String createdAtStart, String createdAtEnd) {
        List<Object[]> queryResponse = monitorPanelNewReadRepository.fetchDetailsForManualSyncAsrs(facility,createdAtStart,createdAtEnd);;
        List<ManualAsrsWaveResponse> manualAsrsWaveResponseList = new ArrayList<>();
        log.info("[{}, fetchCountDetails] The response  size is {} for facility code {}",this.getClass().getSimpleName(),queryResponse.size(),facility);
        for(Object[] object : queryResponse){
            log.info("[{}, fetchCountDetails] The response is {} for facility code {}",this.getClass().getSimpleName(),object,facility);
            ManualAsrsWaveResponse manualAsrsWaveResponse = new ManualAsrsWaveResponse(
                    null==object[0]?null:object[0].toString(), null==object[1]?null:object[1].toString(), null==object[2]?null:object[2].toString(),null==object[3]?null:object[3].toString(),
                    null==object[4]?null:object[4].toString(), null==object[5]?null:Boolean.valueOf(object[5].toString()),null==object[6]?null:object[6].toString(), null==object[7]?null:Integer.parseInt(object[7].toString())
            );
            manualAsrsWaveResponseList.add(manualAsrsWaveResponse);
        }
        return manualAsrsWaveResponseList;
    }
}
