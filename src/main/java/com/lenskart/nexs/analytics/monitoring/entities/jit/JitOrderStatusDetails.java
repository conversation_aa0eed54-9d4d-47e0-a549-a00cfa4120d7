package com.lenskart.nexs.analytics.monitoring.entities.jit;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;


@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "jit_order_status_details")
public class JitOrderStatusDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;
    @Column(name = "fitting_id")
    private Integer fittingId;
    @Column(name = "rx_status")
    private String rxString;
    @Column(name = "order_status")
    private String orderStatus;
    @Column(name = "sub_status")
    private String subStatus;
    @Column(name = "blank_pid")
    private String blankPid;
    /*    @Column(name = "uw_item_id")
       private Integer uwItemId;*/
    @Column(name = "lens")
    private String lens;
    @Column(name = "order_code")
    private Integer orderCode;
    @Column(name = "country_code")
    private String countryCode;
    @Column(name = "facility")
    private String facility;
    @Column(name = "order_type")
    private String orderType;
    @Column(name = "order_sub_type")
    private String orderSubType;
    @Column(name = "lens_id")
    private Integer lensId;
    @Column(name = "lens_package")
    private String lensPackage;
    @Column(name = "barcode")
    private String barcode;
    @Column(name = "actual_pid")
    private Integer actualPid;
    @Column(name = "increment_id")
    private Integer incrementId;
    @Column(name = "source")
    private String source;
    @Column(name = "source_reference_id")
    private String sourceReferenceId;
    @Column(name = "qc_fail_count")
    private Integer qcFailCount;
    @Column(name = "lk_status")
    private String lkStatus;
}
