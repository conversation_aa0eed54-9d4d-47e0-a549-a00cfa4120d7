package com.lenskart.nexs.analytics.monitoring.service;

import com.lenskart.nexs.analytics.monitoring.entities.mongodb.MonitorPanelSavedViewsOfEmp;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelSavedView;
import com.lenskart.nexs.analytics.monitoring.repository.mongodb.MonitorPanelSavedViewsRepository;
import com.lenskart.nexs.analytics.monitoring.response.FetchViewsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class MonitorPanelSavedViewsService {
    @Autowired
    MonitorPanelSavedViewsRepository monitorPanelSavedViewsRepository;

    public FetchViewsResponse fetchViews(String empCode) {

        MonitorPanelSavedViewsOfEmp monitorPanelSavedViewsOfEmp = getViewsByEmpCode(empCode);

        FetchViewsResponse fetchViewsResponse = new FetchViewsResponse();

        if(monitorPanelSavedViewsOfEmp != null) {
            fetchViewsResponse.setMonitorPanelSavedViews(monitorPanelSavedViewsOfEmp.getMonitorPanelSavedViewList());
            String defaultFilterName = getDefaultViewName(monitorPanelSavedViewsOfEmp.getMonitorPanelSavedViewList());
            if(defaultFilterName != null)
            fetchViewsResponse.setDefaultFilterName(defaultFilterName);
        } else {
            fetchViewsResponse.setDefaultFilterName("");
            fetchViewsResponse.setMonitorPanelSavedViews(Collections.emptyList());
        }
        return fetchViewsResponse;
    }

    public void saveViews(List<MonitorPanelSavedView> monitorPanelSavedViews, String empCode) throws Exception {
        checkDuplicateViewName(monitorPanelSavedViews);
        MonitorPanelSavedViewsOfEmp monitorPanelSavedFilters = new MonitorPanelSavedViewsOfEmp();
        monitorPanelSavedFilters.setEmpCode(empCode);
        monitorPanelSavedFilters.setUpdatedAt(new Date());
        monitorPanelSavedFilters.setMonitorPanelSavedViewList(monitorPanelSavedViews);
        monitorPanelSavedViewsRepository.save(monitorPanelSavedFilters);

    }

    private void checkDuplicateViewName(List<MonitorPanelSavedView> monitorPanelSavedViews) throws Exception {
        Set<String> viewNames = new HashSet<>();
        for(MonitorPanelSavedView monitorPanelSavedView : monitorPanelSavedViews) {
            viewNames.add(monitorPanelSavedView.getViewName());
        }
        if(viewNames.size() < monitorPanelSavedViews.size())
            throw new Exception("Given view name is duplicate, please enter a new view name");
    }

    private String getDefaultViewName(List<MonitorPanelSavedView> monitorPanelSavedViews) {
        for(MonitorPanelSavedView monitorPanelSavedView : monitorPanelSavedViews) {
            if(monitorPanelSavedView.getIsDefaultView()) {
                return monitorPanelSavedView.getViewName();
            }
        }
        return null;
    }

    private MonitorPanelSavedViewsOfEmp getViewsByEmpCode(String empCode) {
        Optional<MonitorPanelSavedViewsOfEmp> monitorPanelSavedFilters = monitorPanelSavedViewsRepository.findById(empCode);

        return monitorPanelSavedFilters.isPresent() ? monitorPanelSavedFilters.get() : null;
    }


}
