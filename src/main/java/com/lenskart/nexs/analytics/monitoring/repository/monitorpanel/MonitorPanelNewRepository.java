package com.lenskart.nexs.analytics.monitoring.repository.monitorpanel;

import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import org.joda.time.LocalDateTime;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.repository.query.Param;
import javax.persistence.Tuple;
import java.util.List;

@Repository
public interface MonitorPanelNewRepository extends CrudRepository<MonitorPanelNew, Long>, JpaSpecificationExecutor<MonitorPanelNew>, PagingAndSortingRepository<MonitorPanelNew,Long> {

    List<MonitorPanelNew> findAllByShippingPackageId(String shippingPackageId);
    List<MonitorPanelNew> findAllByWmsOrderCode(String wmsOrderCode);
    List<MonitorPanelNew> findAllByWmsOrderCodeIn(List<String> wmsOrderCode);

    MonitorPanelNew findByOrderItemId(Integer orderItemId);

    List<MonitorPanelNew> findAllByFittingId(Integer fittingId);

    @Query(value = "select shipping_package_id from nexs_dp.monitor_panel_data where order_item_id = ?1;", nativeQuery = true)
    String getShippingIdByOrderItemId(Integer orderItemId);

    @Query(value ="\n" +
            "\tSELECT facility_code as facilityCode,\n" +
            "    COUNT(CASE WHEN is_fulfillable = 0  THEN 'FULFILLABLE' ELSE NULL END) AS 'FULFILLABLE',\n" +
            "    COUNT(CASE WHEN is_fulfillable = 1 THEN 'UN_FULFILLABLE' ELSE NULL END) AS 'UNFULFILLABLE',\n" +
            "    COUNT(CASE WHEN is_allocated = 1 AND is_fulfillable = 0 THEN 'ALLOCATED' ELSE NULL END) AS 'ALLOCATED'\n" +
            "    FROM nexs_dp.monitor_panel_data\n" +
            "    WHERE product_id = :pid\n" +
            "    AND v3_fr_tag IN :frTags\n" +
            "    AND order_item_created_at BETWEEN :startDate AND :endDate GROUP BY facility_code;", nativeQuery = true)
    List<Tuple> getFacilityBasedProductDetails(@Param("pid")Long pid, @Param("frTags")List<String> frTags, @Param("startDate")String startDate , @Param("endDate") String endDate);


    @Query(value = "select facility_code as facilityCode, count(product_id) as total\n" +
            "from monitor_panel_data\n" +
            "where product_id = :pid\n" +
            "AND v3_fr_tag IN :frTags\n" +
            "AND order_item_created_at BETWEEN :startDate AND :endDate GROUP BY facility_code;", nativeQuery = true)
    List<Tuple> getProductCountForAllFacilities(@Param("pid")Long pid, @Param("frTags")List<String> frTags, @Param("startDate")String startDate , @Param("endDate") String endDate);


    @Query(value = "\n" +
            "select * from nexs_dp.monitor_panel_data where courier_code is null and wms_status in :statuses and updated_at > NOW() - INTERVAL :time MINUTE LIMIT :limit ;", nativeQuery = true)
    List<MonitorPanelNew> findAllByWmsStatusAndUpdatedAt(@Param("statuses")List<String> softCourierUpdateEligibleStatus, @Param("time")int lastUpdateTime, @Param("limit")int updateLimit);
}
