package com.lenskart.nexs.analytics.monitoring.strategy.Impl;

import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import com.lenskart.nexs.analytics.monitoring.entities.wms.*;
import com.lenskart.nexs.analytics.monitoring.model.CourierDetails;
import com.lenskart.nexs.analytics.monitoring.model.JitOrderItem;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelPickingData;
import com.lenskart.nexs.analytics.monitoring.model.OrderItemData;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelProcessorFactory;
import com.lenskart.nexs.analytics.util.MonitorPanelUtil;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.monitoring.strategy.MonitorPanelBaseStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Component
@Slf4j
public class InsertStrategyMonitorPanel extends MonitorPanelBaseStrategy {


    @Override
    public QueryType supportedStates() {
        return QueryType.INSERT;
    }

    /**
     * starting point for processing insert action on the received event
     * @param requestPayload
     * @throws Exception
     */
    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    @Override
    protected void execute(RequestPayload requestPayload) throws Exception {
        log.info("[InsertStrategyMonitorPanel]: request payload received is : {}", requestPayload);
        this.processInsertAction(requestPayload);
    }

    /**
     *  process received data and persist to monitor_panel_data table.
     * @param requestPayload
     */
    private void processInsertAction(RequestPayload requestPayload) throws Exception {
        Integer orderItemId = requestPayload.getOrderItemId();
        Map<Integer, MonitorPanelPickingData> orderItemToPickingMap = new HashMap<>();
        Map<String, OrderItemData> groupStatusMap = new HashMap<>();
        Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap = new HashMap<>();
        Map<String, JitOrderItem> jitOrderItemStatusMap = new HashMap<>();
        Map<Integer,Integer> pickingPriorityMap = new HashMap<>();
        Map<String, CourierDetails> courierDetailsMap = new HashMap<>();

        try {
            MonitorPanelNew existingData = monitorPanelNewRepository.findByOrderItemId(orderItemId);
            if (ObjectUtils.isNotEmpty(existingData)) {
                log.info("" +
                        "Entry already present for order item id : {} , calling update method.", orderItemId);
                super.updateStrategy.execute(requestPayload);
            }
            existingData = new MonitorPanelNew();
            //@TODO : re evaluate this check
            if (!ObjectUtils.isEmpty(requestPayload.getOrderItemAfterData())) {
                this.createNewEntryForOrderItem(requestPayload, existingData,orderItemToPickingMap,groupStatusMap,jitOrderStatusDetailsMap,jitOrderItemStatusMap,pickingPriorityMap,courierDetailsMap);
            }
            if(Constants.terminalStatusList.contains(existingData.getWmsStatus())){
                return;
            }
            log.info("[InsertStrategy]: persisting monitorPanel data : {} ", existingData.toString());
            monitorPanelNewRepository.save(existingData);
        }catch (Exception e){
            log.error("Failed to update entry got order item id : {} , with exception message : {}", orderItemId, e);

        }
    }

    /**
     *  create new entry for new order item if no existing entry is found.
     * @param requestPayload
     * @param existingData
     */
    public void createNewEntryForOrderItem(RequestPayload requestPayload, MonitorPanelNew existingData, Map<Integer,MonitorPanelPickingData> orderItemToPickingMap, Map<String, OrderItemData> groupStatusMap, Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap, Map<String, JitOrderItem> jitOrderItemStatusMap, Map<Integer,Integer> pickingPriorityMap, Map<String, CourierDetails> courierDetailsMap) throws Exception {
        try{
            log.info("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : createNewEntryForOrderItem : creating new monitor panel data for payload : {}", requestPayload.getOrderItemAfterData());
            OrderItems orderItems = orderItemRepository.getOrderItemsByOrderItemId(requestPayload.getOrderItemId());
            if(ObjectUtils.isNotEmpty(requestPayload.getOrderItemAfterData())){
                MonitorPanelUtil.convertDtoToOrderItem(requestPayload.getOrderItemAfterData(),orderItems);
            }
            monitorPanelDataService.createMonitorPanelDataFromOrderItem(orderItems, existingData,orderItemToPickingMap,groupStatusMap,jitOrderStatusDetailsMap,jitOrderItemStatusMap,pickingPriorityMap,courierDetailsMap);
            log.info("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : createNewEntryForOrderItem : monitor panel data created is : {}", existingData);
        }catch (Exception e){
            log.error("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : Exception caught while creating monitor panel new data for payload : {}", requestPayload);
            throw new Exception("Exception caught while creating new entry for order item " + e);
        }

    }
}
