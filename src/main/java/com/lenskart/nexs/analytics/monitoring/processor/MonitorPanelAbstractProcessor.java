package com.lenskart.nexs.analytics.monitoring.processor;

import com.google.gson.Gson;
import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelNewRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.OrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import javax.annotation.PostConstruct;

@Slf4j
public abstract class MonitorPanelAbstractProcessor {

    //    @Autowired
    public static MonitorPanelExecutor executor;
    public static MonitorPanelNewRepository monitorPanelNewRepository;
    public static Gson gson;
    public static MonitorPanelProcessorFactory monitorPanelProcessorFactory;
    public static OrderItemRepository orderItemsReporitory;
    @Autowired
    private ApplicationContext applicationContext;

    protected abstract RequestPayload doValidate(RequestPayload requestPayload) throws Exception;
    protected abstract RequestPayload prepareEventMessage(DebeziumMessage debeziumMessage) throws Exception;
    protected abstract void executePopulateMonitorPanelData(RequestPayload requestPayload) throws Exception;


    /**
     * get query type for current event message
     * @param status
     * @return
     */
    public QueryType getQueryType(String status){
        if(Constants.CREATED_STATUS.equalsIgnoreCase(status)){
            return QueryType.INSERT;
        }else if(monitorPanelProcessorFactory.getTerminalStatusList().contains(status)){
            return QueryType.DELETE;
        }
        return QueryType.UPDATE;
    }

    /**
     *  common validation of the event message
     * @param requestPayload
     * @return
     * @throws Exception
     */
    protected RequestPayload commonValidation(RequestPayload requestPayload) throws Exception{

//        @TODO: RETURNED order was still present in the mointor panel data, should not be processed.
        try{
            if(monitorPanelProcessorFactory.getTerminalStatusList().contains(requestPayload.getStatus())){
                return requestPayload;
            }
        }catch (Exception e){
            throw new Exception("Invalid state for the event message : "+ e.getStackTrace());
        }
        return requestPayload;
    }

    /**
     *  execution point for kafka messsage consumed
     * @param debeziumMessage
     * @throws Exception
     */
    public void doExecute(DebeziumMessage debeziumMessage) throws Exception {

        RequestPayload requestPayload;
        try{
            requestPayload = prepareEventMessage(debeziumMessage);
            log.debug("requestPayload orderItemId- {}  shippingPackageId - {} data {}",requestPayload.getOrderItemId(),
                    requestPayload.getShippingPackageId() , requestPayload);
        }catch (Exception e){
            throw new Exception("Failed to prepare event message with message : "+ e);
        }

        try{
            requestPayload = commonValidation(requestPayload);
        }catch (Exception e){
            throw new Exception("Failed to perform common validation on event with message : "+ e);
        }

        try{
            requestPayload = doValidate(requestPayload);
        }catch (Exception e){
            throw new Exception("Failed to validate data event with message : "+ e);
        }

        try{
            executePopulateMonitorPanelData(requestPayload);
        }catch (Exception e){
            throw new Exception("Failed to execute update monitor panel data event with message : "+ e);
        }

    }

    @PostConstruct
    private void postConstruct() {
        init();
    }

    @Autowired
    public final void init() {
        this.executor = applicationContext.getBean(MonitorPanelExecutor.class);
        this.monitorPanelNewRepository = applicationContext.getBean(MonitorPanelNewRepository.class);
        this.gson = applicationContext.getBean(Gson.class);
        this.monitorPanelProcessorFactory = applicationContext.getBean(MonitorPanelProcessorFactory.class);
        this.orderItemsReporitory = applicationContext.getBean(OrderItemRepository.class);
    }

}
