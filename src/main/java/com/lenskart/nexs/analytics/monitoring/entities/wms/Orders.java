package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderMetaData;
import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;


import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = SqlTableConstants.ORDERS)
public class Orders extends BaseEntity {

    @NotNull
    @Column(name ="order_id",unique = true, columnDefinition = "varchar(20)")
    private Integer orderId;


    @NotNull
    @Column(name ="increment_id",unique = true, columnDefinition = "varchar(20)")
    private Integer incrementId;


    @Column(name ="payment_status")
    private Boolean paymentStatus;



    @Temporal(TemporalType.TIMESTAMP)
    @Column(name ="order_created_at" , columnDefinition = "timestamp")
    private Date orderCreatedAt;



    private String priority;

    private String status;

    @Column(name ="order_amount")
    private BigDecimal orderAmount;

    @Column(name ="order_type")
    private String orderType;


    @Column(name ="country_code")
    private String countryCode;

    @Column(name ="source")
    private String source;


    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "currency")
    private String currency;

    @OneToMany(mappedBy = "nexs_order_id",fetch = FetchType.EAGER,cascade = {CascadeType.PERSIST})
    private List<OrderMetaData> orderMetaDataList = new ArrayList<OrderMetaData>();

    @Version
    private Integer version;
}
