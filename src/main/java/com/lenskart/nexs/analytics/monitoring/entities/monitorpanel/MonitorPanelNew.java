package com.lenskart.nexs.analytics.monitoring.entities.monitorpanel;


import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Version;

@Setter
@Getter
@Entity
@Audited
@ToString
@AuditOverride(forClass = MonitorPanelData.class)
@Table(name = "monitor_panel_data")
public class MonitorPanelNew extends MonitorPanelData {
    @Column(name = "picking_status")
    private Integer pickingStatus;
}
