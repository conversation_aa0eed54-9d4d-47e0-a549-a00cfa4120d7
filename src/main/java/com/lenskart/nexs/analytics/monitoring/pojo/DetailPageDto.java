package com.lenskart.nexs.analytics.monitoring.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class DetailPageDto {
    @JsonProperty(value="Increment ID",index=0)
    private Integer incrementId;
    @JsonProperty(value="Shipping Package ID",index=1)
    private String shippingPackageId;
    @JsonProperty(value="Current Status",index=2)
    private String wmsStatus;
    @JsonProperty(value="FR Tag",index=3)
    private String frTag;
    @JsonProperty(value="Fitting ID",index=4)
    private Integer fittingId;
    @JsonProperty(value="Order Item ID",index=5)
    private Integer orderItemId;
    @JsonProperty(value="Product ID",index=6)
    private Integer productId;
    @JsonProperty(value="Unicom Order Code",index=7)
    private String wmsOrderCode;
    @JsonProperty(value="Channel",index=10)
    private String orderChannel;
    @JsonProperty(value="Power Type",index=11)
    private String powerType;
    @JsonProperty(value="Lens Type",index=12)
    private String lensType;
    @JsonProperty(value="Country",index=13)
    private String country;
    @JsonProperty(value="Last Updated At",index=14)
    private Date orderItemLastUpdatedAt;
    @JsonProperty(value="Created At",index=15)
    private Date orderItemCreatedAt;
    @JsonProperty(value="Item Type",index=16)
    private String itemType;
    @JsonProperty(value="Barcode",index=17)
    private String barcode;
    @JsonProperty(value="Tray No",index=18)
    private String trayId;
    @JsonProperty(value="Manifest No",index=19)
    private String manifestNo;
    @JsonProperty(value = "Blank Pid", index = 20)
    private String blankPid;
    @JsonProperty(value="AWB No",index=21)
    private String awbNumber;
    @JsonProperty(value="Courier Code",index=22)
    private String courierCode;
    @JsonProperty(value="Group Effective Status", index = 23)
    private String groupEffectiveStatus;
    @JsonProperty(value = "Invoice Number", index = 24)
    private String invoiceNumber;
    @JsonProperty(value="Order Aging",index=8)
    private float ageingSinceCreated = 0.0f;
    @JsonProperty(value="Last Update Since",index=9)
    private float ageingSinceLastUpdate = 0.0f;
    @JsonProperty(value = "Jit Group Status", index = 25)
    private String jitGroupStatus;


    private static float epochToHours(long time){
        return Float.parseFloat(String.format("%.2f",(float)time/(1000*60*60)));
    }
    public float getAgeingSinceCreated() {
        long currentTime = new Date().getTime();
        long orderItemCreatedTime = getOrderItemCreatedAt().getTime();
        float ageingInHours = epochToHours(currentTime - orderItemCreatedTime);
        return ageingInHours;
    }
    public float getAgeingSinceLastUpdate() {
        long currentTime = new Date().getTime();
        long orderItemUpdateTime = getOrderItemLastUpdatedAt().getTime();
        float updateAgeingInHours = epochToHours(currentTime - orderItemUpdateTime);
        return updateAgeingInHours;
    }
}


/**
 *
 *
 *
 *
 * Increment ID
 * FR Tag
 * Product ID
 * Order Item ID
 * Current Status
 * Fitting ID
 * Unicom Sale order code   * -> confirm with Ankit
 * Shipment ID
 * Ageing: Current Date - Created At (in hours)
 * Updated Since Ageing in hrs: Current Date - Last Updated At (in hours)
 * Store code      *-> confirm with Ankit
 * Courier name  * -> courier_shipment_details ( confirm with Ankit)
 * Channel
 * Power type (Single Vision, Bifocal, Progressive)  * ->where do we get power type from?
 * Lens Type (Same as what we are showing in picking)
 * Country
 * Last updated at
 * Last Updated by
 */
