package com.lenskart.nexs.analytics.monitoring.strategy.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNewHistory;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelProcessorFactory;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.monitoring.strategy.MonitorPanelBaseStrategy;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DeleteStrategyMonitorPanel extends MonitorPanelBaseStrategy {
    @Override
    public QueryType supportedStates() {
        return QueryType.DELETE;
    }

    @Autowired
    private MonitorPanelProcessorFactory monitorPanelProcessorFactory;

    /**
     * start point of deletion of monitor panel data
     *
     * @param requestPayload
     * @throws Exception
     */
    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    @Override
    protected void execute(RequestPayload requestPayload) throws Exception {
        this.processDeleteAction(requestPayload);
    }

    @Value("${nexs.update.list.batch}")
    private int batchProcessingSize;

    /**
     * all the shipments with terminal states ( CANCELLED, REASSIGNED, DISPATCHED) are moved to monitor panel history table and deleted from monitor panel data.
     *
     * @param requestPayload
     */
    private void processDeleteAction(RequestPayload requestPayload) throws Exception {
        try {
            List<MonitorPanelNew> monitorPanelNewList = monitorPanelNewRepository.findAllByWmsOrderCode(requestPayload.getWmsOrderCode());
            log.error("processDeleteAction monitorPanelNewList :{} | requestPayload:{}",monitorPanelNewList.size(),
                    requestPayload);
            if(!monitorPanelNewList.isEmpty()) {
                monitorPanelNewRepository.deleteAll(monitorPanelNewList);
            }
        } catch (Exception e) {
            log.error("[DeleteStrategyMonitorPanel][processDeleteAction] : failed to delete terminal entries with error : {}", e.getMessage());
            throw new Exception("Exception caught while deleting terminal order items " + e.getStackTrace());
        }

    }


    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    public void deleteStaleData(List<String> shipmentIds) {
        shipmentIds = shipmentIds.stream()
                .distinct()
                .collect(Collectors.toList());

        log.info("updating group effective status for shipment ids in  : {} ", shipmentIds);

        int totalSize = shipmentIds.size();
        int numberOfBatches = (int) Math.ceil((double) totalSize / batchProcessingSize);

        for (int i = 0; i < numberOfBatches; i++) {
            int start = i * batchProcessingSize;
            int end = Math.min(start + batchProcessingSize, totalSize);
            List<String> currentBatchToDelete = shipmentIds.subList(start, end);
            List<String> orderCodeToDelete = orderItemRepository.findAllByShippingPackageIdIn(currentBatchToDelete).stream()
                                                                                .map(OrderItems::getWmsOrderCode)
                                                                                .distinct()
                                                                                .collect(Collectors.toList());
            List<MonitorPanelNew> itemsToDelete = monitorPanelNewRepository.findAllByWmsOrderCodeIn(orderCodeToDelete);
            monitorPanelNewRepository.deleteAll(itemsToDelete);
        }

    }
}
