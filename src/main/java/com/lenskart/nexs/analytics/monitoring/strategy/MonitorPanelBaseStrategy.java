package com.lenskart.nexs.analytics.monitoring.strategy;


import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelExecutor;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelProcessorFactory;
import com.lenskart.nexs.analytics.monitoring.repository.jitdb.JitDbRepository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelNewHistoryRepository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelNewRepository;
import com.lenskart.nexs.analytics.monitoring.repository.picking.PicklistOrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.repository.scm.ShipmentDetailsRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.*;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.monitoring.service.MonitorPanelDataService;
import com.lenskart.nexs.analytics.monitoring.strategy.Impl.OrderActivityEventStrategy;
import com.lenskart.nexs.analytics.monitoring.strategy.Impl.UpdateStrategyMonitorPanel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import javax.annotation.PostConstruct;
import java.util.List;

public abstract class MonitorPanelBaseStrategy {

    protected static MonitorPanelNewRepository monitorPanelNewRepository;
    protected static MonitorPanelNewHistoryRepository monitorPanelNewHistoryRepository;
    protected static OrderItemRepository orderItemRepository;
    protected static MonitorPanelDataService monitorPanelDataService;
    protected static ShipmentDetailsRepository shipmentDetailsRepository;
    protected static PicklistOrderItemRepository picklistOrderItemRepository;
    protected static OrdersRepository orderRepository;
    protected static OrderItemHeaderRepository orderItemHeaderRepository;
    protected static OrderItemPowerRepository orderItemPowerRepository;
    protected static JitDbRepository jitDbRepository;
    protected static UpdateStrategyMonitorPanel updateStrategy;
    protected static OrderActivityEventStrategy orderActivityEventStrategy;
    @Autowired
    ApplicationContext applicationContext;
    public abstract QueryType supportedStates();
    protected abstract void execute(RequestPayload requestPayload) throws Exception ;
    public void doExecute(RequestPayload requestPayload) throws Exception{
        execute(requestPayload);
    }
    @PostConstruct
    private void postConstruct() {
        setRepository();
    }

    @Autowired
    public final void setRepository() {
        this.monitorPanelNewRepository = applicationContext.getBean(MonitorPanelNewRepository.class);
        this.monitorPanelDataService = applicationContext.getBean(MonitorPanelDataService.class);
        this.orderItemRepository = applicationContext.getBean(OrderItemRepository.class);
        this.monitorPanelNewHistoryRepository = applicationContext.getBean(MonitorPanelNewHistoryRepository.class);
        this.shipmentDetailsRepository = applicationContext.getBean(ShipmentDetailsRepository.class);
        this.picklistOrderItemRepository = applicationContext.getBean(PicklistOrderItemRepository.class);
        this.orderRepository = applicationContext.getBean(OrdersRepository.class);
        this.orderItemHeaderRepository = applicationContext.getBean(OrderItemHeaderRepository.class);
        this.orderItemPowerRepository = applicationContext.getBean(OrderItemPowerRepository.class);
        this.jitDbRepository = applicationContext.getBean(JitDbRepository.class);
        this.updateStrategy = applicationContext.getBean(UpdateStrategyMonitorPanel.class);
        this.orderActivityEventStrategy = applicationContext.getBean(OrderActivityEventStrategy.class);
    }

}
