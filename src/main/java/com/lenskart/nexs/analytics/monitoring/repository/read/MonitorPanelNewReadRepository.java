package com.lenskart.nexs.analytics.monitoring.repository.read;

import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.response.ManualAsrsWaveResponse;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;
import java.util.Date;
import java.util.List;

@Repository("monitorPanelNewReadRepository")
public interface MonitorPanelNewReadRepository extends CrudRepository<MonitorPanelNew, Long>, JpaSpecificationExecutor<MonitorPanelNew>, PagingAndSortingRepository<MonitorPanelNew,Long> {

    List<MonitorPanelNew> findAllByShippingPackageId(String shippingPackageId);
    List<MonitorPanelNew> findAllByWmsOrderCode(String wmsOrderCode);
    List<MonitorPanelNew> findAllByWmsOrderCodeIn(List<String> wmsOrderCode);

    MonitorPanelNew findByOrderItemId(Integer orderItemId);

    List<MonitorPanelNew> findAllByFittingId(Integer fittingId);

    @Query(value = "select shipping_package_id from nexs_dp.monitor_panel_data where order_item_id = ?1;", nativeQuery = true)
    String getShippingIdByOrderItemId(Integer orderItemId);

    @Query(value ="\n" +
            "\tSELECT facility_code as facilityCode,\n" +
            "    COUNT(CASE WHEN is_fulfillable = 0  THEN 'FULFILLABLE' ELSE NULL END) AS 'FULFILLABLE',\n" +
            "    COUNT(CASE WHEN is_fulfillable = 1 THEN 'UN_FULFILLABLE' ELSE NULL END) AS 'UNFULFILLABLE',\n" +
            "    COUNT(CASE WHEN is_allocated = 1 AND is_fulfillable = 0 THEN 'ALLOCATED' ELSE NULL END) AS 'ALLOCATED'\n" +
            "    FROM nexs_dp.monitor_panel_data\n" +
            "    WHERE product_id = :pid\n" +
            "    AND v3_fr_tag IN :frTags\n" +
            "    AND order_item_created_at BETWEEN :startDate AND :endDate GROUP BY facility_code;", nativeQuery = true)
    List<Tuple> getFacilityBasedProductDetails(@Param("pid")Long pid, @Param("frTags")List<String> frTags, @Param("startDate")String startDate , @Param("endDate") String endDate);


    @Query(value = "select facility_code as facilityCode, count(product_id) as total\n" +
            "from monitor_panel_data\n" +
            "where product_id = :pid\n" +
            "AND v3_fr_tag IN :frTags\n" +
            "AND order_item_created_at BETWEEN :startDate AND :endDate GROUP BY facility_code;", nativeQuery = true)
    List<Tuple> getProductCountForAllFacilities(@Param("pid")Long pid, @Param("frTags")List<String> frTags, @Param("startDate")String startDate , @Param("endDate") String endDate);

    List<MonitorPanelNew> findAllByShippingPackageIdIn(List<String>currentBatchToDelete);

    @Query(value = "select  group_effective_status, processing_type, picking_priority, jit_type, jit_group_status, isAsrsOrder, order_channel, count(distinct shipping_package_id) " +
            "from monitor_panel_data where " +
            "group_effective_status in ('Synced','QC Fail:::Pending Picking','Pending Picking') and " +
            "facility_code = :facility  and created_at between :createdAtStart and :createdAtEnd " +
            "group by group_effective_status, processing_type,picking_priority,jit_type,jit_group_status,isAsrsOrder,order_channel", nativeQuery = true)
    List<Object[]> fetchDetailsForManualSyncAsrs(@Param("facility") String facility, @Param("createdAtStart") String createdAtStart, @Param("createdAtEnd") String createdAtEnd);
}
