package com.lenskart.nexs.analytics.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Version;
import java.util.Calendar;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class PicklistOrderItemDto {

    @JsonProperty("increment_id")
    private Integer incrementId;
    @JsonProperty("product_id")
    private Integer productId;
    @JsonProperty("wms_order_id")
    private Integer wmsOrderId;
    @JsonProperty("wms_order_item_id")
    private Integer wmsOrderItemId;
    @JsonProperty("wms_order_code")
    private String wmsOrderCode;
    @JsonProperty("shipment_id")
    private String shipmentId;
    @JsonProperty("scm_order_id")
    private Integer scmOrderId;
    @JsonProperty("scm_order_item_id")
    private Integer scmOrderItemId;
    @JsonProperty("status")
    private Integer status = 0;
    @JsonProperty("location_barcode")
    private String locationBarcode;
    @JsonProperty("location_hierarchy")
    private String locationHierarchy;
    @JsonProperty("no_item_per_order")
    private Integer noItemPerOrder;
    @JsonProperty("no_product")
    private Integer noProduct;
    @JsonProperty("product_name")
    private String productName;
    @JsonProperty("product_image")
    private String productImage;
    @JsonProperty("channel")
    private String channel;
    @JsonProperty("product_type")
    private String productType;
    @Column(name = "es_sync_log")
    private String esSyncLog;
    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("priority")
    private Integer priority;
    @JsonProperty("facility")
    private String facility;
    @JsonProperty("order_state")
    private String orderState; // Order status
    @JsonProperty("fitting_id")
    private Integer fittingId;
    @JsonProperty("processing_type")
    private String processingType;
    @JsonProperty("item_type")
    private String itemType;
    @JsonProperty("fitting")
    private String fitting;
    @JsonProperty("jit_order")
    private Boolean jitOrder;
    @JsonProperty("fast_picking")
    private Boolean fastPicking;
    @JsonProperty("scm_order_created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String scmOrderCreatedAt;
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createdAt;
    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Field(value = "updatedDate", type = FieldType.Date, format = DateFormat.custom, pattern = "uuuu-MM-dd HH:mm:ss")
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private String updatedAt;
    @JsonProperty("created_by")
    private String createdBy;
    @JsonProperty("updated_by")
    private String updatedBy;
    @JsonProperty("version")
    private Integer version;
    @JsonProperty("repick_status")
    private String repickStatus;
    @JsonProperty("repick_count")
    private Integer repickCount;
    @JsonProperty("location_type")
    private String locationType;
}
