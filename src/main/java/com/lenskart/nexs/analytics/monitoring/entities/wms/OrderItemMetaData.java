package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = SqlTableConstants.ORDER_ITEM_META_DATA)
public class OrderItemMetaData extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "order_item_id", referencedColumnName = "id")
    private OrderItems order_item_id;

    @Column(name = "pair_key")
    private String pairKey;

    private String value;

}
