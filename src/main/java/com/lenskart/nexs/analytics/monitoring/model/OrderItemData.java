package com.lenskart.nexs.analytics.monitoring.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;


@Data
@AllArgsConstructor
public class OrderItemData {
    private Date recentUpdateTime;
    private String effectiveStatus;
    private String orderItemType;
    private String qcStatus;
    private String groupId;
    private String v3FrTag;
    private Integer pickingPriority;
    private Boolean trueLastPiece;
    private Boolean lensOnlyOrder;
    private Boolean isAsrsOrder;
}
