package com.lenskart.nexs.analytics.monitoring.utils;

import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import com.lenskart.nexs.analytics.monitoring.model.JitOrderItem;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class MonitorPanelUtils {
    private Set<String> outOFLensLabWmsStatus = Constants.fr1_fr2StatusMap.keySet();

    /**
     *
     * @param jitOrderStatusDetail
     * @param monitorPanelData
     * @return
     * Keeping the default status as undetermind
     * Next priority is if the item is actually out of lenslab based on wms status
     * JIT Table Priority Columns
     * LK_STATUS for processing and
     */
    public String getJitStatus(JitOrderStatusDetails jitOrderStatusDetail, MonitorPanelData monitorPanelData){
        String jitStatus = Constants.JIT_UN_CLASSIFIED;
        outOFLensLabWmsStatus.removeAll(Arrays.asList( "CREATED", "PENDING_PICKING", "IN_PICKING", "PICKED", "PRODUCTION_DONE","BLANK_IN_TRAY"));
        if(outOFLensLabWmsStatus.contains(monitorPanelData.getWmsStatus())) {
            jitStatus  = Constants.JIT_OUT_OF_LENSLAB;
        }
        else {
            log.info("[{} ,getJitStatus ] Using new flow of jit status being updated for orderItemId {}", this.getClass().getSimpleName(),
                    jitOrderStatusDetail.getSourceReferenceId());
            jitStatus = determineNewJitStatus(jitOrderStatusDetail, monitorPanelData, jitStatus);
        }

        log.info("[{} ,getJitStatus ] The jitStatus is {} for  orderItemId {}",this.getClass().getSimpleName(),jitStatus,
                jitOrderStatusDetail.getSourceReferenceId());
        return jitStatus;
    }

    /**
     *
     * @param jitOrderStatusDetail
     * @param monitorPanelData
     * @param jitStatus
     * @return
     */
    private String determineNewJitStatus(JitOrderStatusDetails jitOrderStatusDetail, MonitorPanelData monitorPanelData, String jitStatus) {
        StringBuffer jitKey = new StringBuffer();
        if (jitOrderStatusDetail.getQcFailCount()>0) {
            jitKey.append("QC_FAILED");
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getRxString())) {
            jitKey.append(jitOrderStatusDetail.getRxString());
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getOrderStatus())) {
            jitKey.append(jitOrderStatusDetail.getOrderStatus());
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getSubStatus())) {
            jitKey.append(jitOrderStatusDetail.getSubStatus());
            jitKey.append(Constants.DELIMITER);
        }
        if (!StringUtils.isEmpty(jitOrderStatusDetail.getLkStatus())) {
            jitKey.append(jitOrderStatusDetail.getLkStatus());
        }
        log.info("[] the jit key for orderItemId {} is {}",jitOrderStatusDetail.getSourceReferenceId(),jitKey.toString());
        jitStatus = Constants.autoJitOrderStatusMap.containsKey(jitKey.toString())?Constants.autoJitOrderStatusMap.get(jitKey.toString()):Constants.JIT_UN_CLASSIFIED;
        return jitStatus;
    }

    public Map<String, JitOrderItem> createJitGroupEffectiveStatusMap(MonitorPanelData monitorPanelData, Map<String,JitOrderItem> jitOrderItemMap){
        log.info("[{}, createJitGroupEffectiveStatusMap] getting group effective status for jit order with id : {}",
                this.getClass().getSimpleName(),monitorPanelData.getFittingId());
        if(jitOrderItemMap.containsKey(monitorPanelData.getFittingId().toString())){
            JitOrderItem existingOrderItem = jitOrderItemMap.get(monitorPanelData.getFittingId().toString());
            String jitGroupStatus;
            if(monitorPanelData.getJitStatus() == null && existingOrderItem.getJitGroupStatus() == null ){
                jitGroupStatus = Constants.JIT_UN_CLASSIFIED;
            }else{
                jitGroupStatus =
                        Constants.jitGroupStatusPriorityMap.get(existingOrderItem.getJitGroupStatus())>Constants.jitGroupStatusPriorityMap.get(monitorPanelData.getJitGroupStatus()) ?
                                existingOrderItem.getJitGroupStatus() : monitorPanelData.getJitGroupStatus();
            }
            log.info("[{}, createJitGroupEffectiveStatusMap] getting group effective status as : {},  for jit order with id : {}",
                    this.getClass().getSimpleName(), jitGroupStatus, monitorPanelData.getFittingId());
            existingOrderItem.setJitGroupStatus(jitGroupStatus);
        }else {
            JitOrderItem jitOrderItem = new JitOrderItem();
            jitOrderItem.setJitType(monitorPanelData.getJitType());
            jitOrderItem.setJitGroupStatus(monitorPanelData.getJitGroupStatus());
            jitOrderItem.setItemType(monitorPanelData.getItemType());
            jitOrderItemMap.put(monitorPanelData.getFittingId().toString(),jitOrderItem);
        }

        return jitOrderItemMap;
    }
}
