package com.lenskart.nexs.analytics.monitoring.utils;

import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.pojo.DetailPageDto;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class DetailsPageUtils {

    public static float epochToHours(long time){

        return Float.parseFloat(String.format("%.2f",(float)time/(1000*60*60)));
    }

    public static String formatDate(Date date){
        SimpleDateFormat simpleDateFormat= new SimpleDateFormat(Constants.DATE_24_HOUR_FORMAT);
        return simpleDateFormat.format(date);

    }

    public static int fetchFbLevel(Map<String,Integer> fbMap,String key){
        int level=Constants.DEFAULT_FULLFILLABILITY;
        if(fbMap.containsKey(key)){
            level=fbMap.get(key);
        }
        return level;
    }
    private static String derivePowerType(String isAp){
        String powerType=Constants.PowerType.SINGLE_VISION.getLabel();
        if(!StringUtils.isEmpty(isAp)){
            if(isAp.equalsIgnoreCase("yes"))
                powerType=Constants.PowerType.BIFOCAL.getLabel();
        }
        return powerType;
    }

    /**
     * Determine the processing_type and item_type based on frTag received from the UI in request
     * @param frtags
     * @return
     * @throws Exception
     */
    public static List<Pair<String,String>> deduceFrTag(List<String> frtags) throws Exception {
        List<Pair<String,String>> pairList=new ArrayList<>();
        for(String frTag:frtags){
            String[] splitArray= frTag.split(Constants.DELIMITER);
            if(splitArray.length==2) {
                pairList.add(Pair.of(splitArray[0], splitArray[1]));
            }
        }

        return pairList;

    }



    public static DetailPageDto mapOrderItemDetailsPojo(String frTag, OrderItems orderItems){
        DetailPageDto detailPagePojo=new DetailPageDto();

        detailPagePojo.setOrderChannel(orderItems.getChannel());
        detailPagePojo.setFittingId(orderItems.getFittingId());
        detailPagePojo.setWmsStatus(orderItems.getStatus());
        detailPagePojo.setFrTag(frTag);
        detailPagePojo.setWmsOrderCode(orderItems.getWmsOrderCode());
        detailPagePojo.setProductId(orderItems.getProduct_id());
        detailPagePojo.setCountry(orderItems.getNexsOrderId().getCountryCode());
        detailPagePojo.setIncrementId(orderItems.getNexsOrderId().getIncrementId());
        detailPagePojo.setItemType(orderItems.getItemType());
        detailPagePojo.setOrderItemId(orderItems.getOrderItemId());
        if(orderItems.getPower()!=null){

            detailPagePojo.setLensType(orderItems.getPower().getLensType());
            detailPagePojo.setPowerType(derivePowerType(orderItems.getPower().getWithAp()));

        }

        if(orderItems.getShippingPackageId() != null) {
            detailPagePojo.setShippingPackageId(orderItems.getShippingPackageId());
        }

        //convert to epoch and determine the ageing in hours

        long orderItemUpdateTime=orderItems.getUpdatedAt().getTime();
        long orderItemCreatedTime=orderItems.getCreatedAt().getTime();
        long currentTime=new Date().getTime();
        float ageingInHours=epochToHours(currentTime-orderItemCreatedTime);
        float updateAgeingInHours=epochToHours(currentTime-orderItemUpdateTime);
        detailPagePojo.setAgeingSinceCreated(ageingInHours);
        detailPagePojo.setAgeingSinceLastUpdate(updateAgeingInHours);

        detailPagePojo.setOrderItemCreatedAt(orderItems.getCreatedAt());
        detailPagePojo.setOrderItemLastUpdatedAt(orderItems.getUpdatedAt());

        detailPagePojo.setBarcode(orderItems.getBarcode());
        detailPagePojo.setTrayId(orderItems.getLocationId());

        return detailPagePojo;
    }
}
