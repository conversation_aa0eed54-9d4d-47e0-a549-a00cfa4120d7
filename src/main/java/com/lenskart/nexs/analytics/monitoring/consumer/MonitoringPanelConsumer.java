package com.lenskart.nexs.analytics.monitoring.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.dto.JitOrderStatusDetailsDto;
import com.lenskart.nexs.analytics.monitoring.dto.OrderItemsDto;
import com.lenskart.nexs.analytics.monitoring.dto.PicklistOrderItemDto;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelProcessorFactory;
import com.lenskart.nexs.analytics.monitoring.strategy.Impl.DeleteStrategyMonitorPanel;
import com.lenskart.nexs.analytics.monitoring.utils.KafkaProducerUtils;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.regex.Pattern;


@Slf4j
@Service
public class MonitoringPanelConsumer {
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    MonitorPanelProcessorFactory monitorPanelProcessorFactory;

    @Autowired
    DeleteStrategyMonitorPanel deleteStrategyMonitorPanel;

    @Autowired
    Gson gson;

    @Value("${nexs.listner.enabled}")
    protected boolean isListnerEnabled;

    @Autowired
    private KafkaProducerUtils kafkaProducerUtils;

    @Value("${nexs.kafka.order-items.temp.topic}")
    private String orderItemTemTopic;

    @Value("${kafka.topic.name.pickilist-order-items.temp.topic}")
    private String picklistOrderItemTempTopic;

    @Value("${kafka.topic.name.jit-order-status-details.temp.topic}")
    private String jitOrderStatusDetailsTempTopic;

    @Value("${nexs.kafka.order-items.topic.temp.enabled}")
    private Boolean isProducerEnabled;

    @Value("${nexs.monitor.panel.channel.allowed.regex:^BULKTOVENDOR$}")
    private String allowedChannelRegex;

    private Pattern allowedChannelPattern;

    @PostConstruct
    private void init(){
        allowedChannelPattern = Pattern.compile(allowedChannelRegex, Pattern.CASE_INSENSITIVE);
    }


    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${kafka.topic.name.order-items}", groupId = "${kafka.topic.name.order-items-group-id}")
    public void producerOrderItems(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) throws Exception {

        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            if (isListnerEnabled) {
                log.info("[order_items_activity][producerOrderItems] The messageConsumed is {} and header {}",
                        message, messageHeader);
                //TODO temeporary fixes for kafka partition
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                if (isProducerEnabled) {
                    log.info("[order_items_activity][producerOrderItems] isProducerEnabled  {} started", isProducerEnabled);
                    String text = gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                    OrderItemsDto orderItemsDto = ObjectHelper.getObjectMapper().readValue(text, OrderItemsDto.class);
                    if(isAllowedChannels(orderItemsDto.getChannel())) {
                        kafkaProducerUtils.sendAndGet(orderItemTemTopic, orderItemsDto.getWmsOrderCode(), message);
                    }
                } else {
                    MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                    monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                }
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[order_items_activity][producerOrderItems] : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("[order_items_activity][producerOrderItems] exception caught  : {}", e.getMessage(), e);
            throw e;
        } finally {
            log.debug("[listenOrderItems][producerOrderItems] : processed message: {}", message);
//            ack.acknowledge();
        }
    }

    @Logging
    private boolean isAllowedChannels(String channel) {
        if(Objects.nonNull(allowedChannelPattern) && Objects.nonNull(channel)){
            return !allowedChannelPattern.matcher(channel).find();
        }
        return true;
    }


    @Trace(dispatcher = true)
    @KafkaListener(topics = "${nexs.kafka.order-items.temp.topic}", groupId = "${nexs.kafka.order-items.topic.temp" +
            ".group-id}")
    public void listenOrderItemsTemp(@Payload String message, @Headers MessageHeaders messageHeader,
                                     Acknowledgment ack) {

        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            if (isListnerEnabled) {
                log.info("[order_items_activity][listenOrderItemsTemp] The messageConsumed is {} and header {}",
                        message, messageHeader);
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[order_items_activity][listenOrderItemsTemp] : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
        } catch (Exception e) {
            log.error("[order_items_activity][listenOrderItemsTemp] listenOrderItemsTemp exception caught  : "+ e.getMessage(), e);
        } finally {
            timeTaken = System.currentTimeMillis() - startTime;
            log.info("[listenOrderItems][listenOrderItemsTemp] : time taken : {} processed message: {}",timeTaken,
                    message);
            ack.acknowledge();
        }
    }


    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${kafka.topic.name.pickilist-order-items}", groupId = "${kafka.topic.name.pickilist-order-items-group-id}")
    public void producerPicklistOrderItems(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) throws Exception {

        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            log.info("[picklist_order_item_activity][producerPicklistOrderItems] The messageConsumed is {} and header {}",
                    message, messageHeader);
            if (isListnerEnabled) {
                //TODO temeporary fixes for kafka partition
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                if (isProducerEnabled) {
                    log.info("[picklist_order_item_activity][producerPicklistOrderItems] isProducerEnabled  {} started", isProducerEnabled);
                    String text = gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                    PicklistOrderItemDto picklistOrderItemDto = ObjectHelper.getObjectMapper().readValue(text, PicklistOrderItemDto.class);
                    if(isAllowedChannels(picklistOrderItemDto.getChannel())) {
                        kafkaProducerUtils.sendAndGet(picklistOrderItemTempTopic, picklistOrderItemDto.getWmsOrderCode(),
                                message);
                    }
                } else {
                    MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                    monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                }
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[picklist_order_item_activity][producerPicklistOrderItems] : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("[picklist_order_item_activity][producerPicklistOrderItems] exception caught  : {}", e.getMessage(), e);
            throw e;
        } finally {
            log.info("[listenOrderItems][producerPicklistOrderItems] : processed message: {}", message);
//            ack.acknowledge();
        }

    }


    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${kafka.topic.name.pickilist-order-items.temp.topic}", groupId = "${kafka.topic.name.pickilist-order-items.temp.topic" +
            ".group-id}")
    public void listenPicklistOrderItemsTemp(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) {

        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            log.info("[picklist_order_item_activity][listenPicklistOrderItemsTemp] :  The messageConsumed is {} and header {}",
                    message, messageHeader);
            if (isListnerEnabled) {
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[picklist_order_item_activity][listenPicklistOrderItemsTemp] : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
        } catch (Exception e) {
            log.error("[picklist_order_item_activity][listenPicklistOrderItemsTemp] exception caught  : {}", e.getMessage(), e);
        } finally {
            log.info("[listenOrderItems][listenPicklistOrderItemsTemp] : processed message: {}", message);
            ack.acknowledge();
        }

    }


    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${kafka.topic.name.jit-order-status-details.temp.topic}", groupId = "${kafka.topic.name.jit-order-status-details.temp.topic" +
            ".group-id}")
    public void listenJitOrderStatusDetailsTemp(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) {
        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            log.info("[jit_order_status_details_activity][listenJitOrderStatusDetailsTemp] The messageConsumed is {} and header {}",
                    message, messageHeader);
            if (isListnerEnabled) {
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[jit_order_status_details_activity][listenJitOrderStatusDetailsTemp] : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
        } catch (Exception e) {
            log.error("[jit_order_status_details_activity][listenJitOrderStatusDetailsTemp] exception caught  : {}", e.getMessage(), e);
        } finally {
            log.info("[listenOrderItems][listenJitOrderStatusDetailsTemp] : processed message: {}", message);
            ack.acknowledge();
        }

    }


    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${kafka.topic.name.jit-order-status-details}", groupId = "${kafka.topic.name.jit-order-status-details-group-id}")
    public void producerJitOrderStatusDetails(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) throws Exception {
        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            log.info("[jit_order_status_details_activity][producerJitOrderStatusDetails] The messageConsumed is {} and header {}",
                    message, messageHeader);
            if (isListnerEnabled) {
                //TODO temeporary fixes for kafka partition
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                log.info("[jit_order_status_details_activity][producerJitOrderStatusDetails] The messageConsumed is {} and header {}",
                        message, messageHeader);
                if (isProducerEnabled) {
                    log.info("[jit_order_status_details_activity][producerJitOrderStatusDetails] isProducerEnabled  {} started", isProducerEnabled);
                    String text = gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                    JitOrderStatusDetailsDto jitOrderStatusDetailsDto = ObjectHelper.getObjectMapper().readValue(text, JitOrderStatusDetailsDto.class);
                    kafkaProducerUtils.sendAndGet(jitOrderStatusDetailsTempTopic,
                            String.valueOf(jitOrderStatusDetailsDto.getFittingId()), message);
                } else {
                    MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                    monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                }
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[jit_order_status_details_activity][producerJitOrderStatusDetails] : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
            ack.acknowledge();
        } catch (Exception e) {
            log.error("[jit_order_status_details_activity][producerJitOrderStatusDetails] exception caught  : {}", e.getMessage(), e);
            throw e;
        } finally {
            log.info("[listenOrderItems][producerJitOrderStatusDetails] : processed message: {}", message);
//            ack.acknowledge();
        }
    }
}
