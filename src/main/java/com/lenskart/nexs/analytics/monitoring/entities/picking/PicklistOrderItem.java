package com.lenskart.nexs.analytics.monitoring.entities.picking;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "picklist_order_item")
public class PicklistOrderItem implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "increment_id")
    private Integer incrementId;

    @Column(name = "product_id")
    private Integer productId;

    @Column(name = "wms_order_id")
    private Integer wmsOrderId;

    @Column(name = "wms_order_item_id")
    private Integer wmsOrderItemId;

    @Column(name = "wms_order_code")
    private String wmsOrderCode;

    @Column(name = "shipment_id")
    private String shipmentId;

    @Column(name = "scm_order_id")
    private Integer scmOrderId;

    @Column(name = "scm_order_item_id")
    private Integer scmOrderItemId;

    @Column(name = "status")
    private Integer status = 0;

    @Column(name = "location_barcode")
    private String locationBarcode;

    @Column(name = "location_hierarchy", columnDefinition = "JSON")
    private String locationHierarchy;

    @Column(name = "no_item_per_order")
    private Integer noItemPerOrder;

    @Column(name = "no_product")
    private Integer noProduct;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "product_image")
    private String productImage;

    @Column(name = "channel")
    private String channel;

    @Column(name = "product_type")
    private String productType;

    @Column(name = "es_sync_log")
    private String esSyncLog;

    @Column(name = "order_type")
    private String orderType;

    @Column(name = "priority")
    private Integer priority;

    @Column(name = "facility")
    private String facility;

    @Column(name = "order_state")
    private String orderState; // Order status

    @Column(name = "fitting_id")
    private Integer fittingId;

    @Column(name = "processing_type")
    private String processingType;

    @Column(name = "item_type")
    private String itemType;

    @Column(name = "fitting")
    private String fitting;

    @Column(name = "jit_order")
    private Boolean jitOrder;

    @Column(name = "fast_picking")
    private Boolean fastPicking;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "scm_order_created_at")
    private Date scmOrderCreatedAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false)
    private Date createdAt;

    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    @Field(value = "updatedDate", type = FieldType.Date, format = DateFormat.custom, pattern = "uuuu-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt = Calendar.getInstance().getTime();

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Version
    @Column(name = "version")
    private Integer version;

    @Column(name = "repick_status")
    private String repickStatus;

    @Column(name = "repick_count")
    private Integer repickCount;

    @Column(name = "location_type")
    private String locationType;
}
