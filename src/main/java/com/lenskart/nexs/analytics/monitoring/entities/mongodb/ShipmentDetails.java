package com.lenskart.nexs.analytics.monitoring.entities.mongodb;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Document(
        collection = "shipment_details"
)
@CompoundIndexes({@CompoundIndex(
        name = "ux_shipping_package_id_facility_code",
        unique = true,
        def = "{'shipping_package_id' : 1, 'facility_code' : 1}"
)})
@Data
public class ShipmentDetails {
    @Id
    @Field
    private ObjectId id;
    @Indexed
    @Field("shipping_package_id")
    private String shippingPackageId;
    @Indexed
    @Field("facility_code")
    private String facilityCode;
    @Indexed
    @Field("increment_id")
    private Integer incrementId;
    @Field("invoice_code")
    private String invoiceCode;
    @Indexed
    @Field("shipping_provider_code")
    private String shippingProviderCode;
    @Indexed
    @Field("shipping_method")
    private String shippingMethod;
    @Indexed
    @Field("shipping_package_type")
    private String shippingPackageType;
    @Indexed
    @Field("tracking_number")
    private String trackingNumber;
    @Indexed
    @Field("master_tracking_number")
    private String masterTrackingNumber;
    @Field("tracking_status")
    private String trackingStatus;
    @Field("courier_status")
    private String courierStatus;
    @Field("shipping_manifest_code")
    private String shippingManifestCode;
    @Field("shipping_manifest_id")
    private String shippingManifestId;
    @Indexed
    @Field("channel")
    private String channel;
    @Field("picklist_number")
    private Integer picklistNumber;
    @Field("status")
    private String status;
    @Field("account_code")
    private String accountCode;
    @Field("client_id")
    private String clientId;
    @Field("company_name")
    private String companyName;
    @Field("product_code")
    private String productCode;
    @Field("created_at")
    private Date createdAt;
    @Field("updated_at")
    private Date updatedAt;
    @Field("created_by")
    private Integer createdBy;
    @Field("updated_by")
    private Integer updatedBy;
    @Field("dispatched_at")
    private Date dispatchedAt;
    @Field("delivered_at")
    private Date deliveredAt;
    @Field("lk_country")
    private String lkCountry;
    @Field("total_shipment_count")
    private Integer totalShipmentCount;
    @Field("total_amount")
    private Double totalAmount;
}