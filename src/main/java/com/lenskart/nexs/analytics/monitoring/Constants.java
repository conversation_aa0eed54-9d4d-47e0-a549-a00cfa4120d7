package com.lenskart.nexs.analytics.monitoring;

import com.lenskart.nexs.wms.constants.ErrorType;

import java.util.*;

public class Constants {

    public static final String UPDATE_GROUP_STATUS = "/update/groupStatus";
    public static final String CLEAN_DATA = "/clean/staleData";
    public static final String HOMEPAGE_PATH="/homePage";
    public static final String HOMEPAGE_PATH_V2="/v2/homePage";
    public static final String HOMEPAGE_PATH_V3="/v3/homePage";
    public static final String DETAILS_PAGE_PATH="/details";
    public static final String DETAILS_PAGE_PATH_V2 = "/v2/details";
    public static final String DETAILS_PAGE_PATH_V3 = "/v3/details";
    public static final String SUMMARY_HOME_PAGE = "summary/homePage";
    public static final String DAILY_REPORT_SYNC = "dailyReportSync";
    public static final String DAILY_REPORT_SYNC_FOR_ORDER_ID = "v2/dailyReportSync";
    public static final String DELIMITER=":::";
    public static final String NA="NA";
    public static final String LISTING_PAGE_TAG="tag";
    public static final String TAG_NAME_KEY="tagName";
    public static final String FR_TAG="Fr Tag";
    public static final String LISTING_PAGE_DEFAULT_TAG="Other";
    public static final String LISTING_PAGE_STATUS="status";
    public static final String PICKING_PRIORITY = "priority";
    public static final String LISTING_PAGE_TOTAL_COUNT="totalCount";
    public static final String UNWANTED_STATUS_LIST="CANCELLED,REASSIGNED,DISPATCHED,RETURNED,DELIVERED";
    public static final String FR0_TAG_PREFIX="fr0";
    public static final String FR1_TAG_PREFIX="fr1";
    public static final String FR2_TAG_PREFIX="fr2";
    public static final String TOTAL_KEY="TOTAL";
    public static final String FRAME = "FRAME";
    public static final String UNFULFILLABLE_LENS="UNFULFILLABLE_LENS";
    public static final String UNFULFILLABLE_FRAME="UNFULFILLABLE_FRAME";
    public static final String FULLFILLABLE_CATEGORY="FULLFILLABLE_CATEGORY";
    public static final String FULFILLABILITY="fulfillability";
    public static final String FR1_STATUS_REVERSE_LOOKUP="fr1statusReverseLoopkup";
    public static final String FR0_STATUS_REVERSE_LOOKUP="fr0statusReverseLoopkup";
    public static final String FRTAG_REVERSE_LOOKUP="frtagReverseLookup";
    public static final String FULFILLABILITY_REVERSE_LOOKUP="fullfillabilityReverseLookup";
    public static final String FULFILLABILITY_REVERSE_LOOKUP_V3="fullfillabilityReverseLookupV3";
    public static final String COLUMNS_NODE="COLUMNS";
    public static final String FR_TAG_NODE="FR_TAG";
    public static final String HEADERS_NODE="headers";
    public static final int DEFAULT_FULLFILLABILITY=0;
    public static final String DATE_12_HOUR_FORMAT="dd-MM-yyyy HH:mm:ss aa";
    public static final String DATE_24_HOUR_FORMAT="dd-MM-yyyy HH:mm:ss";
    public static final String OTHER_FR_TAG_LIST="otherFrTagList";
    public static final String MONITOR_TABLE_UPDATED_AT_KEY = "MONITOR_TABLE_UPDATED_AT_KEY";
    public static final String MONITOR_TABLE_1 = "MONITOR_TABLE_1";
    public static final String MONITOR_TABLE_2 = "MONITOR_TABLE_2";
    public static final String REPORT_AS_ON = "reportAsOn";
    public static final String OTHERS = "OTHERS";
    public static final String GMT_TIME = "GMT";
    public static final String START_TIME = "18:30:00";
    public static final String END_TIME = "18:29:59";
    public static final String DEFAULT_TIME_ZONE = "Asia/Kolkata";
    public static final int ARBITRARY_END_DAY = 60;
    public static final String JIT_EXTERNAL_VENTOR = "EXTERNAL VENDOR";
    public static final String JIT_MANUAL = "MANUAL";
    public static final String JIT_LENS_LAB = "Lens Lab";
    public static final String LENS_LAB = "LENS LAB";
    public static final String CURRENT = "current";
    public static final String SEVERE = "severe";
    public static final String STANDARD = "standard";
    public static final String CRITICAL = "critical";
    public static final String FRANCHISE_BULK = "FranchiseBulk";
    public static final String BULK = "BULK";
    public static final String MARKET_PLACE_ORDER = "MPDTC";
    public static final String POPULATE_INVOICE_DATE = "populate-invoice_date";
    public static final String SUPER_ORDER = "SUPER_ORDER";
    public static final String PUSH_TO_NEXS_SALES_ORDER_REPORT = "nexs-sales-order";
    public static final Integer SHIPMENT_ADDRESS_TYPE = 1;
    public static final String PUSH_TO_NEXS_SALES_ORDER_TOPIC = "nexs-sales-topic";

    public static final String FETCH_OPERATION_DETAILS = "/fetchOperationDetails";

    public static final String NEW_CREATED_AT = "NEW_CREATED_AT";

    public static final String FETCH_ORDER_ACTIVITY = "/fetch/order-activity";
    public static final String FETCH_MANUAL_ASRS_WAVE_COUNT = "/fetch/manual/ars/wave/count";
    public static final String SUCCESS = "SUCCESS";
    public static final Integer PAGE_NO = 0;
    public static final List<String> binaryFiltersList = Arrays.asList("isJit","isTrueLastPiece",
            "shipToCustomer","paymentNotCaptured","exchangeFlag","powerFollowUpFlag","vsmHoldFlag","isFulfillable", "isAsrsOrder", "isSuperOrder","isLensOnlyOrder","isTrueLastPiece","isMarketPlaceOrder","isOwndaysOrder","isInternationalOrder");

    public static final List<String> mongoCallEligibleStatus = Arrays.asList("SHIPMENT_DISPATCHED", "AWB_GENERATED", "INVOICE_GENERATED");
    public static final List<String> statusList = Arrays.asList("Synced",
            "Pending Picking",
            "In Picking",
            "Lens In Picking",
            "Tray Making",
            "Lens Tray Making",
            "MEI",
            "Fitting",
            "Order QC",
            "Packing",
            "Manifest",
            "Ready To Ship",
            "QC Fail:::Pending Picking",
            "QC Fail:::In Picking",
            "QC Fail:::Lens In Picking",
            "QC Fail:::Tray Making",
            "QC Fail:::Lens Tray Making",
            "QC Fail:::MEI",
            "QC Fail:::Fitting",
            "Un Classified",
            "Shipment Not Generated",
            "Super Order"
    );
    public static final String SHIPMENT_NOT_GENERATED_STATUS = "Shipment Not Generated";
    public static final Integer DEFAULT_PICKING_PRIORITY = 10;
    public static List<String> lensLabStatusList = Arrays.asList("CREATED", "TRAY MAKING", "OMA SYNCED", "SYNCED", "LENS LAB", "BLANK PICKING", "BLANK PID RECEIVED", "RXU REJECTED", "INVENTORY NOT AVAILABLE", "QC_FAIL:::INVENTORY NOT AVAILABLE", "QC_FAIL:::TRAY MAKING", "QC_FAIL:::OMA SYNCED", "QC_FAIL:::SYNCED", "QC_FAIL:::LENS LAB", "QC_FAIL:::BLANK PICKING", "QC_FAIL:::BLANK PID RECEIVED", "QC_FAIL:::RXU REJECTED", "QC_FAIL", "REASSIGNED", "OUT OF LENSLAB", "Un Classified");
    public static List<String> externalVendorStatusList = Arrays.asList("SYNCED","JIT_PO_CREATED", "LENS_RECEIVED", "PENDING_FOR_TRAY_MAKING", "ORDER_MOVE_TO_MEI", "TRAY_MAKING", "OMA_SYNCED_FAILED");
    public static final List<String> frTagList = Arrays.asList("FR1", "FR2", "FR0", "BULK", "OTHERS");
    public static final List<String> jitTagList = Arrays.asList("LENS LAB", "EXTERNAL VENDOR");
    public static final List<String> statusToSkip = Arrays.asList("Manifest", "Packing", "Order QC","Un Classified", "Ready To Ship");
    public static final List<String> multiSelectFiltersList = Arrays.asList("orderChannel","qcStatus","country","lensPackageType", "pickingPriority", "itemType");
    public static final List<String> singleSelectFilterList = Arrays.asList("errorType"); //Add filters if more single filters come in
//    public static final int ORDER_ITEMS_PAGE_SIZE = 1500;
    public static final int PICK_LIST_ORDER_ITEM_PAGE_SIZE = 900;
    public static final String QC = "QC";
    public static final String PASS = "Pass";
    public static final String FAIL = "Fail";
    public static final String HOLD = "Hold";
    public static final String V1 = "v1";
    public static final String V2 = "v2";
    public static final String V3 = "v3";
    public static final String DATE_FILTER_FORMAT="yyyy-MM-dd HH:mm:ss";
    public static final String EXCHANGE = "Exchange";
    public static final String SAVE_VIEWS = "saveViews";
    public static final String FETCH_VIEWS = "fetchViews";
    public static final String JIT_HOME_PAGE = "/jit/home";

    public static final String JIT_DETAILS_PAGE = "/jit/detail";
    public static final String PID_DETAILS_PAGE = "/pidOrderDetails";
    public static final String MONOTOR_PANEL_CRON_TRIGGER = "/monitorPanelUpdateTrigger";
    public static final String INVENTORY_NOT_FOUND_STATUS = "INVENTORY_NOT_FOUND";

    public static final List<String> ERROR_TYPE =  Arrays.asList(ErrorType.payment_mismatch.name(),
            ErrorType.pincode_mismatch.name(),
            ErrorType.payment_not_captured.name(),
            ErrorType.power_follow_up.name(),
            ErrorType.pincode_not_servicable.name());
    public static final String LEFT_LENS = "LEFTLENS";

    public static final String RIGHT_LENS = "RIGHTLENS";

    public static final String IN_PICKING = "In Picking";
    public static final String TRUE = "true";
    public static final String JIT_FLAG = "JIT_FLAG" ;
    public static final String JIT = "JIT";

    public static final String TRAY_MAKING = "Tray Making";

    public static final String LENS = "Lens";

    public static final String CRITICAL_START = "24";
    public static final String CRITICAL_END = "48";
    public static final String SEVERE_START = "48";
    public static final String SEVERE_END = "-1";
    public static final String WEBSITE_NAME = "Lenskart Solutions Pvt Ltd";
    public static final String B2B_STORE_NAME_WEBB2B = "Dealskart Online Service Pvt Ltd";

    public static final String DAILY_REPORT_REPUSH = "dailyReport/repush";
    public static final String DAILY_REPORT_SCHEDULER = "dailyReport/retryScheduler";

    public static final String EVENT_BASED_SOURCE = "event";
    public static final String SCHEDULER_BASED_SOURCE = "scheduler";

    public static final String STORE_INVENTORY_TYPE = "STORE_INVENTORY_TYPE";
    public static final String TRUE_LAST_PIECE = "TLP";
    public static final String LAST_PIECE = "LP";
    public static final String LENS_ONLY_ORDER = "LO";
    public static final String STATUS = "status";
    public enum GroupType{
        FITTING_ID,SHIPPING_ID, INCREMENT_ID;
    }

    public enum Severity{
        CRITICAL, SEVERE, CURRENT;
    }
    public enum FulfillableType{

        TOTAL_ORDERS(-1),FULFILLABLE(0),UNFULFILLABLE_FRAME(1),UNFULFILLABLE_LENS(1);
        int fulfillability;

        private FulfillableType(int fulfillability)
        {
            this.fulfillability = fulfillability;
        }

        public int getFulfillability(){
            return this.fulfillability;
        }

    }

    public enum FulfillableTypeV3{

        TOTAL_ORDERS(-1),FULFILLABLE_ORDERS(0),UNFULFILLABLE_ORDERS(1), ALLOCATED_ORDERS(1);
        int fulfillability;

        private FulfillableTypeV3(int fulfillability)
        {
            this.fulfillability = fulfillability;
        }

        public int getFulfillability(){
            return this.fulfillability;
        }

    }

    public enum JitOrder{
        JIT_ORDERS;
    }

    public enum PowerType{

        SINGLE_VISION("Single Vision"),BIFOCAL("bifocal"),PROGRESSIVE("progressive");
        private String label;
        private PowerType(String label){
            this.label=label;
        }
        public String getLabel(){
            return this.label;
        }

    }

    public static final Map<String,String> tagsMap=new LinkedHashMap<String,String>() {{
        put("FR1:::FRAME", "FR1");
        put("PL:::LEFTLENS", "Prescription Lens");
        put("FR2:::SUNGLASS", "FR2");
        put("PL:::RIGHTLENS", "Prescription Lens");
        put("FR0:::SUNGLASS", "FR0_SG");
        put("FR0:::FRAME", "FR0_EG");


    }};

    public static final Map<String, String> frTagsMap = new LinkedHashMap<String, String>(){{
        put("FR1:::FRAME", "FR1");
        put("PL:::LEFTLENS", "FR1");
        put("FR2:::SUNGLASS", "FR2");
        put("PL:::RIGHTLENS", "FR1");
        put("FR0:::SUNGLASS", "FR0");
        put("FR0:::FRAME", "FR0");
    }};

    public static final Map<String,String> fr0StatusMap=new  LinkedHashMap<String,String>() {{
        put("CREATED", "Synced");
        put("PENDING_PICKING", "Pending Picking");
        put("IN_PICKING", "In Picking");
        put("PICKED", "Order QC");
        put("IN_QC", "Order QC");
        put("QC_HOLD", "Order QC");
        put("QC_FAILED", "Order QC");
        put("QC_DONE", "Packing");
        put("INVOICED", "Packing");
        put("AWB_CREATED", "Manifest");
        put("READY_TO_SHIP", "Ready To Ship");
        put("COMPLETE", "Manifest");
        put("POWER_CHANGED", "Un Classified");
        put("POWER_CHANGE_RELEASE", "Un Classified");
        put("FITTING_QC_HOLD", "Un Classified");
    }};


    public static final Map<String,String> fr1_fr2StatusMap=new LinkedHashMap<String,String>() {{
        put("CREATED", "Synced");
        put("JIT_PROCESSING", "JIT Processing");
        put("JIT_PR_GENERATED", "JIT Processing");
        put("JIT_PR_RAISED", "JIT Processing");
        put("PENDING_PICKING", "Pending Picking");
        put("IN_PICKING", "In Picking");
        put("PICKED", "Tray Making");
        put("PRODUCTION_DONE","Tray Making");
        put("BLANK_IN_TRAY","Tray Making");
        put("IN_TRAY", "MEI");
        put("EDGING", "MEI");
        put("PENDING_CUSTOMIZATION", "Fitting");
        put("CUSTOMIZATION_COMPLETE", "Order QC");
        put("IN_QC", "Order QC");
        put("QC_HOLD", "Order QC");
        put("QC_FAILED", "Order QC");
        put("QC_DONE", "Packing");
        put("INVOICED", "Packing");
        put("AWB_CREATED", "Manifest");
        put("READY_TO_SHIP", "Ready To Ship");
        put("COMPLETE", "Manifest");
        put("POWER_CHANGED", "Un Classified");
        put("POWER_CHANGE_RELEASE", "Un Classified");
        put("FITTING_QC_HOLD", "Un Classified");

    }};


    public static final Map<String,String> manualJitOrderStatusMap = new LinkedHashMap<String,String>(){{
        put("CREATED", "SYNCED");
        put("JIT_PROCESSING", "SYNCED");
        put("JIT_PR_GENERATED", "PO GENERATED");
        put("JIT_PR_RAISED", "PO RAISED");
        put("IN_PICKING", "INWARDED");
        put("PICKED", "TRAY MAKING");
        put("QC_FAIL:::JIT_PROCESSING", "QC_FAIL:::SYNCED");
        put("QC_FAIL:::JIT_PR_GENERATED", "QC_FAIL:::PO GENERATED");
        put("QC_FAIL:::JIT_PR_RAISED", "QC_FAIL:::PO RAISED");
        put("QC_FAIL:::IN_PICKING", "QC_FAIL:::INWARDED");
        put("QC_FAIL:::PICKED", "QC_FAIL:::TRAY MAKING");
    }};
    public static final Map<String,String> autoJitOrderStatusMap = new LinkedHashMap<String,String>(){{
        put("CREATED:::CREATED", "SYNCED");
        put("CREATED","SYNCED");
        put("P1:::IN_PROCESS:::ERROR_IN_SYNCING_TO_RX","SYNCED");
        put("CREATED:::OMA_GENERATED:::CREATED" , "OMA SYNCED");
        put("P:::IN_PROCESS:::OMA_GENERATED:::QC_FAILED" , "OMA SYNCED");
        put("P:::IN_PROCESS:::OMA_GENERATED:::CREATED" , "OMA SYNCED");
        put("QC_FAIL:::CREATED:::OMA_GENERATED:::CREATED","QC_FAIL:::OMA SYNCED");
        put("QC_FAIL:::CREATED:::OMA_GENERATED:::QC_FAIL","QC_FAIL:::OMA SYNCED");
        put("QC_FAIL:::IN_PROCESS:::OMA_GENERATED:::QC_FAIL","QC_FAIL:::OMA SYNCED");
        put("CREATED:::OMA_GENERATED:::QC_FAIL","QC_FAIL:::OMA SYNCED");
        put("OR:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "INVENTORY NOT AVAILABLE");
        put("OR:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "INVENTORY NOT AVAILABLE");
        put("OE:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "INVENTORY NOT AVAILABLE");
        put("OE:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "INVENTORY NOT AVAILABLE");
        put("OM:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "INVENTORY NOT AVAILABLE");
        put("OM:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "INVENTORY NOT AVAILABLE");
        put("P:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "INVENTORY NOT AVAILABLE");
        put("P:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "INVENTORY NOT AVAILABLE");
        put("P:::IN_PROCESS:::INVENTORY_NOT_FOUND:::CREATED" , "INVENTORY NOT AVAILABLE");
        put("P:::IN_PROCESS:::INVENTORY_NOT_FOUND:::QC_FAILED" , "INVENTORY NOT AVAILABLE");
        put("OM:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BLANK_IN_PICKING","INVENTORY NOT AVAILABLE");
        put("P1:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BLANK_IN_PICKING","INVENTORY NOT AVAILABLE");
        put("OR:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BLANK_IN_PICKING","INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OR:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OE:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OE:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OM:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OM:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::P:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::CREATED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::P:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::QC_FAILED", "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::P:::IN_PROCESS:::INVENTORY_NOT_FOUND:::CREATED" , "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::P:::IN_PROCESS:::INVENTORY_NOT_FOUND:::QC_FAILED" , "QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OM:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BLANK_IN_PICKING","QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::P1:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BLANK_IN_PICKING","QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::OR:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BLANK_IN_PICKING","QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("QC_FAIL:::BK:::READY_TO_PICK:::INVENTORY_NOT_FOUND:::BREAKAGE","QC_FAIL:::INVENTORY NOT AVAILABLE");
        put("OR:::READY_TO_PICK:::BLANK_DETAILS_RECEIVED:::CREATED" , "BLANK PID RECEIVED");
        put("OE:::READY_TO_PICK:::BLANK_DETAILS_RECEIVED:::CREATED" , "BLANK PID RECEIVED");
        put("OM:::READY_TO_PICK:::BLANK_DETAILS_RECIEVED:::BLANK_IN_PICKING","BLANK PID RECEIVED");
        put("OR:::READY_TO_PICK:::BLANK_DETAILS_RECIEVED:::BLANK_IN_PICKING","BLANK PID RECEIVED");
        put("QC_FAIL:::OM:::READY_TO_PICK:::BLANK_DETAILS_RECIEVED:::BLANK_IN_PICKING","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::OR:::READY_TO_PICK:::BLANK_DETAILS_RECIEVED:::BLANK_IN_PICKING","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::OR:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::ARO:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::BK:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::P1:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::ARI:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::HCI:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("QC_FAIL:::OM:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL:::BLANK PID RECEIVED");
        put("OR:::READY_TO_PICK:::INVENTORY_FOUND:::IN_PICKING", "BLANK PICKING");
        put("OE:::READY_TO_PICK:::INVENTORY_FOUND::BLANK_IN_PICKING","BLANK PICKING");
        put("P:::READY_TO_PICK:::INVENTORY_FOUND:::IN_PICKING", "BLANK PICKING");
        put("OE:::READY_TO_PICK:::INVENTORY_FOUND:::IN_PICKING", "BLANK PICKING");
        put("OM:::READY_TO_PICK:::INVENTORY_FOUND:::IN_PICKING", "BLANK PICKING");
        put("OE:::IN_PROCESS:::INVENTORY_FOUND:::PICKED" , "BLANK PICKING");
        put("OE:::IN_PROCESS:::INVENTORY_FOUND:::IN_TRAY" , "BLANK PICKING");
        put("P:::IN_PROCESS:::INVENTORY_FOUND:::PICKED" , "BLANK PICKING");
        put("P:::IN_PROCESS:::INVENTORY_FOUND:::IN_TRAY" , "BLANK PICKING");
        put("OR:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("OM:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("OM:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("P1:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("SH:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("OD:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("OR:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","BLANK PICKING");
        put("QC_FAIL:::P1:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OR:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OM:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::P1:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OR:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::ARO:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::BK:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::P1:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::ARI:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::HCI:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("OM:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OU:::READY_TO_PICK:::ERROR_AT_RXU:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::BK:::IN_PROCESS:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::SH:::READY_TO_PICK:::ERROR_AT_RXU:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::SH:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::READY_TO_PICK:::INVENTORY_FOUND:::QC_FAIL","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OM:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OM:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::ARO:::IN_PROCESS:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::SH:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OR:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::P1:::IN_PROCESS:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("QC_FAIL:::OR:::IN_PROCESS:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL:::BLANK PICKING");
        put("HCI:::IN_PROCESS:::INVENTORY_FOUND:::PICKED", "LENS LAB");
        put("HCI:::IN_PROCESS:::INVENTORY_FOUND:::IN_TRAY", "LENS LAB");
        put("OE:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("SH:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("ARO:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("HCI:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("ARI:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("BK:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("P1:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("HCO:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("OM:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("OR:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("OD:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","LENS LAB");
        put("SH:::IN_PROCESS:::BLANK_PICKED","LENS LAB");
        put("IN_PROCESS:::BLANK_PICKED","LENS LAB");
        put("SH:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","LENS LAB");
        put("ARO:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","LENS LAB");
        put("QC_FAIL:::OE:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OR:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::HCI:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::ARI:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::ARO:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::BK:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::SH:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::HCO:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OM:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::P1:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OU:::IN_PROCESS:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OU:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::P1:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::HCI:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::SH:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OU:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OU:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_IN_PICKING","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::BK:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::ARI:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::ARO:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::P1:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::SH:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::SH:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OU:::READY_TO_PICK:::ERROR_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::ARI:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::ARO:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::P1:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::BK:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("QC_FAIL:::OD:::READY_TO_PICK:::INVENTORY_FOUND:::BLANK_PICKED","QC_FAIL:::LENS LAB");
        put("SH:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE" , "TRAY MAKING");
        put("SH:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("OU:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("ARO:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("SH:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("P1:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("QC_FAIL:::BK:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("ARI:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("OU:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("HCI:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("ARO:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("ARI:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("OR:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("OE:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("BK:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("OD:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("OU:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("SH:::IN_PROCESS:::PRODUCTION_DONE","TRAY MAKING");
        put("OU:::IN_PROCESS:::PRODUCTION_DONE","TRAY MAKING");
        put("HCI:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("BK:::PRODUCTION_DONE:::PRODUCTION_DONE","TRAY MAKING");
        put("SH:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("HCI:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("OU:::REASSIGNED:::PRODUCTION_DONE","TRAY MAKING");
        put("ARI:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("ARO:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("OD:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","TRAY MAKING");
        put("OD:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("OU:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("OD:::CANCELLED:::PRODUCTION_DONE","TRAY MAKING");
        put("P1:::IN_PROCESS:::CANCELLED_AT_RXU:::PRODUCTION_DONE","TRAY MAKING");
        put("BK:::IN_PROCESS:::CANCELLED_AT_RXU:::PRODUCTION_DONE","TRAY MAKING");
        put("HCI:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("BK:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("ARI:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("ARO:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("OR:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","TRAY MAKING");
        put("P1:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","TRAY MAKING");
        put("QC_FAIL:::ARO:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::HCI:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("SH:::PRODUCTION_DONE:::PRODUCTION_DONE","TRAY MAKING");
        put("ARO:::PRODUCTION_DONE:::PRODUCTION_DONE","TRAY MAKING");
        put("OR:::PRODUCTION_DONE:::PRODUCTION_DONE","TRAY MAKING");
        put("HCI:::PRODUCTION_DONE:::PRODUCTION_DONE","TRAY MAKING");
        put("ARI:::PRODUCTION_DONE:::PRODUCTION_DONE","TRAY MAKING");
        put("QC_FAIL:::SH:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::HCI:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL::TRAY MAKING");
        put("QC_FAIL:::BK:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OE:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARO:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARI:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OD:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OR:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::READY_TO_PICK:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::HCI:::READY_TO_PICK:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::HCI:::PRODUCTION_DONE:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARO:::PRODUCTION_DONE:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::PRODUCTION_DONE:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::IN_PROCESS:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARI:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARO:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARO:::READY_TO_PICK:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OU:::READY_TO_PICK:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::P1:::READY_TO_PICK:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::P1:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OU:::IN_PROCESS:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::READY_TO_PICK:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::IN_PROCESS:::CANCELLED_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARI:::CREATED:::OMA_GENERATED:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARI:::READY_TO_PICK:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::BK:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OU:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::BK:::IN_PROCESS:::CANCELLED_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARI:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARO:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::P1:::PRODUCTION_DONE:::INVENTORY_FOUND:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::OR:::PRODUCTION_DONE:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARI:::PRODUCTION_DONE:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::SH:::IN_PROCESS:::OMA_GENERATED:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("QC_FAIL:::ARO:::READY_TO_PICK:::ERROR_AT_RXU:::PRODUCTION_DONE","QC_FAIL:::TRAY MAKING");
        put("CREATED:::QC_FAIL","QC_FAIL");
        put("OE:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::CREATED:::QC_FAIL","QC_FAIL");
        put("QC_FAIL:::SH:::IN_PROCESS:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::OU:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::OU:::READY_TO_PICK:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::BK:::IN_PROCESS:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::P1:::IN_PROCESS:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::SH:::IN_PROCESS:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::ARI:::IN_PROCESS:::INVENTORY_FOUND:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::HCI:::IN_PROCESS:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("QC_FAIL:::SH:::READY_TO_PICK:::INTERNAL_QC_FAIL:::BREAKAGE","QC_FAIL");
        put("OU:::READY_TO_PICK:::ERROR_AT_EXU:::CREATED", "RXU_REJECTED");
        put("P:::READY_TO_PICK:::ERROR_IN_SYNCING_TO_RX:::CREATED", "RXU_REJECTED");
        put("P:::READY_TO_PICK:::ERROR_IN_SYNCING_TO_RX:::QC_FAILED", "RXU_REJECTED");
        put("OD:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_IN_PICKING","RXU REJECTED");
        put("QC_FAIL:::OD:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_IN_PICKING","RXU REJECTED");
        put("OD:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("QC_FAIL:::OD:::IN_PROCESS:::CANCELLED_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("OU:::READY_TO_PICK:::ERROR_AT_RXU:::BLANK_IN_PICKING","RXU REJECTED");
        put("HCI:::CANCELLED:::PRODUCTION_DONE","RXU REJECTED");
        put("ARI:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("SH:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("OU:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("P1:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("HCI:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("ARO:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("BK:::IN_PROCESS:::ERROR_AT_RXU:::BLANK_PICKED","RXU REJECTED");
        put("OD:::IN_PROCESS:::CANCELLED_AT_RXU:::BREAKAGE","RXU REJECTED");
        put("OD:::IN_PROCESS:::CANCELLED_AT_RXU:::CREATED","RXU REJECTED");
        put("OU:::READY_TO_PICK:::ERROR_AT_RXU:::CREATED","RXU REJECTED");
        put("QC_FAIL:::OU:::READY_TO_PICK:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("OU:::READY_TO_PICK:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("QC_FAIL:::OD:::IN_PROCESS:::CANCELLED_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("OD:::IN_PROCESS:::CANCELLED_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("P1:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("QC_FAIL:::SH:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("SH:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("QC_FAIL:::OU:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("ARO:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("OU:::IN_PROCESS:::ERROR_AT_RXU:::PRODUCTION_DONE","RXU REJECTED");
        put("OU:::READY_TO_PICK:::ERROR_AT_RXU:::QC_FAIL","RXU REJECTED");
        put("QC_FAIL:::OU:::READY_TO_PICK:::ERROR_AT_RXU:::QC_FAIL","RXU REJECTED");
        put("QC_FAIL:::OD:::IN_PROCESS:::CANCELLED_AT_RXU:::BREAKAGE","RXU REJECTED");
        put("OD:::READY_TO_PICK:::ERROR_AT_RXU:::CREATED","RXU REJECTED");
        put("QC_FAIL:::OU:::IN_PROCESS:::ERROR_AT_RXU:::QC_FAIL","RXU REJECTED");
        put("QC_FAIL:::P1:::READY_TO_PICK:::ERROR_AT_RXU:::QC_FAIL","RXU REJECTED");
        put("QC_FAIL:::ARI:::IN_PROCESS:::ERROR_AT_RXU:::QC_FAIL","RXU REJECTED");
        put("QC_FAIL:::ARO:::IN_PROCESS:::ERROR_AT_RXU:::QC_FAIL","RXU REJECTED");
        put("P1:::READY_TO_PICK:::ERROR_AT_RXU:::CREATED","RXU REJECTED");
        put("QC_FAIL:::P1:::IN_PROCESS:::ERROR_AT_RXU:::QC_FAIL","RXU_REJECTED");
        put("QC_FAIL:::OD:::READY_TO_PICK:::ERROR_AT_RXU:::QC_FAIL","RXU_REJECTED");
        put("","Un Classified");
        put("CANCELLED:::CREATED","Un Classified");
        put("QC_FAIL:::SH:::CANCELLED:::PRODUCTION_DONE","Un Classified");
        put("SH:::CANCELLED:::PRODUCTION_DONE","Un Classified");
        put("QC_FAIL:::SH:::IN_PROCESS:::NULL:::PRODUCTION_DONE","Un Classified");
        put("SH:::IN_PROCESS:::NULL:::PRODUCTION_DONE","Un Classified");
        put("OU:::IN_PROCESS:::NULL:::PRODUCTION_DONE","Un Classified");
        put("QC_FAIL:::OD:::REASSIGNED:::BLANK_PICKED","Un Classified");
        put("OD:::REASSIGNED:::BLANK_PICKED","Un Classified");
        put("SH:::REASSIGNED:::BLANK_PICKED","Un Classified");
        put("OD:::REASSIGNED:::BREAKAGE","Un Classified");
        put("REASSIGNED:::CREATED","Un Classified");
        put("SH:::REASSIGNED:::PRODUCTION_DONE","Un Classified");
        put("QC_FAIL:::SH:::REASSIGNED:::PRODUCTION_DONE","Un Classified");
        put("HCI:::REASSIGNED:::PRODUCTION_DONE","Un Classified");
        put("QC_FAIL:::OD:::IN_PROCESS:::CANCELLED_AT_RXU:::QC_FAIL","Un Classified");
        put("OD:::CANCELLED:::BLANK_PICKED","Un Classified");
        put("QC_FAIL:::HCI:::REASSIGNED:::PRODUCTION_DONE","Un Classified");
        put("QC_FAIL:::SH:::REASSIGNED:::BREAKAGE","Un Classified");
        put("QC_FAIL:::SH:::REASSIGNED:::BLANK_PICKED","Un Classified");
        put("OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::BLANK_IN_PICKING","Un Classified");
        put("SH:::REASSIGNED_PENDING:::INVENTORY_FOUND:::BLANK_IN_PICKING","Un Classified");
        put("OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::BLANK_PICKED","Un Classified");
        put("OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::BREAKAGE","Un Classified");
        put("QC_FAIL:::OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::BLANK_IN_PICKING","Un Classified");
        put("QC_FAIL:::SH:::REASSIGNED_PENDING:::INVENTORY_FOUND:::BLANK_IN_PICKING","Un Classified");
        put("QC_FAIL:::OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::BLANK_PICKED","Un Classified");
        put("QC_FAIL:::OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::BREAKAGE","Un Classified");
        put("OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::CREATED","Un Classified");
        put("QC_FAIL:::OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::CREATED","Un Classified");
        put("REASSIGNED_PENDING:::CREATED","Un Classified");
        put("REASSIGNED_PENDING:::OMA_GENERATED:::CREATED","Un Classified");
        put("OD:::REASSIGNED_PENDING:::CANCELLED_AT_RXU:::PRODUCTION_DONE","Un Classified");
        put("READY_TO_PICK:::INVENTORY_FOUND:::QC_FAIL","Un Classified");
    }};


    public static final Map<String,Integer> jitGroupStatusPriorityMap = new HashMap<String,Integer>(){{
        put("CREATED",1);
        put("TRAY MAKING",2);
        put("OMA SYNCED",3);
        put("SYNCED",4);
        put("LENS LAB",5);
        put("BLANK PICKING",6);
        put("BLANK PID RECEIVED",7);
        put("RXU REJECTED",8);
        put("INVENTORY NOT AVAILABLE",9);
        put("QC_FAIL:::INVENTORY NOT AVAILABLE",19);
        put("QC_FAIL:::TRAY MAKING",12);
        put("QC_FAIL:::OMA SYNCED",13);
        put("QC_FAIL:::SYNCED",14);
        put("QC_FAIL:::LENS LAB",15);
        put("QC_FAIL:::BLANK PICKING",16);
        put("QC_FAIL:::BLANK PID RECEIVED",17);
        put("QC_FAIL:::RXU REJECTED",18);
        put("QC_FAIL",11);
        put("REASSIGNED",20);
        put("OUT OF LENSLAB",-1);
        put("OUT OF MANUAL JIT",-1);
        put("Un Classified",0);
        put("INWARDED",3);
        put("PO GENERATED",4);
        put("PO RAISED",5);
        put("QC_FAIL:::PO GENERATED",14);
        put("QC_FAIL:::PO RAISED",15);
        put("QC_FAIL:::INWARDED",13);
    }};


    public static final Map<String,Integer> statusPriorityMap = new HashMap(){{
                put("Synced",8);
                put("JIT Processing",9);
                put("Pending Picking",7);
                put("In Picking",6);
                put("Tray Making",5);
                put("MEI",4);
                put("Fitting",3);
                put("Order QC",2);
                put("Packing",1);
                put("Manifest",0);
                put("Ready To Ship",-1);
                put("OTHERS",-2);
                put("Un Classified",10);
                put("Shipment Not Generated", 11);
                put("Super Order", 12);
    }};
    public static final List<String> outOFLensLabWmsStatus = Arrays.asList("IN_TRAY","EDGING","PENDING_CUSTOMIZATION", "CUSTOMIZATION_COMPLETE","IN_QC", "QC_HOLD","QC_FAILED","QC_DONE","INVOICED", "AWB_CREATED","READY_TO_SHIP","COMPLETE", "POWER_CHANGED","POWER_CHANGE_RELEASE","FITTING_QC_HOLD");
    public static final String JIT_UN_CLASSIFIED = "Un Classified";
    public static final String JIT_OUT_OF_LENSLAB = "OUT OF LENSLAB";
    public static final String JIT_OUT_OF_MANUAL = "OUT OF MANUAL JIT";

    public static final Map<String,Integer> catagoryMap = new HashMap(){{
                put("FULFILLABLE_ORDERS", 0);
                put("UNFULFILLABLE_ORDERS",1);
                put("ALLOCATED_ORDERS",1);
                put("UNALLOCATED_ORDERS", 0);
    }};

    public static final Map<String,String> monitorPanelPredicates = new HashMap(){{
        put("FULFILLABLE_ORDERS", "isFulfillable");
        put("UNFULFILLABLE_ORDERS","isFulfillable");
        put("ALLOCATED_ORDERS","isAllocated");
    }};

    public static final List<String> nullShipmentStatus = Arrays.asList("IN_PICKING", "CREATED");

    public static final String SCHEDULER_KEY = "NXS_MONITOR_PANEL_SCHEDULER";
    public static final String COURIER_UPDATE_SCHEDULER_KEY = "NEXS_COURIER_CODE_UPDATE_SCHEDULER";
    public static final String SCHEDULER_START_VALUE = "IS_RUNNING";

    public static final String ORDER_ITEMS_TABLE = "order_items";
    public static final String PICKLIST_ORDER_ITEMS_TABLE = "picklist_order_item";
    public static final String PICKING_DETAIL_TABLE = "picking_detail";
    public static final String JIT_ORDER_STATUS_DETAILS_TABLE = "jit_order_status_details";
    public static final String CREATED_STATUS = "CREATED";
    public static final List<String> terminalStatusList = Arrays.asList("RETURNED","DISPATCHED","CANCELLED","REASSIGNED");
    public static final List<String> softCourierUpdateEligibleStatus = Arrays.asList("AWB_CREATED", "INVOICED");
    public static final String ORDER_ITEM_IDS = "orderItemIds";

    public static final List<String> qcExcludeStatus = Arrays.asList("AWB_CREATED", "INVOICED", "READY_TO_SHIP", "DISPATCHED");

    public static final List<String> shipmentLevelEvents = Arrays.asList("ORDER_CREATED","SHIPMENT_ID_GENERATED","SHIPMENT_READY_TO_SHIP", "AWB_GENERATED","FITTING_COMPLETE" , "MEI_COMPLETE", "PACKING_DONE", "MANIFEST_NUMBER_GENERATED", "INVOICE_GENERATED","MOVED_TO_MEI", "MOVED_TO_FITTING", "MOVED_FOR_QC", "QC_COMPLETED","QC_FAILED_MEI_COMPLETE", "SHIPMENT_DISPATCHED");
    public interface OrderActivityEventConstants{
        String REFERENCE_ID = "referenceId";
        String AWB_NUMBER = "awbNumber";
        String COURIER_CODE = "courierCode";
        String INVOICE_NUMBER = "invoiceNumber";
        String MANIFEST_NUMBER = "manifestNumber";
        String QC_FAIL = "QC_FAIL";
        String FULLFILLABLE_EVENT = "FULLFILLABLE";
        String UNFULFILLABLE_EVENT = "UNFULFILLABLE";
        String PENDING_CUSTOMIZATION = "PENDING_CUSTOMIZATION";
        String FITTING_HOLD_EVENT = "FITTING_HOLD";
        String FITTING_UNHOLD_EVENT = "FITTING_UNHOLD";
        String HOLD_EVENT = "HOLD";
        String READY_TO_SHIP_EVENT = "READY_TO_SHIP";
        String AWB_GENERATED_EVENT = "AWB_GENERATED";
        String INVOICED_EVENT = "INVOICED";
        String PACKED_EVENT = "PACKED";
        String MANIFEST_EVENT = "MANIFESTED";
        String PICKING_SUMMARY_EVENT = "PICKING_SUMMARY";
        String INVOICE_GENERATED_EVENT = "INVOICE_GENERATED";
        String QC_HOLD_EVENT = "QC_ON_HOLD";
        String MANIFEST_OPERATION = "IN_MANIFEST";
    }

    public static final List<String> holdEvents = Arrays.asList("QC_HOLD");
    public  static final List<Integer> nonAdverbPckingAndSummaryStatus = Arrays.asList(0,1);

}
