package com.lenskart.nexs.analytics.monitoring;

public class MonitorPanelDataCoulmnNameConstants {

    public static final String FR_TAG = "frTag";
    public static final String MP_STATUS = "mpStatus";
    public static final String IS_FULFILLABLE = "isFulfillable";
    public static final String AGEING_SINCE_CREATED = "ageingSinceCreated";
    public static final String AGEING_SINCE_LAST_UPDATE = "ageingSinceLastUpdate";
    public static final String ORDER_ITEM_CREATED_AT = "orderItemCreatedAt";
    public static final String FACILITY_CODE = "facilityCode";
    public static final String ERROR_TYPE = "errorType";
    public static final String GROUP_ID = "groupId";
    public static final String GROUP_EFFECTIVE_STATUS = "groupEffectiveStatus";
    public static final String PICKING_PRIORITY = "pickingPriority";

    public static final String V3_FR_TAG = "v3FrTag";
    public static final String JIT_GROUP_STATUS = "jitGroupStatus";
    public static final String JIT_TYPE = "jitType";
    public static final String IS_JIT = "isJit";
    public static final String PRODUCT_ID = "productId";
    public static final String FULFILLABLE = "fulfillable";
    public static final String UNFULFILLABLE = "unfulfillable";
    public static final String ALLOCATED = "allocated";
    public static final String TOTAL = "total";

}
