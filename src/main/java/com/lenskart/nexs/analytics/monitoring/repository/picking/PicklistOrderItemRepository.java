package com.lenskart.nexs.analytics.monitoring.repository.picking;

import com.lenskart.nexs.analytics.monitoring.entities.picking.PicklistOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface PicklistOrderItemRepository extends JpaRepository<PicklistOrderItem, Long> {

    List<PicklistOrderItem> findByScmOrderItemIdIn(List<Integer> orderItemId);
    List<PicklistOrderItem> findByShipmentId(String shipmentId);
}
