package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

import com.lenskart.nexs.wms.enums.AddressType;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = SqlTableConstants.ADDRESS)
public class Address extends BaseEntity {


    @NotNull
    @Column(name = "name")
    protected String name;

    @NotNull
    @Column(name = "address_line1")
    protected String addressLine1;

    @Column(name = "address_line2")
    protected String addressLine2;

    @NotNull
    @Column(name = "city")
    protected String city;

    @NotNull
    @Column(name = "state")
    protected String state;

    @NotNull
    @Column(name = "country")
    protected String country;

    @NotNull
    @Column(name = "pincode")
    protected String pincode;

    @NotNull
    @Column(name = "phone")
    protected String phone;

    @Column(name = "email")
    protected String email;

    @NotNull
    @Column(name = "address_type")
    private AddressType type;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "order_item_header_id", referencedColumnName = "id")
    private OrderItemHeader order_item_header_id;
}
