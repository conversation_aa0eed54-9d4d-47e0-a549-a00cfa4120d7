package com.lenskart.nexs.analytics.monitoring.mapper;

import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData1;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData2;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.pojo.DetailPageDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DetailPageDtoMapper {

    DetailPageDtoMapper INSTANCE= Mappers.getMapper(DetailPageDtoMapper.class);

    List<DetailPageDto> mapMonitorPanelData1(List<MonitorPanelData1> monitorPanelDataList);

    List<DetailPageDto> mapMonitorPanelData2(List<MonitorPanelData2> monitorPanelDataList);

    List<DetailPageDto> mapMonitorPanelDataNew(List<MonitorPanelNew> monitorPanelNewList);



}
