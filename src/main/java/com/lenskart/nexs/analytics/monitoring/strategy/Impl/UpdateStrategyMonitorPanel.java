package com.lenskart.nexs.analytics.monitoring.strategy.Impl;


import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import com.lenskart.nexs.analytics.monitoring.entities.mongodb.ShipmentDetails;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData2;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.entities.picking.PicklistOrderItem;
import com.lenskart.nexs.analytics.monitoring.entities.wms.*;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.mapper.MonitorPanelMapper;
import com.lenskart.nexs.analytics.monitoring.model.*;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.monitoring.strategy.MonitorPanelBaseStrategy;
import com.lenskart.nexs.analytics.util.MonitorPanelUtil;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UpdateStrategyMonitorPanel extends MonitorPanelBaseStrategy {

    @Override
    public QueryType supportedStates() {
        return QueryType.UPDATE;
    }

    @Value("${nexs.monitor.mongo.query.enable:false}")
    protected boolean isMongoDBQueryEnable;
    @Value("${nexs.monitor.jit.panel.data.process:false}")
    private boolean isJitPanelProcessEnable;

    @Value("${nexs.update.list.batch}")
    private int batchProcessingSize;

    /**
     * start point of update flow for monitor panel data
     *
     * @param requestPayload
     * @throws Exception
     */
    @Transactional(value = "monitorPanelDataTransactionManager", propagation = Propagation.REQUIRED , rollbackFor = Exception.class)
    @Override
    protected void execute(RequestPayload requestPayload) throws Exception {
        log.info("[UpdateStrategyMonitorPanel]: request payload received is : {}", requestPayload);
        this.processUpdateAction(requestPayload);
    }

    /**
     * updating the exiting order item data with the new status and revising group effective status for the entire shipment
     *
     * @param requestPayload
     */
    @Transactional(value = "monitorPanelDataTransactionManager", propagation = Propagation.REQUIRED , rollbackFor = Exception.class)
    public void processUpdateAction(RequestPayload requestPayload) throws Exception {

        try {
            MonitorPanelNew existingData = monitorPanelNewRepository.findByOrderItemId(requestPayload.getOrderItemId());
            if (ObjectUtils.isEmpty(existingData)) {
                existingData = new MonitorPanelNew();
            }

            log.info("[UpdateStrategyMonitorPanel] : item received for shipment id : {} ,  is : {}", requestPayload.getShippingPackageId(), existingData);

            Map<Integer, MonitorPanelPickingData> orderItemToPickingMap = new HashMap<>();
            Map<String, OrderItemData> groupStatusMap = new HashMap<>();
            Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap = new HashMap<>();
            Map<String, JitOrderItem> jitOrderItemStatusMap = new HashMap<>();
            Map<Integer, Integer> pickingPriorityMap = new HashMap<>();
            Map<String, CourierDetails> courierDetailsMap = new HashMap<>();
            List<MonitorPanelNew> monitorPanelNewList = new ArrayList<>();


            this.updatePickingPriorityAndPickingStatusMap(requestPayload, pickingPriorityMap, orderItemToPickingMap);
            this.createCourierDetailsMap(requestPayload.getShippingPackageId(), courierDetailsMap);

            if (ObjectUtils.isNotEmpty(existingData) && ObjectUtils.isNotEmpty(existingData.getOrderItemId())) {
                log.info("[UpdateStrategyMonitorPanel] : updating monitor panel data : {}", existingData);
                updateExistingItemData(existingData, requestPayload, orderItemToPickingMap, groupStatusMap, jitOrderStatusDetailsMap, jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap);
            } else {
                log.info("[UpdateStrategyMonitorPanel] : Creating new monitor panel data object with payload : {}", requestPayload);
                createNewEntryForOrderItem(requestPayload, existingData, orderItemToPickingMap, groupStatusMap, jitOrderStatusDetailsMap, jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap);
            }
            if(Constants.terminalStatusList.contains(existingData.getWmsStatus())){
                return;
            }
            existingData.setPickingStatus(orderItemToPickingMap.containsKey(existingData.getOrderItemId())?orderItemToPickingMap.get(existingData.getOrderItemId()).getStatus(): null);
            monitorPanelNewRepository.save(existingData);

            if(ObjectUtils.isNotEmpty(requestPayload.getJitOrderStatusDetails())){
                log.info("requestPayload.getJitOrderStatusDetails().getFittingId {}",requestPayload.getJitOrderStatusDetails().getFittingId());
                monitorPanelNewList = monitorPanelNewRepository.findAllByFittingId(requestPayload.getJitOrderStatusDetails().getFittingId());
                log.info("list of items received for jit order with wms order code value : {} ,  is : {}", requestPayload.getWmsOrderCode(), monitorPanelNewList.toString());
            }else if (ObjectUtils.isNotEmpty(existingData.getShippingPackageId())){
                log.info("existingData.getShippingPackageId {}",existingData.getShippingPackageId());
                monitorPanelNewList = monitorPanelNewRepository.findAllByShippingPackageId(existingData.getShippingPackageId());
                log.info("list of items received for non jit order with wms order code value : {} ,  is : {}", requestPayload.getWmsOrderCode(), monitorPanelNewList.toString());
            }else if (ObjectUtils.isNotEmpty(requestPayload.getWmsOrderCode())){
                log.info("requestPayload.getWmsOrderCode: {}", requestPayload.getWmsOrderCode());
                monitorPanelNewList = monitorPanelNewRepository.findAllByWmsOrderCode(requestPayload.getWmsOrderCode());
                log.info("list of items received for non jit order with wms order code value : {} ,  is : {}", requestPayload.getWmsOrderCode(), monitorPanelNewList.toString());
            }

            if (ObjectUtils.isNotEmpty(monitorPanelNewList)) {
                log.info("creating jit group status map for item list : {}", monitorPanelNewList.toString());
                if (ObjectUtils.isNotEmpty(monitorPanelNewList) && monitorPanelNewList.stream().anyMatch(MonitorPanelData::getIsJit)) {
                    List<Integer> jitOrderItemIds = monitorPanelNewList.stream().map(MonitorPanelNew::getOrderItemId).collect(Collectors.toList());
                    jitOrderStatusDetailsMap = this.createJitOrderItemMapForNewTable(jitOrderItemIds, requestPayload);
                    this.createJitOrderStatusDetailsMap(monitorPanelNewList, jitOrderStatusDetailsMap, jitOrderItemStatusMap, requestPayload);
                }
            }

            log.info("[UpdateStrategyMonitorPanel] : updating group effective status for monitor panel list with data : {}", monitorPanelNewList.toString());
            this.updateGroupStatus(pickingPriorityMap, monitorPanelNewList, requestPayload.getOrderItemId(), jitOrderItemStatusMap, groupStatusMap);
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel] : Exception caught with message : {}", e.getStackTrace(), e);
            throw new Exception("Exception caught while processing update ", e);
        }
    }

    /**
     * update group effective status for items in the shipment, order item is part of
     *
     * @param pickingPriorityMap
     * @param monitorPanelNewList
     * @param orderItemId
     */

    public void updateGroupStatus(Map<Integer, Integer> pickingPriorityMap, List<MonitorPanelNew> monitorPanelNewList, Integer orderItemId, Map<String, JitOrderItem> jitOrderItemStatusMap, Map<String, OrderItemData> groupStatusMap) throws Exception {
        log.info("[UpdateStrategyMonitorPanel][UpdateGroupStatus] : started for order item id : {} and group id : {}", orderItemId, monitorPanelNewList.get(0).getGroupId());
        try {
            if (monitorPanelNewList.isEmpty()) {
                throw new Exception("Exception caught while updating group status, empty list found");
            }

            for (MonitorPanelNew monitorPanelNew : monitorPanelNewList) {
                log.info("[UpdateStrategyMonitorPanel] : updating group effective status map for item id : {}", monitorPanelNew.getOrderItemId());
                monitorPanelDataService.createGroupEffectiveStatusMap(monitorPanelNew, groupStatusMap, pickingPriorityMap);
            }

            for (Map.Entry<String, OrderItemData> entry : groupStatusMap.entrySet()) {
                monitorPanelDataService.deriveGroupEffectiveStatus(entry.getValue());
            }
            log.info("group status for monitor panel data list : {} , is derived as : {}", monitorPanelNewList.toString(), groupStatusMap.get(monitorPanelNewList.get(0).getGroupId()));

            monitorPanelNewList = this.updateExistingListWithNewStatus(monitorPanelNewList, groupStatusMap);
            monitorPanelNewList = this.updateJitGroupEffectiveStatus(jitOrderItemStatusMap, monitorPanelNewList);
            monitorPanelNewRepository.saveAll(monitorPanelNewList);
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel][UpdateGroupStatus] : failing to update group status, error - " + e);
            throw new Exception("Exception caught while updating group status" + e);
        }
    }

    /**
     * create new entry for new order item if no existing entry is found.
     *
     * @param requestPayload
     * @param existingData
     */
    public void createNewEntryForOrderItem(RequestPayload requestPayload, MonitorPanelNew existingData, Map<Integer, MonitorPanelPickingData> orderItemToPickingMap, Map<String, OrderItemData> groupStatusMap, Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap, Map<String, JitOrderItem> jitOrderItemStatusMap, Map<Integer, Integer> pickingPriorityMap, Map<String, CourierDetails> courierDetailsMap) throws Exception {
        try {
            log.info("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : createNewEntryForOrderItem : creating new monitor panel data for payload : {}", requestPayload.getOrderItems());
            OrderItems orderItems = orderItemRepository.getOrderItemsByOrderItemId(requestPayload.getOrderItemId());
            //log.info("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : OrderItems data for order item id : {} ,  is : {}", requestPayload.getOrderItemId(), orderItems);
            if (ObjectUtils.isNotEmpty(requestPayload.getOrderItems())) {
                MonitorPanelUtil.convertDtoToOrderItem(requestPayload.getOrderItems(), orderItems);
            }else if (ObjectUtils.isNotEmpty(requestPayload.getPicklistOrderItem()) || ObjectUtils.isNotEmpty(requestPayload.getJitOrderStatusDetails())){
                if(StringUtils.isEmpty(requestPayload.getShippingPackageId()) && ObjectUtils.isNotEmpty(requestPayload.getOrderItemId())){
                    log.info("createNewEntryForOrderItem.getPicklistOrderItem {} {}",requestPayload.getOrderItemId()
                            ,requestPayload.getShippingPackageId());
                    requestPayload.setShippingPackageId(orderItems.getShippingPackageId());
                }
            }
            monitorPanelDataService.createMonitorPanelDataFromOrderItem(orderItems, existingData, orderItemToPickingMap, groupStatusMap, jitOrderStatusDetailsMap, jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap);
            log.info("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : createNewEntryForOrderItem : monitor panel data created is : {}", existingData);
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel][createNewEntryForOrderItem] : Exception caught while creating monitor panel new data for payload : {}"+requestPayload+"  with message : {} "+e.getMessage(),e);
            throw new Exception("Exception caught while creating new entry for order item " + e);
        }

    }


    /**
     * update existing order items with the updated status
     *
     * @param monitorPanelNewList
     * @param groupStatusMap
     */
    public List<MonitorPanelNew> updateExistingListWithNewStatus(List<MonitorPanelNew> monitorPanelNewList, Map<String, OrderItemData> groupStatusMap) throws Exception {
        try {
            log.info("[UpdateStrategyMonitorPanel][updateExistingListWithNewStatus] : updating group status for existing monitor panel data list : {}", monitorPanelNewList.toString());
            for (MonitorPanelNew monitorPanelNew : monitorPanelNewList) {
                if (groupStatusMap.containsKey(monitorPanelNew.getGroupId())) {
                    log.info("[UpdateStrategyMonitorPanel][updateExistingListWithNewStatus] : updating group effective status for the items corresponding to group id :{}", monitorPanelNew.getGroupId());
                    if (monitorPanelNew.getShippingPackageId() == null) {
                        monitorPanelNew.setGroupEffectiveStatus(Constants.SHIPMENT_NOT_GENERATED_STATUS);
                    } else {
                        monitorPanelNew.setGroupEffectiveStatus(groupStatusMap.get(monitorPanelNew.getGroupId()).getEffectiveStatus());
                    }
                    monitorPanelNew.setV3FrTag(groupStatusMap.get(monitorPanelNew.getGroupId()).getV3FrTag());
                    monitorPanelNew.setPickingPriority(groupStatusMap.get(monitorPanelNew.getGroupId()).getPickingPriority());
                    monitorPanelNew.setIsLensOnlyOrder(groupStatusMap.get(monitorPanelNew.getGroupId()).getLensOnlyOrder());
                    monitorPanelNew.setIsTrueLastPiece(groupStatusMap.get(monitorPanelNew.getGroupId()).getTrueLastPiece());
                    monitorPanelNew.setIsAsrsOrder(groupStatusMap.get(monitorPanelNew.getGroupId()).getIsAsrsOrder());
                    log.info("updating status for order item id : {} ,  with group status as  : {}", monitorPanelNew.getOrderItemId(), monitorPanelNew.getGroupEffectiveStatus());
                } else {
                    log.error("[UpdateStrategyMonitorPanel][updateExistingListWithNewStatus] : No entry found in map for group id : {}", monitorPanelNew.getGroupId());
                }
            }
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel][updateExistingListWithNewStatus] : failed to update existing data with error : {}", e.getMessage());
            throw new Exception("Exception caught while updating the existing data " + e);
        }
        log.info("[UpdateStrategyMonitorPanel][updateExistingListWithNewStatus] : updated group status for existing monitor panel data list : {}", monitorPanelNewList.toString());
        return monitorPanelNewList;
    }

    /**
     * updating group effective status for jit order items
     *
     * @param jitOrderItemMap
     * @param monitorPanelNewList
     * @return
     */
    public List<MonitorPanelNew> updateJitGroupEffectiveStatus(Map<String, JitOrderItem> jitOrderItemMap, List<MonitorPanelNew> monitorPanelNewList) throws Exception {
        log.info("[UpdateStrategyMonitorPanel][updateJitGroupEffectiveStatus] : updating jit group status for monitor panel data list : {}", monitorPanelNewList);
        for (MonitorPanelNew monitorPanelData : monitorPanelNewList) {
            if (ObjectUtils.isNotEmpty(monitorPanelData) && jitOrderItemMap.containsKey(monitorPanelData.getFittingId().toString())) {
                log.info("[UpdateStrategyMonitorPanel][updateJitGroupEffectiveStatus]  : updating group effective status for jit order with id : {}", monitorPanelData.getFittingId());
                monitorPanelData.setJitGroupStatus(jitOrderItemMap.get(monitorPanelData.getFittingId().toString()).getJitGroupStatus());
                monitorPanelData.setJitType(jitOrderItemMap.get(monitorPanelData.getFittingId().toString()).getJitType());
            } else {
                log.error("[UpdateStrategyMonitorPanel][updateJitGroupEffectiveStatus]  : No entry found in map for fitting id : {}", monitorPanelData.getFittingId());
            }
        }
        return monitorPanelNewList;
    }


    /**
     * creating courier details map
     *
     * @param shippingPackageId
     * @param courierDetailsMap
     */
    public void createCourierDetailsMap(String shippingPackageId, Map<String, CourierDetails> courierDetailsMap) throws Exception {
        log.error("[UpdateStrategyMonitorPanel][createCourierDetailsMap] : creating courier details map for shipment id : {}", shippingPackageId);
        try {
            if (this.isMongoDBQueryEnable && ObjectUtils.isNotEmpty(shippingPackageId)) {
                List<ShipmentDetails> shipmentDetailsList = shipmentDetailsRepository.findByShippingPackageIdIn(new ArrayList<>(Collections.singleton(shippingPackageId)));
                log.info("[UpdateStrategyMonitorPanel][createCourierDetailsMap] : list of shipment details received is : {}", shipmentDetailsList);
                for (ShipmentDetails shipmentDetails : shipmentDetailsList) {
                    CourierDetails courierDetails = new CourierDetails();
                    courierDetails.setManifestNumber(shipmentDetails.getShippingManifestId());
                    courierDetails.setCourierCode(shipmentDetails.getShippingProviderCode());
                    courierDetails.setAwbNumber(shipmentDetails.getTrackingNumber());
                    courierDetails.setInvoiceNumber(shipmentDetails.getInvoiceCode());
                    courierDetailsMap.put(shipmentDetails.getShippingPackageId(), courierDetails);
                }
            }
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel][createCourierDetailsMap] : Exception caught while creating map of shipment details with message : {}"+e.getMessage(), e);
            throw new Exception("Exception caught while creating courier details map " + e.getStackTrace());
        }
    }

    /**
     * updating picking priority and picking status map for all the items in the shipment
     *
     * @param requestPayload
     * @param pickingPriorityMap
     * @param orderItemIdToPickingStatusMap
     */
    public void updatePickingPriorityAndPickingStatusMap(RequestPayload requestPayload, Map<Integer, Integer> pickingPriorityMap, Map<Integer, MonitorPanelPickingData> orderItemIdToPickingStatusMap) throws Exception {
        log.info("[UpdateStrategyMonitorPanel][updatePickingPriorityAndPickingStatusMap] : generating picking priority map for payload : {}", requestPayload);
        try {
            Integer orderItemId = requestPayload.getOrderItemId();
            List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepository.findByShipmentId(requestPayload.getShippingPackageId());
            log.info("[UpdateStrategyMonitorPanel][updatePickingPriorityAndPickingStatusMap] : picklist order item list received is : {}", picklistOrderItemList);
            for (PicklistOrderItem picklistOrderItem : picklistOrderItemList) {
                MonitorPanelPickingData monitorPanelPickingData = new MonitorPanelPickingData(picklistOrderItem.getPriority(),picklistOrderItem.getStatus(),picklistOrderItem.getLocationType());
                orderItemIdToPickingStatusMap.put(picklistOrderItem.getScmOrderItemId(), monitorPanelPickingData);
                pickingPriorityMap.put(picklistOrderItem.getScmOrderItemId(), picklistOrderItem.getPriority());
            }
            if (!ObjectUtils.isEmpty(requestPayload.getPicklistOrderItem())) {
                log.info("[UpdateStrategyMonitorPanel][updatePickingPriorityAndPickingStatusMap] : picking map is updated for request with payload : {}", requestPayload);
                MonitorPanelPickingData monitorPanelPickingData = new MonitorPanelPickingData(requestPayload.getPicklistOrderItem().getPriority(),requestPayload.getPicklistOrderItem().getStatus(),requestPayload.getPicklistOrderItem().getLocationType());
                orderItemIdToPickingStatusMap.put(orderItemId, monitorPanelPickingData);
                pickingPriorityMap.put(orderItemId, requestPayload.getPicklistOrderItem().getPriority());
            }
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel][updatePickingPriorityAndPickingStatusMap] : failed to update picking priority map with error : {}", e.getMessage(), e);
            throw new Exception("Exception caught while updating picking priority map" + e.getStackTrace());
        }
    }


    /**
     * update the existing data items
     *
     * @param existingData
     * @param requestPayload
     */
    public void updateExistingItemData(MonitorPanelNew existingData, RequestPayload requestPayload, Map<Integer, MonitorPanelPickingData> orderItemToPickingStatusMap, Map<String, OrderItemData> groupStatusMap, Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap, Map<String, JitOrderItem> jitOrderItemStatusMap, Map<Integer, Integer> pickingPriorityMap, Map<String, CourierDetails> courierDetailsMap) throws Exception {
        try {
            Optional<OrderItems> orderItems1;
            OrderItems orderItems;
            if (existingData.getOrderItemId().equals(requestPayload.getOrderItemId())) {
                if (!ObjectUtils.isEmpty(requestPayload.getOrderItemAfterData())) {
                    log.info("[UpdateStrategyMonitorPanel] : updating monitor panel data for order item event");
                    orderItems = super.orderItemRepository.getOrderItemsByOrderItemId(requestPayload.getOrderItemId());
                    log.info("[UpdateStrategyMonitorPanel][processUpdateAction] : OrderItems data for order item event order item id : {} ,  is : {}", requestPayload.getOrderItemId(), orderItems);
                    MonitorPanelUtil.convertDtoToOrderItem(requestPayload.getOrderItems(), orderItems);
                    if (ObjectUtils.isEmpty(orderItems.getShippingPackageId()) && Objects.nonNull(requestPayload.getShippingPackageId())) {
                        orderItems.setShippingPackageId(requestPayload.getShippingPackageId());
                    }
                    MonitorPanelUtil.convertDtoToOrderItem(requestPayload.getOrderItemAfterData(), orderItems);
                    orderItems.setShippingPackageId(requestPayload.getShippingPackageId());
                    monitorPanelDataService.createMonitorPanelDataFromOrderItem(orderItems, existingData, orderItemToPickingStatusMap, groupStatusMap, jitOrderStatusDetailsMap, jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap);
                } else if (!ObjectUtils.isEmpty(requestPayload.getPicklistOrderItem()) || !ObjectUtils.isEmpty(requestPayload.getJitOrderStatusDetails())) {
                    log.info("[UpdateStrategyMonitorPanel] : updating monitor panel data for picklist order item event");
                    orderItems = super.orderItemRepository.getOrderItemsByOrderItemId(requestPayload.getOrderItemId());
                    if (ObjectUtils.isEmpty(orderItems.getShippingPackageId()) && Objects.nonNull(requestPayload.getShippingPackageId())) {
                        orderItems.setShippingPackageId(requestPayload.getShippingPackageId());
                    }
                    log.info("[UpdateStrategyMonitorPanel][processUpdateAction] : OrderItems data for picking event for order item id : {} ,  is : {}", requestPayload.getOrderItemId(), orderItems);
                    monitorPanelDataService.createMonitorPanelDataFromOrderItem(orderItems, existingData, orderItemToPickingStatusMap, groupStatusMap, jitOrderStatusDetailsMap, jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap);
                }
            }
        } catch (Exception e) {
            log.error("[UpdateStrategyMonitorPanel][updateExistingItemData]failed to update existing data with error : {}", e.getMessage(), e);
            throw new Exception("Exception caught while updating the existing data" + e);
        }

    }


    /**
     * create a map of jit order items
     *
     * @param
     * @return
     */

    public Map<String, JitOrderStatusDetails> createJitOrderItemMapForNewTable(List<Integer> jitOrderIds, RequestPayload requestPayload) {
        Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap = new HashMap<>();
        if (isJitPanelProcessEnable) {
            List<String> jitIds = new ArrayList<>();
            for (Integer jitOrderId : jitOrderIds) {
                if (jitOrderId != null) {
                    jitIds.add(jitOrderId.toString());
                }
            }
            List<JitOrderStatusDetails> jitOrderItemStatusList = super.jitDbRepository.findBySourceReferenceIdInAndOrderTypeEquals(jitIds, Constants.JIT);
            log.info("checking if the list received from jitdb is empty : {}", jitOrderItemStatusList == null);
            for (JitOrderStatusDetails jitOrderStatusDetail : jitOrderItemStatusList) {
                jitOrderStatusDetailsMap.put(jitOrderStatusDetail.getSourceReferenceId(), jitOrderStatusDetail);
            }

            if (!ObjectUtils.isEmpty(requestPayload.getJitOrderStatusDetails())) {
                JitOrderStatusDetails jitOrderStatusDetails = MonitorPanelMapper.PANEL_MAPPER.jitOrderStatusDetailsDtoMapper(requestPayload.getJitOrderStatusDetails());
                jitOrderStatusDetailsMap.put(requestPayload.getOrderItemId().toString(), jitOrderStatusDetails);
            }
        }
        log.info("jit status map created : {}", jitOrderStatusDetailsMap);
        return jitOrderStatusDetailsMap;
    }


    public void createJitOrderStatusDetailsMap(List<MonitorPanelNew> monitorPanelNewList, Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap, Map<String, JitOrderItem> jitOrderItemStatusMap, RequestPayload requestPayload) {

        boolean isJit;
        for (MonitorPanelNew monitorPanelNew : monitorPanelNewList) {
            isJit = monitorPanelNew.getIsJit();
            if (isJit && jitOrderStatusDetailsMap.containsKey(monitorPanelNew.getOrderItemId().toString())) {
                JitOrderStatusDetails jitOrderStatusDetail = jitOrderStatusDetailsMap.get(monitorPanelNew.getOrderItemId().toString());
                String jitType = jitOrderStatusDetail.getOrderSubType() == null ? Constants.JIT_LENS_LAB : Constants.JIT_MANUAL.equalsIgnoreCase(jitOrderStatusDetail.getOrderSubType()) ? Constants.JIT_EXTERNAL_VENTOR : Constants.JIT_LENS_LAB;
                String jitStatus = monitorPanelDataService.getJitStatus(jitOrderStatusDetail, monitorPanelNew, jitType);
                monitorPanelNew.setJitGroupStatus(jitStatus);
                monitorPanelNew.setJitStatus(jitStatus);
                if (Constants.INVENTORY_NOT_FOUND_STATUS.equalsIgnoreCase(jitOrderStatusDetail.getSubStatus())) {
                    monitorPanelNew.setBlankPid(jitOrderStatusDetail.getBlankPid());
                }
                monitorPanelNew.setJitType(jitType);
                monitorPanelDataService.createJitGroupEffectiveStatusMap(monitorPanelNew, jitOrderItemStatusMap);
            }
        }

    }


    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    public void validateAndUpdateGroupEffectiveStatus(List<String> shipmentIds) throws Exception {
        shipmentIds = shipmentIds.stream()
                .distinct()
                .collect(Collectors.toList());

        log.info("updating group effective status for shipment ids in  : {} ", shipmentIds );

        try {
            RequestPayload requestPayload = new RequestPayload();
            int currentBegin = 0;

            int totalSize =shipmentIds.size();
            int numberOfBatches = (int) Math.ceil((double) totalSize / batchProcessingSize);

            for (int i = 0; i < numberOfBatches; i++) {
                int start = i * batchProcessingSize;
                int end = Math.min(start + batchProcessingSize, totalSize);
                List<String> currentBatchToDelete = shipmentIds.subList(start, end);
                for(String shipmentId : currentBatchToDelete){
                    log.info("[validateAndUpdateGroupEffectiveStatus] updating group status for shipment : {}", shipmentId);
                    requestPayload.setShippingPackageId(shipmentId);
                    updateGroupStatusFromWms(requestPayload);
                }
                currentBegin+=batchProcessingSize;
                batchProcessingSize+=currentBegin;
            }
        }catch(Exception e){
                log.error("exception caught while updating group status for shipment with message : {}", e.getMessage(),e);
            }
        }


        public void updateGroupStatusFromWms(RequestPayload requestPayload) throws Exception {
            Map<String, CourierDetails> courierDetailsMap = new HashMap<>();
            Map<Integer, MonitorPanelPickingData> orderItemToPickingMap = new HashMap<>();
            Map<String, OrderItemData> groupStatusMap = new HashMap<>();
            Map<String, JitOrderStatusDetails> jitOrderStatusDetailsMap = new HashMap<>();
            Map<String, JitOrderItem> jitOrderItemStatusMap = new HashMap<>();
            Map<Integer, Integer> pickingPriorityMap = new HashMap<>();
            List<MonitorPanelNew> monitorPanelNewList = new ArrayList<>();

            List<OrderItems> orderItemsList = orderItemRepository.findAllByShippingPackageId(requestPayload.getShippingPackageId());
            log.info("order items list for shipment : {}  , received is  : {}", requestPayload.getShippingPackageId(), orderItemsList);
            this.updatePickingPriorityAndPickingStatusMap(requestPayload, pickingPriorityMap, orderItemToPickingMap);
            this.createCourierDetailsMap(requestPayload.getShippingPackageId(), courierDetailsMap);
            monitorPanelNewList = monitorPanelNewRepository.findAllByWmsOrderCode(orderItemsList.get(0).getWmsOrderCode());
            if (ObjectUtils.isNotEmpty(monitorPanelNewList) && monitorPanelNewList.stream().anyMatch(x -> x.getIsJit())) {
                List<Integer> jitOrderItemIds = monitorPanelNewList.stream().map(MonitorPanelNew::getOrderItemId).collect(Collectors.toList());
                jitOrderStatusDetailsMap = this.createJitOrderItemMapForNewTable(jitOrderItemIds, requestPayload);
                this.createJitOrderStatusDetailsMap(monitorPanelNewList, jitOrderStatusDetailsMap, jitOrderItemStatusMap, requestPayload);
            }
            Map<Integer, MonitorPanelNew> orderItemToMonitorPanelDataMap = monitorPanelNewList.stream().collect(Collectors.toMap(MonitorPanelNew::getOrderItemId, data -> data));


            for (OrderItems orderItems : orderItemsList) {
                monitorPanelDataService.createMonitorPanelDataFromOrderItem(orderItems, orderItemToMonitorPanelDataMap.get(orderItems.getOrderItemId()), orderItemToPickingMap, groupStatusMap, jitOrderStatusDetailsMap, jitOrderItemStatusMap, pickingPriorityMap, courierDetailsMap);
            }


            for (Map.Entry<String, OrderItemData> entry : groupStatusMap.entrySet()) {
                monitorPanelDataService.deriveGroupEffectiveStatus(entry.getValue());
            }

            monitorPanelNewList = this.updateExistingListWithNewStatus(monitorPanelNewList, groupStatusMap);
            monitorPanelNewList = this.updateJitGroupEffectiveStatus(jitOrderItemStatusMap, monitorPanelNewList);
            monitorPanelNewRepository.saveAll(monitorPanelNewList);
        }
    }