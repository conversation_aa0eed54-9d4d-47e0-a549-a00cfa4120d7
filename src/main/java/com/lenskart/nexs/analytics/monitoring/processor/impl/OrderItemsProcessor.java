package com.lenskart.nexs.analytics.monitoring.processor.impl;

import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.dto.OrderItemsDto;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderItemsProcessor extends MonitorPanelAbstractProcessor {

    @Override
    protected RequestPayload doValidate(RequestPayload requestPayload) throws Exception {
        log.debug("[OrderItemEvent] : doValidate,  payload received is : {}", requestPayload.getOrderItemAfterData().toString());
        if(ObjectUtils.isNotEmpty(requestPayload.getOrderItemAfterData())){
            return requestPayload;
        }else {
            log.error("[OrderItemsProcessor] : Invalid order item status details");
            throw new Exception(" Exception caught while validating order item event message");
        }

    }

    @Override
    protected RequestPayload prepareEventMessage(DebeziumMessage debeziumMessage) throws Exception {
        log.debug("[OrderItemEvent] : debizium message received is : {}", debeziumMessage );
        RequestPayload requestPayload = new RequestPayload();
        try{
            if (null != debeziumMessage && Constants.ORDER_ITEMS_TABLE.equalsIgnoreCase(debeziumMessage.getPayload().getSource().getTable())
                    && debeziumMessage.getPayload().getBefore() != debeziumMessage.getPayload().getAfter()) {
                String text = super.gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                OrderItemsDto orderItemsDto = ObjectHelper.getObjectMapper().readValue(text,OrderItemsDto.class);
                requestPayload.setOrderItemId(orderItemsDto.getOrderItemId());
                QueryType queryType = super.getQueryType(orderItemsDto.getStatus());
                requestPayload.setQueryType(queryType);
                requestPayload.setStatus(orderItemsDto.getStatus());
                if(orderItemsDto.getShippingPackageId() != null){
                    requestPayload.setShippingPackageId(orderItemsDto.getShippingPackageId());
                }
                requestPayload.setWmsOrderCode(orderItemsDto.getWmsOrderCode());
                requestPayload.setOrderItems(orderItemsDto);
                requestPayload.setOrderItemAfterData(orderItemsDto);
            }
        }catch (Exception e){
            log.error("Exception caught while creating  order item event with error : "+ e);
            throw new Exception("Event message conversion to  order item failed with exception : "+ e);
        }
        log.debug("OrderItemEvent.payload generated is : {}", requestPayload.getOrderItemId());
        return requestPayload;
    }

    @Override
    protected void executePopulateMonitorPanelData(RequestPayload requestPayload) throws Exception {
        log.debug("[OrderItemEvent] : executePopulateMonitorPanelData,  calling db strategy for request : {}", requestPayload.getOrderItemAfterData().toString());
        super.executor.doExecute(requestPayload,requestPayload.getQueryType());
    }
}


