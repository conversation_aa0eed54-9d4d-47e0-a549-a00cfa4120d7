package com.lenskart.nexs.analytics.monitoring.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.analytics.monitoring.dto.OrderItemsDto;
import com.lenskart.nexs.analytics.monitoring.dto.PickingDetailDto;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelProcessorFactory;
import com.lenskart.nexs.analytics.monitoring.processor.impl.OrderItemsOrderActivityProcessor;
import com.lenskart.nexs.analytics.monitoring.strategy.Impl.DeleteStrategyMonitorPanel;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.regex.Pattern;


@Slf4j
@Service
public class OrderActivityEventConsumer {

    @Value("${nexs.order.activity.enabled}")
    protected boolean isOrderActivityEnabled;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    MonitorPanelProcessorFactory monitorPanelProcessorFactory;
    @Autowired
    DeleteStrategyMonitorPanel deleteStrategyMonitorPanel;
    @Value("${nexs.kafka.order-items.topic.temp.enabled}")
    private Boolean isProducerEnabled;

    @Autowired
    Gson gson;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    @Autowired
    private OrderItemsOrderActivityProcessor orderActivityProcessor;

    @Value("${nexs.monitor.panel.channel.allowed.regex:^BULKTOVENDOR$}")
    private String allowedChannelRegex;

    private Pattern allowedChannelPattern;

    @PostConstruct
    private void init(){
        allowedChannelPattern = Pattern.compile(allowedChannelRegex, Pattern.CASE_INSENSITIVE);
    }

    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${nexs.kafka.order-items.temp.topic}", groupId = "${kafka.topic.name.order-activity-group-id}")
    public void orderItemsOrderActivityListner(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) throws Exception {

        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            if (isOrderActivityEnabled) {
                log.info("[OrderActivityConsumer][orderItemActivity] The messageConsumed is {} and header {}",
                        message, messageHeader);
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                orderActivityProcessor.doExecute(debeziumMessage);
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("[OrderActivityConsumer][orderItemActivity] : time taken in processing order activity data ms {} and minutes {}", timeTaken,
                        timeTaken / (60 * 1000));
            }
        } catch (Exception e) {
            log.error("[OrderActivityConsumer][orderItemActivity] orderItemsOrderActivityListner exception caught  : {}", e.getMessage(), e);
        } finally {
            log.info("[listenOrderItems][orderItemActivity] : processed message: {}", message);
            ack.acknowledge();
        }
    }

    @Trace(dispatcher = true)
    @KafkaListener(topicPattern = "${kafka.topic.name.picking-detail}", groupId = "${kafka.topic.name.picking-detail-group-id}")
    public void consumerPickingDetail(@Payload String message, @Headers MessageHeaders messageHeader, Acknowledgment ack) {

        long startTime = System.currentTimeMillis();
        long timeTaken;
        try {
            if (isOrderActivityEnabled) {
                log.info("[OrderActivityConsumer][producerPickingDetail]  The messageConsumed is {} and header {}",
                        message, messageHeader);
                DebeziumMessage debeziumMessage = objectMapper.readValue(message, DebeziumMessage.class);
                String afterData = gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
                PickingDetailDto afterPickingDetail = ObjectHelper.getObjectMapper().readValue(afterData,PickingDetailDto.class);
                if(isAllowedChannels(afterPickingDetail.getChannel())) {
                    MonitorPanelAbstractProcessor monitorPanelAbstractProcessor = monitorPanelProcessorFactory.getEventProcessor(debeziumMessage);
                    monitorPanelAbstractProcessor.doExecute(debeziumMessage);
                }
            }
            timeTaken = System.currentTimeMillis() - startTime;
            log.info("[OrderActivityConsumer][producerPickingDetail]  : time taken in processing data from wms and picking ms {} and minutes {}", timeTaken,
                    timeTaken / (60 * 1000));
            ack.acknowledge();
        } catch (Exception e) {
            log.error("[OrderActivityConsumer][producerPickingDetail] exception caught  : {}", e.getMessage(), e);
        } finally {
            log.info("[OrderActivityConsumer][producerPickingDetail] : processed message: {}", message);
            ack.acknowledge();
        }
    }

    @Logging
    private boolean isAllowedChannels(String channel) {
        if(Objects.nonNull(allowedChannelPattern) && Objects.nonNull(channel)){
            return !allowedChannelPattern.matcher(channel).find();
        }
        return true;
    }
}
