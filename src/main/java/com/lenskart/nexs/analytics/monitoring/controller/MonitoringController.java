package com.lenskart.nexs.analytics.monitoring.controller;

import com.lenskart.nexs.analytics.connector.PoServiceConnector;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.consumer.MonitoringPanelConsumer;
import com.lenskart.nexs.analytics.monitoring.consumer.OrderActivityEventConsumer;
import com.lenskart.nexs.analytics.monitoring.cronjobs.MonitorPanelDataCron;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelSavedView;
import com.lenskart.nexs.analytics.monitoring.request.DetailPageRequest;
import com.lenskart.nexs.analytics.monitoring.request.HomePageRequest;
import com.lenskart.nexs.analytics.monitoring.request.ProductDetailsPageRequest;
import com.lenskart.nexs.analytics.monitoring.response.DetailPageResponse;
import com.lenskart.nexs.analytics.monitoring.response.MonitorPanelSummaryResponse;
import com.lenskart.nexs.analytics.monitoring.response.ManualAsrsWaveResponse;
import com.lenskart.nexs.analytics.monitoring.response.OrderActivityEventResponse;
import com.lenskart.nexs.analytics.monitoring.response.ProductDetailsPageResponse;
import com.lenskart.nexs.analytics.monitoring.service.MonitorPanelSavedViewsService;
import com.lenskart.nexs.analytics.monitoring.service.MonitoringPanelService;
import com.lenskart.nexs.analytics.monitoring.service.OrderActivityEventService;
import com.lenskart.nexs.analytics.monitoring.strategy.Impl.DeleteStrategyMonitorPanel;
import com.lenskart.nexs.analytics.monitoring.strategy.Impl.UpdateStrategyMonitorPanel;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import com.lenskart.nexs.responseBuilder.ResponseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping(value = "/nexs/analytics/monitoring/")
@Slf4j
public class MonitoringController {

    @Autowired
    private ResponseBuilder responseBuilder;

    @Autowired
    private MonitoringPanelService monitoringService;

    @Autowired
    private MonitorPanelDataCron MonitorPanelDataCron;

    @Autowired
    private MonitorPanelSavedViewsService monitorPanelSavedViewsService;

    @Autowired
    private MonitorPanelDataCron monitorPanelDataCron;

    @Autowired
    private OrderActivityEventService orderActivityEventService;

    @Value("${nexs.monitoring.event.enabled:event}")
    private String dataSource;

    @Autowired
    private MonitoringPanelConsumer monitoringPanelConsumer;

    @Autowired
    private UpdateStrategyMonitorPanel updateMonitorPanel;

    @Autowired
    private DeleteStrategyMonitorPanel deleteStrategyMonitorPanel;

    @Autowired
    private PoServiceConnector poServiceConnector;
    @Autowired
    private OrderActivityEventConsumer orderActivityEventConsumer;


    @RequestMapping(value = Constants.HOMEPAGE_PATH, method = RequestMethod.GET)
    public ResponseEntity<BaseResponseModel> getMonitoringPanelDetails(@RequestHeader(value = "facility-code") String facilityCode) throws Exception {
        Map<String, Object> panelMap = monitoringService.generateMonitoringPanelListingPage(facilityCode);
        return responseBuilder.successResponse(panelMap, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.DETAILS_PAGE_PATH, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> fetchDetailsPage(@RequestBody DetailPageRequest detailPageRequest,
                                                              @RequestHeader(value = "facility-code") String facilityCode) throws Exception {
        DetailPageResponse detailPageResponse = monitoringService.generateDetailsPage(detailPageRequest, facilityCode);
        return responseBuilder.successResponse(detailPageResponse, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.HOMEPAGE_PATH_V2, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> getMonitoringPanelDetailsV2(@RequestParam(value = "version") String version, @RequestBody HomePageRequest homePageRequest,
                                                                         @RequestHeader(value = "facility-code") String facilityCode) throws Exception {
        Map<String, Object> panelMap;
        if (version.equals(Constants.V2)) {
            panelMap = monitoringService.generateMonitoringPanelListingPage(homePageRequest, facilityCode);
        } else {
            panelMap = monitoringService.generateMonitoringPanelListingPage(facilityCode);
        }
        return responseBuilder.successResponse(panelMap, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.DETAILS_PAGE_PATH_V2, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> fetchDetailsPageV2(@RequestParam(value = "version") String version, @RequestBody DetailPageRequest detailPageRequest,
                                                                @RequestHeader(value = "facility-code") String facilityCode) throws Exception {
        DetailPageResponse detailPageResponse;
        if (version.equals(Constants.V2)) {
            detailPageResponse = monitoringService.generateDetailsPageV2(detailPageRequest, facilityCode);
        } else {
            detailPageResponse = monitoringService.generateDetailsPage(detailPageRequest, facilityCode);
        }
        return responseBuilder.successResponse(detailPageResponse, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.HOMEPAGE_PATH_V3, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> getMonitoringPanelDetailsV3(@RequestParam(value = "version") String version, @RequestBody HomePageRequest homePageRequest,
                                                                         @RequestHeader(value = "facility-code") String facilityCode,
                                                                         @RequestParam(value = "source", required = false) String source) throws Exception {
        Map<String, Object> panelMap;
        if (version.equals(Constants.V3)) {
            if (ObjectUtils.isEmpty(source)) {
                source = dataSource;
            }
            panelMap = monitoringService.generateMonitoringPanelListingPageV3(homePageRequest, facilityCode, source);
        } else {
            panelMap = monitoringService.generateMonitoringPanelListingPage(facilityCode);
        }
        return responseBuilder.successResponse(panelMap, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.DETAILS_PAGE_PATH_V3, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> fetchDetailsPageV3(@RequestParam(value = "version") String version, @RequestBody DetailPageRequest detailPageRequest,
                                                                @RequestHeader(value = "facility-code") String facilityCode,
                                                                @RequestParam(value = "source", required = false) String source) throws Exception {
        DetailPageResponse detailPageResponse;
        if (version.equals(Constants.V3)) {
            if (ObjectUtils.isEmpty(source)) {
                source = dataSource;
            }
            detailPageResponse = monitoringService.generateDetailsPageV3(detailPageRequest, facilityCode, source);
        } else {
            detailPageResponse = monitoringService.generateDetailsPage(detailPageRequest, facilityCode);
        }
        return responseBuilder.successResponse(detailPageResponse, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

//    @RequestMapping(value = "scheduler", method = RequestMethod.GET)
//    public void callScheduler() throws Exception {
//        MonitorPanelDataCron.populateMonitorPanelData1();
//    }

    @RequestMapping(value = Constants.SAVE_VIEWS, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> saveViews(@RequestBody List<MonitorPanelSavedView> monitorPanelSavedViews) throws Exception {
        String empCode = MDC.get("USER_ID");
        if (StringUtils.isBlank(empCode)) {
            log.error("[saveViews] User is null or empty");
            throw new Exception("User is null or empty");
        }

        try {
            monitorPanelSavedViewsService.saveViews(monitorPanelSavedViews, empCode);
            return responseBuilder.successResponse("Views saved successfully", "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
        } catch (Exception e) {
            log.error("Error occurred while saving views: {} - {}", e.getMessage(), monitorPanelSavedViews);
            throw new Exception("Error occurred while saving views: " + e.getMessage(), e);
        }

    }


    @RequestMapping(value = Constants.FETCH_VIEWS, method = RequestMethod.GET)
    public ResponseEntity<BaseResponseModel> fetchViews() throws Exception {

        String empCode = MDC.get("USER_ID");
        if (StringUtils.isBlank(empCode)) {
            log.error("[fetchViews] User is null or empty");
            throw new Exception("User is null or empty");
        }

        try {
            return responseBuilder.successResponse(monitorPanelSavedViewsService.fetchViews(empCode)
                    , "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
        } catch (Exception e) {
            log.error("Error occurred while fetching views for empCode: {} - {}", empCode, e.getMessage());
            throw new Exception("Error occurred while fetching views for empCode: " + empCode + " : " + e.getMessage(), e);
        }

    }

    @RequestMapping(value = Constants.JIT_HOME_PAGE, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> getJitMonitorPanelHomePageDetails(@RequestBody HomePageRequest homePageRequest,
                                                                               @RequestHeader(value = "facility-code") String facilityCode,
                                                                               @RequestParam(value = "source", required = false) String source) throws Exception {
        Map<String, Object> panelMap;
        try {
            if (ObjectUtils.isEmpty(source)) {
                source = dataSource;
            }
            panelMap = monitoringService.generateJitMonitorPanelHomePageRequest(homePageRequest, facilityCode, source);
        } catch (Exception e) {
            log.error("Exception caught while generating jit home page request");
            throw new Exception("Exception caught while generating jit home page request with message : {}", e);
        }
        return responseBuilder.successResponse(panelMap, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }


    @RequestMapping(value = Constants.JIT_DETAILS_PAGE, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> fetchJitDetailsPage(@RequestBody DetailPageRequest detailPageRequest,
                                                                 @RequestHeader(value = "facility-code") String facilityCode,
                                                                 @RequestParam(value = "source", required = false) String source) throws Exception {
        DetailPageResponse detailPageResponse;
        try {
            if (ObjectUtils.isEmpty(source)) {
                source = dataSource;
            }
            detailPageResponse = monitoringService.generateJitDetailsPage(detailPageRequest, facilityCode, source);
        } catch (Exception e) {
            log.error("Exception caught while generating jit details page request");
            throw new Exception("Exception caught while generating jit details page request with message : {}", e);
        }

        return responseBuilder.successResponse(detailPageResponse, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }


    @RequestMapping(value = Constants.PID_DETAILS_PAGE, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> fetchPidDetailsPage(@RequestBody ProductDetailsPageRequest productDetailsPageRequest,
                                                                 @RequestParam(value = "source", required = false) String source) throws Exception {
        if (ObjectUtils.isEmpty(source)) {
            source = dataSource;
        }
        List<ProductDetailsPageResponse> productDetailsPageResponseList = monitoringService.generatePidDetailsPage(productDetailsPageRequest, source);
        return responseBuilder.successResponse(productDetailsPageResponseList, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.MONOTOR_PANEL_CRON_TRIGGER, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> updateMonitorPanelData() throws Exception {
        monitorPanelDataCron.populateMonitorPanelData1();
        return responseBuilder.successResponse(null, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.UPDATE_GROUP_STATUS, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> updateGroupStatus(@RequestBody List<String> shipmentIds) throws Exception {
        updateMonitorPanel.validateAndUpdateGroupEffectiveStatus(shipmentIds);
        return responseBuilder.successResponse(null, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.CLEAN_DATA, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> cleanTerimalData(@RequestBody List<String> shipmentIds) throws Exception {
        deleteStrategyMonitorPanel.deleteStaleData(shipmentIds);
        return responseBuilder.successResponse(null, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

//    @RequestMapping(value = "/consumer", method = RequestMethod.POST)
//    public void testOrderActivity(@RequestBody Object object) throws Exception {
//        orderActivityEventConsumer.orderItemsOrderActivityListner(object, new MessageHeaders(new HashMap<>()), new Acknowledgment() {
//            @Override
//            public void acknowledge() {
//                System.out.println("ack");
//            }
//        });
//    }

    @RequestMapping(value = Constants.FETCH_ORDER_ACTIVITY, method = RequestMethod.GET)
    public ResponseEntity<BaseResponseModel> fetchOrderActivity(@RequestParam(value = "entity") String entity) throws Exception {
        OrderActivityEventResponse orderActivityEventResponse = orderActivityEventService.getOrderActivityResponse(entity);
        return responseBuilder.successResponse(orderActivityEventResponse, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.SUMMARY_HOME_PAGE, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> getMonitorPanelSummaryPage(@RequestBody HomePageRequest homePageRequest,
                                                                         @RequestHeader(value = "facility-code") String facilityCode,
                                                                         @RequestParam(value = "source", required = false) String source) throws Exception {


        MonitorPanelSummaryResponse summaryResponse = monitoringService.generateMonitorPanelSummaryResponse(homePageRequest, facilityCode);
        return responseBuilder.successResponse(summaryResponse, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.FETCH_MANUAL_ASRS_WAVE_COUNT, method = RequestMethod.GET)
    public ResponseEntity<BaseResponseModel> fetchCountForAsrs(@RequestParam(value = "facility", required = true)String facility,
                                                               @RequestParam(value = "createdAtStart", required = true)String createdAtStart,
                                                               @RequestParam(value = "createdAtEnd", required = true)String createdAtEnd) throws Exception {
        List<ManualAsrsWaveResponse> manualAsrsWaveResponseList = new ArrayList<>();
        manualAsrsWaveResponseList = monitoringService.fetchCountDetails(facility,createdAtStart,createdAtEnd);
        return responseBuilder.successResponse(manualAsrsWaveResponseList,Constants.SUCCESS,ResponseCodes.RESPONSE_SUCCESS);
    }
}
