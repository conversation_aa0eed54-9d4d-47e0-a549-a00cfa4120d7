package com.lenskart.nexs.analytics.monitoring.mapper;


import com.lenskart.nexs.analytics.monitoring.dto.JitOrderStatusDetailsDto;
import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData1;
import com.lenskart.nexs.analytics.monitoring.pojo.DetailPageDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MonitorPanelMapper {

    MonitorPanelMapper PANEL_MAPPER = Mappers.getMapper(MonitorPanelMapper.class);

    JitOrderStatusDetails jitOrderStatusDetailsDtoMapper(JitOrderStatusDetailsDto jitOrderStatusDetailsDto);
}