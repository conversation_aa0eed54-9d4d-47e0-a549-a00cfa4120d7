package com.lenskart.nexs.analytics.monitoring.entities.wms;


import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = SqlTableConstants.ORDER_META_DATA)
public class OrderMetaData extends BaseEntity {

    @Column(name = "pair_key")
    private String pairKey;

    private String value;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "nexs_order_id", referencedColumnName = "id")
    private Orders nexs_order_id;
}
