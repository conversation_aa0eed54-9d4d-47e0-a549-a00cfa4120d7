package com.lenskart.nexs.analytics.monitoring.repository.jitdb;

import com.lenskart.nexs.analytics.monitoring.entities.jit.JitOrderStatusDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface JitDbRepository extends JpaRepository<JitOrderStatusDetails, Long> {
    public List<JitOrderStatusDetails> findBySourceReferenceIdInAndOrderTypeEquals(List<String> sourceReferenceIds,String orderType);
}