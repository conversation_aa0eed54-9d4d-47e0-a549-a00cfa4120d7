package com.lenskart.nexs.analytics.monitoring.utils;

import com.lenskart.nexs.analytics.monitoring.Constants;

import javax.persistence.Tuple;
import java.util.*;

public class ListingPageUtils {


    public static String getFulfillabilitytag(String frTag,int fullfillableFlag) {

        String fulfillabilitytag= Constants.FulfillableType.UNFULFILLABLE_FRAME.name();
        if(fullfillableFlag ==0)
            fulfillabilitytag=Constants.FulfillableType.FULFILLABLE.name();
        else {
            if(frTag.toLowerCase().contains("lens")) {

                fulfillabilitytag=Constants.FulfillableType.UNFULFILLABLE_LENS.name();
            }

        }
        return fulfillabilitytag;

    }

    public static String getFulfillabilitytagV3(String frTag,int fullfillableFlag) {

        String fulfillabilitytag= Constants.FulfillableTypeV3.UNFULFILLABLE_ORDERS.name();
        if(fullfillableFlag ==0)
            fulfillabilitytag=Constants.FulfillableTypeV3.FULFILLABLE_ORDERS.name();
        else {
                fulfillabilitytag=Constants.FulfillableTypeV3.UNFULFILLABLE_ORDERS.name();

        }
        return fulfillabilitytag;

    }


    public static  String getEffectiveStatus(String tag,String status){
        Map<String,String> searchMap=Constants.fr1_fr2StatusMap;
        if(tag.toLowerCase().contains(Constants.FR0_TAG_PREFIX)){

            searchMap=Constants.fr0StatusMap;

            //The below code will go away and not usable in production


        }
        return searchMap.containsKey(status)?searchMap.get(status):null;

    }

}
