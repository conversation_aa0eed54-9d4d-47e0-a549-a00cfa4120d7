package com.lenskart.nexs.analytics.monitoring.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelSavedView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FetchViewsResponse {

    @NonNull
    private String defaultFilterName;

    @NonNull
    private List<MonitorPanelSavedView> monitorPanelSavedViews;

}
