package com.lenskart.nexs.analytics.monitoring.processor.impl;

import com.lenskart.nexs.activity.model.DebeziumMessage;
import com.lenskart.nexs.activity.repositories.OrderItemActivityDataRepository;
import com.lenskart.nexs.analytics.monitoring.dto.OrderItemsDto;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;

import com.lenskart.nexs.analytics.monitoring.processor.MonitorPanelAbstractProcessor;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
@Slf4j
public class OrderItemsOrderActivityProcessor extends MonitorPanelAbstractProcessor {

    @Autowired
    private OrderItemActivityDataRepository orderItemActivityDataRepository;

    @Override
    protected RequestPayload doValidate(RequestPayload requestPayload) throws Exception {
        log.debug("[OrderItemsOrderActivityProcessor] : doValidate,  payload received is : {}", requestPayload.getOrderItemAfterData().toString());
        if (ObjectUtils.isNotEmpty(requestPayload.getOrderItemAfterData())) {
            return requestPayload;
        } else {
            log.error("[OrderItemsOrderActivityProcessor] : Invalid order item status details");
            throw new Exception(" Exception caught while validating order item event message");
        }
    }

    /**
     * preparing request payload for processing order activity event data
     *
     * @param debeziumMessage
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(value = "monitorPanelDataTransactionManager", rollbackFor = Exception.class)
    protected RequestPayload prepareEventMessage(DebeziumMessage debeziumMessage) throws Exception {
        RequestPayload requestPayload = new RequestPayload();
        String afterData = super.gson.toJson(debeziumMessage.getPayload().getAfter()).toString();
        OrderItemsDto afterItemDto = ObjectHelper.getObjectMapper().readValue(afterData, OrderItemsDto.class);
        String beforeData = super.gson.toJson(debeziumMessage.getPayload().getBefore()).toString();
        OrderItemsDto beforeItemDto = ObjectHelper.getObjectMapper().readValue(beforeData, OrderItemsDto.class);
        requestPayload.setWmsOrderCode(afterItemDto.getWmsOrderCode());
        requestPayload.setShippingPackageId(afterItemDto.getShippingPackageId());
        requestPayload.setOrderItemId(afterItemDto.getOrderItemId());
        requestPayload.setOrderItemAfterData(afterItemDto);
        requestPayload.setOrderItemBeforeData(beforeItemDto);
        requestPayload.setStatus(afterItemDto.getStatus());
        requestPayload.setQueryType(QueryType.INSERT_ORDER_ACTIVITY);
        return requestPayload;
    }

    @Override
    protected void executePopulateMonitorPanelData(RequestPayload requestPayload) throws Exception {
        log.debug("[OrderItemsOrderActivityProcessor] : executePopulateMonitorPanelData,  calling db strategy for request : {}", requestPayload.getOrderItemAfterData().toString());
        super.executor.doExecute(requestPayload, requestPayload.getQueryType());
    }



}
