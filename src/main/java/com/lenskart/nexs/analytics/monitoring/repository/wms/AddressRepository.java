package com.lenskart.nexs.analytics.monitoring.repository.wms;


import com.lenskart.nexs.analytics.monitoring.entities.wms.Address;
import com.lenskart.nexs.common.base.EntitiesRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AddressRepository extends EntitiesRepository<Address, Integer> {

    @Query(value = "select * from order_address where order_item_header_id = ?1 and address_type = ?2",nativeQuery = true)
    Address findByOrderItemHeaderIdAndAddressType(long orderItemHeaderId, int addressType);

}
