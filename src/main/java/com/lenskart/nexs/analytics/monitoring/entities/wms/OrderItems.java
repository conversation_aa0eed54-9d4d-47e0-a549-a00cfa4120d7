package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.wms.constants.ErrorType;
import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;
import com.lenskart.nexs.wms.enums.FulfillableType;
import lombok.*;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = SqlTableConstants.ORDER_ITEMS)
public class OrderItems extends BaseEntity {


    @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "nexs_order_id", referencedColumnName = "id")
    private Orders nexsOrderId;


    @Column(name ="order_item_id")
    private Integer orderItemId;

    @Column(name ="item_id")
    private Integer itemId;

    @Column(name ="product_id")
    private Integer product_id;


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name ="fulfilled_at" , columnDefinition = "timestamp")
    private Date fulfilledAt;


    @Column(name ="barcode" )
    private String barcode;


    @Column(name ="holded" )
    private Integer holded;

    @Column(name ="status" )
    private String status;

    @NotNull
    @Column(name ="wms_order_code", columnDefinition = "varchar(20)")
    private String wmsOrderCode;


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name ="expected_dispatch_date")
    private Date expectedDispatchDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name ="expected_delivery_date")
    private Date expectedDeliveryDate;

    @Column(name ="shipping_package_id", columnDefinition = "varchar(30)")
    private String shippingPackageId;

    @Column(name ="facility_code")
    private String facilityCode;

    @NotNull
    @Column(name ="channel")
    private String channel;

    @NotNull
    @Column(name ="payment_method")
    private String paymentMethod;

    @NotNull
    @Column(name ="nav_channel")
    private String navChannel;


//    @OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(name = "shipping_package_id", referencedColumnName = "shipping_package_id")
//    private ShipmentDetails shipmentDetails;


    @Column(name ="fulfilled_type")
    private FulfillableType fulfillableType;


    @Column(name ="item_amount")
    private BigDecimal itemAmount;

    @ManyToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "order_item_header_id", referencedColumnName = "id")
    private OrderItemHeader order_item_header_id;


    @LazyCollection(LazyCollectionOption.FALSE)
    @OneToMany(mappedBy = "order_item_id",cascade = {CascadeType.PERSIST})
    private List<OrderItemMetaData> orderItemMetaDataList= new ArrayList<OrderItemMetaData>();

    @NotNull
    @Column(name ="product_delivery_type")
    private String productDeliveryType;

    @Column(name ="fitting_id")
    private Integer fittingId;

    @Column(name ="location_id")
    private String locationId;

    @Column(name ="location_type")
    private String locationType;

    @Column(name ="fitting_type")
    private String fittingType;

    @Column(name ="hold")
    private Integer hold;

    @Column(name ="hold_reason")
    private String holdReason;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "power_id")
    private OrderItemPower power;

    @Column(name ="processing_type")
    private String processingType;

    @Column(name ="item_type")
    private String itemType;

    @Column(
            name = "comments"
    )
    private String comments;

    @Version
    private Integer version;

    @Column(name = "qc_fail_count")
    private int qcFailCount;

    //ToDo: Add error Type
    @Column(name="error_type")
    @Enumerated(EnumType.STRING)
    private ErrorType errorType;
}
