package com.lenskart.nexs.analytics.monitoring.cronjobs;

import com.lenskart.nexs.analytics.dao.CacheDAO;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelNew;
import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItems;
import com.lenskart.nexs.analytics.monitoring.entities.wms.PacketHandoverDetails;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelPickingData;
import com.lenskart.nexs.analytics.monitoring.model.OrderItemData;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelData1Repository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelData2Repository;
import com.lenskart.nexs.analytics.monitoring.repository.monitorpanel.MonitorPanelNewRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.OrderItemRepository;
import com.lenskart.nexs.analytics.monitoring.repository.wms.PacketHandoverDetailsRepository;
import com.lenskart.nexs.analytics.monitoring.service.MonitorPanelDataService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MonitorPanelDataCron {


    @Autowired
    MonitorPanelData1Repository monitorPanelData1Repository;

    @Autowired
    MonitorPanelData2Repository monitorPanelData2Repository;

    @Autowired
    MonitorPanelDataService monitorPanelDataService;

    @Autowired
    OrderItemRepository orderItemRepository;

    @Autowired
    private CacheDAO cacheDAO;

    @Autowired
    MonitorPanelNewRepository monitorPanelNewRepository;

    @Autowired
    MonitorPanelNewRepository monitorPanelNewReadRepository;
    @Autowired
    PacketHandoverDetailsRepository packetHandoverDetailsRepository;


    @Value("${nexs.monitor.data.page.siz:1500}")
    private int pageSize;

    @Value("${nexs.courier.update.time}")
    private int lastUpateTime;

    @Value("${nexs.courier.update.limit}")
    private int updateLimit;

    @Value("${nexs.courier.update.scheduler.ttl:30}")
    private int schedulerTTL;

    @Value("${nexs.courier.update.scheduler.enabled:false}")
    private boolean isCourierUpdateSchedulerEnabled;


    @Scheduled(cron = "${monitoring.scheduler.cron}")
    @SchedulerLock(name = "populateMonitorPanelData",lockAtLeastForString = "${monitoring.scheduler.least.lock}", lockAtMostForString = "${monitoring.scheduler.most.lock}")
    public void populateMonitorPanelData1() throws Exception {
        log.info("populateMonitorPanelData schedular started : " + new Date());
        long startTime= System.currentTimeMillis();

        try {
            if(cacheDAO.hasKey(Constants.SCHEDULER_KEY)){
                log.error("[populateMonitorPanelData] Scheduler is being executed before 3 minutes, exiting scheduler");
                return ;
            }
            cacheDAO.putVal(Constants.SCHEDULER_KEY, Constants.SCHEDULER_START_VALUE, 30L, TimeUnit.MINUTES);
            String redisKey = (String) cacheDAO.getVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY);
            List<OrderItems> orderItemsList;
            int pageNumber = 0;
            long timeTaken;
            log.info("fetcing max id");
            long maxId=orderItemRepository.getMaxId();
            do {
                Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by("id").ascending());
                Map<Integer, MonitorPanelPickingData> orderItemIdToPickingStatusMap = new HashMap<>();
                Map<Integer,Integer> pickingPriorityMap = new HashMap<>();
                Map<String, OrderItemData> groupStatusMap = new HashMap<>();
                List<Integer>jitOrderItemIds = new ArrayList<>();
                orderItemsList = new ArrayList<>();
                monitorPanelDataService.getOrderItemsAndCorrespondingPickingStatus(orderItemsList, orderItemIdToPickingStatusMap, jitOrderItemIds, pickingPriorityMap, pageable, maxId);
                timeTaken = System.currentTimeMillis() - startTime;
                log.info("time taken in reading data from wms and picking ms {} and minutes {} | size: {}", timeTaken,
                        timeTaken / (60 * 1000),orderItemsList.size());
                monitorPanelDataService.populateMonitorPanelData(redisKey, orderItemsList, orderItemIdToPickingStatusMap, groupStatusMap, jitOrderItemIds, pickingPriorityMap);
                pageNumber++;
            } while (orderItemsList.size() == pageSize);

            log.info("[populateMonitorPanelData] total pageNumber: {} | pageSize: {} | {}",pageNumber,pageSize,
                    (pageNumber * pageSize));
            if(ObjectUtils.isEmpty(redisKey)){
                long count = monitorPanelData1Repository.count();
                if(count==0){
                    cacheDAO.putVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY, Constants.MONITOR_TABLE_2 + Constants.DELIMITER + System.currentTimeMillis());
                }else {
                    cacheDAO.putVal(Constants.MONITOR_TABLE_UPDATED_AT_KEY, Constants.MONITOR_TABLE_1 + Constants.DELIMITER + System.currentTimeMillis());
                }
            }
            String[] tableNameAndUpdatedAtList = redisKey.split(Constants.DELIMITER);

            monitorPanelDataService.deleteTableAndUpdateKey(tableNameAndUpdatedAtList);


            timeTaken = System.currentTimeMillis() - startTime;

            log.info("populateMonitorPanelData schedular ended  and time taken in ms {} and minutes {} " ,timeTaken, timeTaken/(60*1000));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error occurred while populating monitor panel data in DB - message : {}", e.getMessage(),e);
        }finally {
            cacheDAO.delVal(Constants.SCHEDULER_KEY);
        }
    }

    /**
     *  updating soft courier details
     * @throws Exception
     */
    @Scheduled(cron = "${monitoring.courier.scheduler.cron}")
    @SchedulerLock(name = "courierDetailUpdate",lockAtLeastForString = "${monitoring.courier.scheduler.least.lock}", lockAtMostForString = "${monitoring.courier.scheduler.most.lock}")
    public void updateSoftCourier() throws Exception {

        if(isCourierUpdateSchedulerEnabled){

            log.info("[updateSoftCourier] Scheduler is being executed at time : " + new DateTime());

            try{
                if(cacheDAO.hasKey(Constants.COURIER_UPDATE_SCHEDULER_KEY)){
                    log.error("[updateSoftCourier] Scheduler is being executed before 30 minutes, exiting scheduler");
                    return ;
                }
                cacheDAO.putVal(Constants.COURIER_UPDATE_SCHEDULER_KEY, Constants.SCHEDULER_START_VALUE, (long) schedulerTTL, TimeUnit.MINUTES);
                // reading from slave
                List<MonitorPanelNew> softCourierUpdateEligibleShipments = monitorPanelNewReadRepository.findAllByWmsStatusAndUpdatedAt(Constants.softCourierUpdateEligibleStatus, lastUpateTime , updateLimit);

                List<String> shipmentsToUpdateCourier = softCourierUpdateEligibleShipments.stream()
                        .map(MonitorPanelNew::getShippingPackageId)
                        .distinct().collect(Collectors.toList());
                List<PacketHandoverDetails> courierDetails = packetHandoverDetailsRepository.getCourierByPacketList(shipmentsToUpdateCourier);
                Map<String, String> courierShipmentMap = courierDetails.stream().collect(Collectors.toMap(PacketHandoverDetails::getPacketId,PacketHandoverDetails::getCourierCode));

                for(MonitorPanelNew monitorPanelData : softCourierUpdateEligibleShipments){
                    monitorPanelData.setCourierCode(courierShipmentMap.get(monitorPanelData.getShippingPackageId()));
                }
                // updating to master
                monitorPanelNewRepository.saveAll(softCourierUpdateEligibleShipments);
            }catch (Exception e){
                log.error("Failed to run courier update scheduler at time : {} with exception message : {}" , new DateTime(), e.getMessage(), e);
                throw new Exception("Failed to run courier update scheduler ");
            }finally {
                cacheDAO.delVal(Constants.COURIER_UPDATE_SCHEDULER_KEY);
            }
        }
    }


}
