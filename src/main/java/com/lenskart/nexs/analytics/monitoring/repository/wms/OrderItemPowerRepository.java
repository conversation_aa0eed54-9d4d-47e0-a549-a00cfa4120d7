package com.lenskart.nexs.analytics.monitoring.repository.wms;


import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemPower;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface OrderItemPowerRepository extends JpaRepository<OrderItemPower,Integer> {

    OrderItemPower findByOrderIdAndProductId(Integer orderId, Integer productId);
}
