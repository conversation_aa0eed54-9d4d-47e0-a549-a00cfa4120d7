package com.lenskart.nexs.analytics.monitoring.strategy.Impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.nexs.activity.entities.OrderActivityEvent;
import com.lenskart.nexs.activity.entities.OrderItemActivityData;
import com.lenskart.nexs.activity.model.Action;
import com.lenskart.nexs.activity.model.Events;
import com.lenskart.nexs.activity.model.OrderEventData;
import com.lenskart.nexs.activity.model.Transitions;
import com.lenskart.nexs.activity.repositories.OrderActivityEventRepository;
import com.lenskart.nexs.activity.repositories.OrderItemActivityDataRepository;
import com.lenskart.nexs.analytics.activity.repository.picking.PickingDetailRepository;
import com.lenskart.nexs.analytics.entities.picking.PickingDetail;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.entities.mongodb.ShipmentDetails;
import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.model.OrderItemData;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.monitoring.strategy.MonitorPanelBaseStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OrderActivityEventStrategy extends MonitorPanelBaseStrategy {
    @Autowired
    private PickingDetailRepository pickingDetailRepository;

    @Autowired
    private OrderActivityEventRepository orderActivityEventRepository;
    @Autowired
    private OrderItemActivityDataRepository orderItemActivityDataRepository;
    @Autowired
    @Qualifier("orderActivityEvent")
    private Events events;

    @Override
    public QueryType supportedStates() {
        return QueryType.INSERT_ORDER_ACTIVITY;
    }

    @Transactional(value = "orderActivityTransactionManager", rollbackFor = Exception.class)
    @Override
    protected void execute(RequestPayload requestPayload) throws Exception {
        log.info("[OrderActivityStrategy] : request received for processing order activity for event payload : {}", requestPayload);

        if (ObjectUtils.isNotEmpty(requestPayload.getOrderItemAfterData()) && ObjectUtils.isNotEmpty(requestPayload.getOrderItemAfterData().getOrderItemId())) {
            log.info("[OrderActivityStrategy] : processing order activity for event payload : {}", requestPayload.toString());
            this.processOrderActivityEvent(requestPayload);
        } else if (ObjectUtils.isNotEmpty(requestPayload.getPickingDetailData())) {
            log.info("[OrderActivityStrategy] : processing picking summary status for event payload : {} ", requestPayload);
            Map<String, Object> metaDataMap = new HashMap<>();
            this.createOrderItemDtoMap(requestPayload.getPickingDetailData(), metaDataMap);
            this.prepareAndUpdatePickingSummaryEvent(requestPayload, metaDataMap);
        }


    }


    /**
     * process order activity event data , update the event string and persist the event message
     *
     * @param requestPayload
     */

    public void processOrderActivityEvent(RequestPayload requestPayload) throws Exception {
        try{
            log.info("[processOrderActivityEvent] : processing order activity event with payload :  {} ",requestPayload.toString());
            OrderEventData orderEventData = this.fetchOrderActivityEvent(requestPayload);
            Map<String, Object> metaDataMap = new HashMap<>();
            this.createOrderItemDtoMap(requestPayload.getOrderItemAfterData(), metaDataMap);

            // create a map for all the events
            if (Constants.shipmentLevelEvents.contains(orderEventData.getOrderActivityEvent())) {
                // update only when shipment level events are received
                this.updateShipmentLevelData(requestPayload, metaDataMap, orderEventData);
            }

            // below methods would perform extra checks on data to identify supporting events.
            if (ObjectUtils.isNotEmpty(requestPayload.getOrderItemBeforeData())) {
                this.checkAndUpdateFulfillabilityEvent(requestPayload, metaDataMap);
                this.checkAndUpdateFittingHoldEvents(requestPayload, metaDataMap);
                this.processEarlierTransitions(requestPayload, metaDataMap);
            }

            // below methods would process the events based on only order item transitions
            this.prepareAndPersistOrderItemActivityData(requestPayload, orderEventData);
            this.createAndPersistOrderActivityData(requestPayload, orderEventData, metaDataMap);
            this.prepareNonTransitionalEvents(requestPayload, metaDataMap);
        }catch (Exception e){
            log.error(" exception caught while processing order activity event with payload :  "+ requestPayload.toString() + " caught exception  :  "+ e);
            throw new Exception(" Failed to process order activity event ");
        }
        log.info("processing order activity event for item : {} of shipment : {}", requestPayload.getOrderItemId(), requestPayload.getShippingPackageId());

    }

    /**
     * create and persist order activity data
     *
     * @param requestPayload
     * @param
     */
    public void createAndPersistOrderActivityData(RequestPayload requestPayload, OrderEventData orderEventData, Map<String, Object> metaDataMap) throws Exception {

        try{
            log.info(" [OrderActivityEventStrategy] [createAndPersistOrderActivityData] : processing order activity event for payload : {}  , with event data : {} ", requestPayload, orderEventData);

            OrderActivityEvent orderActivityEvent = null;

            if(!Constants.shipmentLevelEvents.contains(orderEventData.getOrderActivityEvent())){
             orderActivityEvent = new OrderActivityEvent();
            }else {
                orderActivityEvent = orderActivityEventRepository.findByReferenceIdAndEvent(requestPayload.getWmsOrderCode(), orderEventData.getOrderActivityEvent());
//                boolean toBeProcessed = this.eventToBeProcessed(orderActivityEvent);
                if(ObjectUtils.isEmpty(orderActivityEvent)){
                    orderActivityEvent = new OrderActivityEvent();
                }else {
                    return;
                }
            }

            orderActivityEvent.setReferenceId(requestPayload.getWmsOrderCode());
            orderActivityEvent.setEvent(orderEventData.getOrderActivityEvent());
            orderActivityEvent.setFacilityCode(requestPayload.getOrderItemAfterData().getFacilityCode());
            orderActivityEvent.setIdempotancyKey(this.generateIdempotencyKey(requestPayload,orderEventData.getOrderActivityEvent()));

            String eventString = orderEventData.getEventMessage();
            for (Map.Entry<String, Object> entry : metaDataMap.entrySet()) {
                if (eventString.contains(entry.getKey())) {
                    eventString = eventString.replace(entry.getKey(), entry.getValue().toString());
                }
            }
            eventString = eventString.replace("[", "").replace("]", "").replace("{", "").replace("}", "");
            orderActivityEvent.setMessage(eventString);
            orderActivityEvent.setUpdatedAt(new Date());
            orderActivityEvent.setCreatedBy(requestPayload.getOrderItemAfterData().getCreated_by());
            orderActivityEvent.setUpdatedBy(requestPayload.getOrderItemAfterData().getUpdated_by());
            orderActivityEvent.setItemEventUpdatedAt(new Date(Long.parseLong(requestPayload.getOrderItemAfterData().getUpdated_at())));

            log.info(" [createAndPersistOrderActivityData] : updated order activity event data is : {} ", orderActivityEvent);
            orderActivityEventRepository.save(orderActivityEvent);
        }catch (Exception e){
            log.error(" [OrderActivityEventStrategy] [createAndPersistOrderActivityData] : failed to processes data with request payload : {} , with error message : {}" , requestPayload, e.getMessage(), e);
            throw new Exception("Order Activity Data persistence failure");
        }

    }

    /**
     * prepare and persist order activity data
     *
     * @param requestPayload
     */
    public void prepareAndPersistOrderItemActivityData(RequestPayload requestPayload, OrderEventData orderEventData) {
        try{
            log.info("[OrderActivityEventStrategy][prepareAndPersistOrderItemActivityData] : processing item level event for payload : {}" , requestPayload);

            OrderItemActivityData orderItemActivityData = orderItemActivityDataRepository.findByWmsOrderCodeAndOrderItemId(requestPayload.getOrderItemAfterData().getWmsOrderCode(), requestPayload.getOrderItemAfterData().getOrderItemId());

            log.info(" [prepareAndPersistOrderItemActivityData] : order item activity data received is : {}", orderItemActivityData);

            if (ObjectUtils.isEmpty(orderItemActivityData)) {
                orderItemActivityData = new OrderItemActivityData();
                orderItemActivityData.setWmsOrderCode(requestPayload.getOrderItemAfterData().getWmsOrderCode());
                orderItemActivityData.setOrderItemId(requestPayload.getOrderItemAfterData().getOrderItemId());
                orderItemActivityData.setCreatedAt(new Date());
            }
            orderItemActivityData.setUpdatedStatus(orderEventData.getOrderActivityEvent());
            orderItemActivityData.setCurrentStatus(requestPayload.getStatus());
            orderItemActivityData.setUpdatedAt(new Date());
            Instant instant = Instant.ofEpochMilli(Long.valueOf(requestPayload.getOrderItemAfterData().getUpdated_at()));
            orderItemActivityData.setItemEventUpdatedAt(Date.from(instant));
            orderItemActivityData.setCreatedBy(requestPayload.getOrderItemAfterData().getCreated_by());
            orderItemActivityData.setUpdatedBy(requestPayload.getOrderItemAfterData().getUpdated_by());

            log.info(" [prepareAndPersistOrderItemActivityData] : updated values for order item activity data is : "+ orderItemActivityData);
            boolean isQcHoldUnHoldEvent = this.isQcHoldUnHoldEvent(requestPayload);
            if(isQcHoldUnHoldEvent){
                return;
            }
            orderItemActivityDataRepository.save(orderItemActivityData);
        }catch (Exception e){
            log.error("[OrderActivityEventStrategy][prepareAndPersistOrderItemActivityData] : failed processing item level event for payload : {} ,  with exception : {}" , requestPayload, e.getMessage(), e);
        }


    }

    /**
     * fetching order activity event data based on the before and after transitions in the debezium payload
     *
     * @param requestPayload
     * @return
     * @throws JsonProcessingException
     */
    public OrderEventData fetchOrderActivityEvent(RequestPayload requestPayload) throws Exception {
        log.info("[OrderItemsOrderActivityProcessor][fetchOrderActivityEvent] : fetching order activity event for event message : {}", requestPayload);
        OrderEventData eventData = null;
        try {
            Transitions transitions = null;
            if (requestPayload.getOrderItemAfterData().getQcFailCount() >= 1 && !Constants.qcExcludeStatus.contains(requestPayload.getOrderItemAfterData().getStatus())) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.QC_FAIL);
            } else{
                transitions = events.getEvents().get(requestPayload.getOrderItemAfterData().getStatus());
            }
            Action action = new Action(ObjectUtils.isEmpty(requestPayload.getOrderItemBeforeData()) ? "null" : requestPayload.getOrderItemBeforeData().getStatus(), requestPayload.getOrderItemAfterData().getStatus());
            eventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();
        } catch (Exception e) {
            log.error("incorrect transition received for event message : {} ", requestPayload, e);
            throw new Exception("Invalid transition found " + e);
        }
        return eventData;
    }


    /**
     * generate idempotency key to be persisted, to maintain the uniqueness of the events
     *
     * @param requestPayload
     * @return
     */
    public String generateIdempotencyKey(RequestPayload requestPayload, String orderActivityEvent) {
        String idempotencyKey = requestPayload.getOrderItemId() + orderActivityEvent;
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(idempotencyKey.getBytes());
    }

    /**
     * creating a map order item dto based on key fields and values
     *
     * @param object
     * @param orderItemDtoMap
     */
    public void createOrderItemDtoMap(Object object, Map<String, Object> orderItemDtoMap) {
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                orderItemDtoMap.put(field.getName(), field.get(object));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * @param requestPayload
     * @param metaDataMap
     * @throws Exception
     */
    public void checkAndUpdateFulfillabilityEvent(RequestPayload requestPayload, Map<String, Object> metaDataMap) throws Exception {
        log.info(" [OrderActivityEventStrategy][checkAndUpdateFulfillabilityEvent] : checking fullfilability change event for payload : {} ", requestPayload);

        if (requestPayload.getOrderItemBeforeData().getFulfillableType().equals(requestPayload.getOrderItemAfterData().getFulfillableType())) {
            return;
        }

        try {
            OrderEventData orderEventData = null;
            Transitions transitions = null;
            Action action = null;
            if (requestPayload.getOrderItemAfterData().getFulfillableType().toString().equalsIgnoreCase("0")) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.FULLFILLABLE_EVENT);
                action = new Action(Constants.OrderActivityEventConstants.UNFULFILLABLE_EVENT, Constants.OrderActivityEventConstants.FULLFILLABLE_EVENT);
                orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();
            } else if (requestPayload.getOrderItemAfterData().getFulfillableType().toString().equalsIgnoreCase("1")) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.UNFULFILLABLE_EVENT);
                action = new Action(Constants.OrderActivityEventConstants.FULLFILLABLE_EVENT, Constants.OrderActivityEventConstants.UNFULFILLABLE_EVENT);
                orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();
            }
            log.info(" [OrderActivityEventStrategy][checkAndUpdateFulfillabilityEvent] : order event data received is : {} ", orderEventData);

            this.prepareAndPersistOrderItemActivityData(requestPayload, orderEventData);
            this.createAndPersistOrderActivityData(requestPayload, orderEventData, metaDataMap);
        }catch (Exception e){
            log.error(" [OrderActivityEventStrategy][checkAndUpdateFittingHoldEvents] : Exception caught while persisting fulfillable event for payload :  {}  with error message :  {}", requestPayload, e.getMessage(), e);
            throw new Exception("Failed to update fulfillable event");
        }

    }

    public void checkAndUpdateFittingHoldEvents(RequestPayload requestPayload, Map<String, Object> metaDataMap) throws Exception {
        log.info(" [OrderActivityEventStrategy][checkAndUpdateFittingHoldEvents] : checking fullfilability change event for payload : {} ", requestPayload);

        if (requestPayload.getOrderItemBeforeData().getHold().equals(requestPayload.getOrderItemAfterData().getHold())) {
            return;
        }
        try{
            OrderEventData orderEventData = null;
            Transitions transitions = null;
            Action action = null;
            if (Constants.OrderActivityEventConstants.PENDING_CUSTOMIZATION.equalsIgnoreCase(requestPayload.getOrderItemAfterData().getStatus()) && requestPayload.getOrderItemAfterData().getHold() == 1) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.HOLD_EVENT);
                action = new Action(Constants.OrderActivityEventConstants.FITTING_UNHOLD_EVENT, Constants.OrderActivityEventConstants.FITTING_HOLD_EVENT);
                orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();
            } else if (Constants.OrderActivityEventConstants.PENDING_CUSTOMIZATION.equalsIgnoreCase(requestPayload.getOrderItemAfterData().getStatus()) && requestPayload.getOrderItemAfterData().getHold() == 0) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.HOLD_EVENT);
                action = new Action(Constants.OrderActivityEventConstants.FITTING_HOLD_EVENT, Constants.OrderActivityEventConstants.FITTING_UNHOLD_EVENT);
                orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();
            }
            log.info(" [OrderActivityEventStrategy][checkAndUpdateFittingHoldEvents] : order event data received is : {} ", orderEventData);

            this.prepareAndPersistOrderItemActivityData(requestPayload, orderEventData);
            this.createAndPersistOrderActivityData(requestPayload, orderEventData, metaDataMap);
        }catch (Exception e){
            log.error(" [OrderActivityEventStrategy][checkAndUpdateFittingHoldEvents] : Exception caught while persisting hold/unhold event for payload :  {}  with error message :  {}", requestPayload, e.getMessage(), e);
            throw new Exception("Failed to update hold/unhold event");
        }

    }


    public void prepareNonTransitionalEvents(RequestPayload requestPayload, Map<String, Object> metaDataMap) throws Exception {
        log.info(" [OrderActivityEventStrategy][prepareNonTransitionalEvents] : checking non-transitional change event for payload : {} ", requestPayload);


        try{
            OrderEventData orderEventData = null;
            Transitions transitions = null;
            if (Constants.OrderActivityEventConstants.INVOICED_EVENT.equalsIgnoreCase(requestPayload.getOrderItemAfterData().getStatus())) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.PACKED_EVENT);
            }

            if (ObjectUtils.isNotEmpty(transitions)) {
                Action action = new Action(requestPayload.getOrderItemBeforeData().getStatus(), requestPayload.getOrderItemAfterData().getStatus());
                orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();

                log.info(" [OrderActivityEventStrategy][prepareNonTransitionalEvents] : order event data received is : {} ", orderEventData);

                this.prepareAndPersistOrderItemActivityData(requestPayload, orderEventData);
                this.createAndPersistOrderActivityData(requestPayload, orderEventData, metaDataMap);
            }
        }catch (Exception e){
            log.error(" [OrderActivityEventStrategy][prepareNonTransitionalEvents] : Exception caught while persisting non-transitional event for payload :  {}  with error message :  {}", requestPayload, e.getMessage(), e);
            throw new Exception("Failed to update non-transitional event");
        }

    }


    public void updateShipmentLevelData(RequestPayload requestPayload, Map<String, Object> metaDataMap, OrderEventData orderEventData) throws Exception {
        log.info(" [OrderActivityStrategy] [updateShipmentLevelData] : processing shipment level updates for payload : {} , and order event data : {}", requestPayload, orderEventData);
        if (Constants.shipmentLevelEvents.contains(orderEventData.getOrderActivityEvent())) {
            List<OrderItemActivityData> orderItemActivityDataList = orderItemActivityDataRepository.findByWmsOrderCodeAndUpdatedStatus(requestPayload.getWmsOrderCode(), orderEventData.getOrderActivityEvent());
            Set<Integer> orderItemIds = orderItemActivityDataList.stream().map(OrderItemActivityData::getOrderItemId).collect(Collectors.toSet());
            orderItemIds.add(requestPayload.getOrderItemId());
            metaDataMap.put("orderItemIds", orderItemIds);

            if (Constants.qcExcludeStatus.contains(orderEventData.getOrderActivityEvent())) {
                List<ShipmentDetails> shipmentDetailsList = shipmentDetailsRepository.findByShippingPackageIdIn(Collections.singletonList(requestPayload.getShippingPackageId()));
                metaDataMap.put(Constants.OrderActivityEventConstants.AWB_NUMBER, shipmentDetailsList.get(0).getTrackingNumber());
                metaDataMap.put(Constants.OrderActivityEventConstants.COURIER_CODE, shipmentDetailsList.get(0).getShippingProviderCode());
                metaDataMap.put(Constants.OrderActivityEventConstants.INVOICE_NUMBER, shipmentDetailsList.get(0).getInvoiceCode());
                metaDataMap.put(Constants.OrderActivityEventConstants.MANIFEST_NUMBER, shipmentDetailsList.get(0).getShippingManifestId());
            }

            if (Constants.OrderActivityEventConstants.INVOICE_GENERATED_EVENT.equalsIgnoreCase(orderEventData.getOrderActivityEvent())) {
                metaDataMap.put(Constants.OrderActivityEventConstants.INVOICE_NUMBER, monitorPanelDataService.getInvoiceNumber(requestPayload.getShippingPackageId()));
            }

            // TODO : need to add db creds for calling orderQc db to fetch qc hold reason to be added to map for later getting persisted in the event string as key -> failedReason
        }
    }

    public void prepareAndUpdatePickingSummaryEvent(RequestPayload requestPayload, Map<String, Object> metaDataMap) throws Exception {

        log.info("[OrderActivityEventStrategy][prepareAndUpdatePickingSummaryEvent] : processing picking summary event for payload : {}", requestPayload);

        try {

            List<PickingDetail> pickingDetailList = pickingDetailRepository.findByShipmentId(requestPayload.getShippingPackageId());
            Transitions transitions = events.getEvents().get(Constants.OrderActivityEventConstants.PICKING_SUMMARY_EVENT);
            Action action = new Action(Constants.OrderActivityEventConstants.PICKING_SUMMARY_EVENT, Constants.OrderActivityEventConstants.PICKING_SUMMARY_EVENT);
            OrderEventData orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();
            OrderActivityEvent orderActivityEvent = orderActivityEventRepository.findByReferenceIdAndEvent(requestPayload.getWmsOrderCode(), "PICKING_SUMMARY_CREATED");
            List<Integer> productIdList = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(orderActivityEvent) && orderActivityEvent.getMessage().contains(requestPayload.getPickingDetailData().getPickingSummaryId().toString())) {
                orderActivityEvent.getMessage().contains(requestPayload.getPickingDetailData().getPickingSummaryId().toString());
                for (PickingDetail pickingDetail : pickingDetailList) {
                    if (orderEventData.getEventMessage().contains(pickingDetail.getPickingSummaryId().toString())) {
                        productIdList.add(pickingDetail.getProductId());
                    }
                }
            }
            productIdList.add(requestPayload.getOrderItemId());
            metaDataMap.put("pIds", productIdList);
            this.createAndPersistPickingSummaryEventData(requestPayload, orderEventData, metaDataMap);
            this.prepareAndPersistPickingSummaryItemEventData(requestPayload, orderEventData);

            log.info("[OrderActivityEventStrategy][prepareAndUpdatePickingSummaryEvent] : picking summary event updated successfully ");
        }catch (Exception e){
            log.error("[OrderActivityEventStrategy][prepareAndUpdatePickingSummaryEvent] : failed processing picking summary event for payload : {} ,  with message : {}", requestPayload, e.getMessage(), e);
        }

    }


    /**
     * create and persist picking summary event data
     *
     * @param requestPayload
     * @param
     */
    public void createAndPersistPickingSummaryEventData(RequestPayload requestPayload, OrderEventData orderEventData, Map<String, Object> metaDataMap) throws Exception {

        log.info("[OrderActivityStrategy][createAndPersistPickingSummaryEventData] : preparing picking summary event data for payload : {} ", requestPayload);
        try {
            String eventString = orderEventData.getEventMessage();
            for (Map.Entry<String, Object> entry : metaDataMap.entrySet()) {
                if (eventString.contains(entry.getKey())) {
                    eventString = eventString.replace(entry.getKey(), entry.getValue().toString());
                }
            }
            eventString = eventString.replace("[", "").replace("]", "").replace("{", "").replace("}", "");
            OrderActivityEvent orderActivityEvent = new OrderActivityEvent();
            orderActivityEvent.setIdempotancyKey(this.generateIdempotencyKey(requestPayload, orderEventData.getOrderActivityEvent()));
            orderActivityEvent.setReferenceId(requestPayload.getWmsOrderCode());
            orderActivityEvent.setEvent(orderEventData.getOrderActivityEvent());
            orderActivityEvent.setFacilityCode(requestPayload.getPickingDetailData().getFacility());
            orderActivityEvent.setCreatedAt(new Date());
            orderActivityEvent.setMessage(eventString);
            orderActivityEvent.setUpdatedAt(new Date());
            orderActivityEvent.setCreatedBy(requestPayload.getPickingDetailData().getCreatedBy());
            orderActivityEvent.setUpdatedBy(requestPayload.getPickingDetailData().getCreatedBy());
            Instant instant = Instant.parse(requestPayload.getPickingDetailData().getUpdatedAt());
            orderActivityEvent.setItemEventUpdatedAt(new Date(instant.toEpochMilli()));

            log.info("[OrderActivityStrategy][createAndPersistPickingSummaryEventData] : prepared picking summary event data is  : {} ", orderActivityEvent);

            orderActivityEventRepository.save(orderActivityEvent);
        }catch (Exception e){
            log.info("[OrderActivityStrategy][createAndPersistPickingSummaryEventData] : failed updating picking summary event data for paylaod  : {}  , with message  : {} ",requestPayload, e.getMessage(), e);
            throw new Exception("Failed to prepare and persist picking summary event data");
        }
    }

    /**
     * prepare and persist picking summary event data
     *
     * @param requestPayload
     */
    public void prepareAndPersistPickingSummaryItemEventData(RequestPayload requestPayload, OrderEventData orderEventData) throws Exception {

        try{
            log.info("[OrderActivityEventStrategy][prepareAndPersistPickingSummaryItemEventData] : processing picking summary event payload : {}" , requestPayload);

            OrderItemActivityData orderItemActivityData = orderItemActivityDataRepository.findByWmsOrderCodeAndOrderItemId(requestPayload.getOrderItemAfterData().getWmsOrderCode(), requestPayload.getPickingDetailData().getWmsOrderItemId());

            log.info("[OrderActivityEventStrategy][prepareAndPersistPickingSummaryItemEventData] : order item activity data received is  : {}" , orderItemActivityData);

            Instant instant = null;
            if (ObjectUtils.isEmpty(orderItemActivityData)) {
                orderItemActivityData = new OrderItemActivityData();
                orderItemActivityData.setWmsOrderCode(requestPayload.getOrderItemAfterData().getWmsOrderCode());
                orderItemActivityData.setOrderItemId(requestPayload.getOrderItemAfterData().getOrderItemId());
                orderItemActivityData.setUpdatedStatus(orderEventData.getOrderActivityEvent());
                orderItemActivityData.setCurrentStatus(requestPayload.getStatus());
                orderItemActivityData.setCreatedAt(new Date());
                orderItemActivityData.setUpdatedAt(new Date());
                instant = Instant.ofEpochMilli(Long.valueOf(requestPayload.getOrderItemAfterData().getUpdated_at()));
            } else {
                orderItemActivityData.setUpdatedStatus(orderEventData.getOrderActivityEvent());
                orderItemActivityData.setCurrentStatus(requestPayload.getStatus());
                orderItemActivityData.setUpdatedAt(new Date());
                instant = Instant.ofEpochMilli(Long.valueOf(requestPayload.getOrderItemAfterData().getUpdated_at()));
            }
            orderItemActivityData.setItemEventUpdatedAt(Date.from(instant));
            orderItemActivityData.setCreatedBy(requestPayload.getOrderItemAfterData().getCreated_by());
            orderItemActivityData.setUpdatedBy(requestPayload.getOrderItemAfterData().getUpdated_by());

            log.info("[OrderActivityEventStrategy][prepareAndPersistPickingSummaryItemEventData] : order item activity data prepared for  picking summary event is : {}" , orderItemActivityData);
            orderItemActivityDataRepository.save(orderItemActivityData);
        }catch (Exception e){
            log.error("[OrderActivityEventStrategy][prepareAndPersistPickingSummaryItemEventData] : failed processing picking summary event payload : {} , with message : {}"  , requestPayload, e.getMessage(), e);
            throw  new Exception("Failed preparing and persisting picking item summary event data");
        }

    }


    public void processEarlierTransitions(RequestPayload requestPayload, Map<String, Object> metaDataMap) throws Exception {
        log.info(" [OrderActivityEventStrategy][processEarlierTransitions] : processing transitions before main event for payload : {} ", requestPayload);

        if (requestPayload.getOrderItemBeforeData().getHold().equals(requestPayload.getOrderItemAfterData().getHold())) {
            return;
        }
        try{
            OrderEventData orderEventData = null;
            Transitions transitions = null;
            Action action = null;

            if (Constants.OrderActivityEventConstants.READY_TO_SHIP_EVENT.equalsIgnoreCase(requestPayload.getOrderItemAfterData().getStatus()) && Constants.OrderActivityEventConstants.MANIFEST_OPERATION.equals(requestPayload.getOrderItemAfterData().getOperation())) {
                transitions = events.getEvents().get(Constants.OrderActivityEventConstants.MANIFEST_EVENT);
            }

            action = new Action(requestPayload.getOrderItemBeforeData().getStatus(), requestPayload.getOrderItemAfterData().getStatus());
            orderEventData = transitions.getOrderActivityTransitions().get(action).getOrderEventData();

            log.info(" [OrderActivityEventStrategy][processEarlierTransitions] : order event data received is : {} ", orderEventData);

            this.prepareAndPersistOrderItemActivityData(requestPayload, orderEventData);
            this.createAndPersistOrderActivityData(requestPayload, orderEventData, metaDataMap);
        }catch (Exception e){
            log.error(" [OrderActivityEventStrategy][processEarlierTransitions] : Exception caught while persisting earlier transitions event for payload :  {}  with error message :  {}", requestPayload, e.getMessage(), e);
            throw new Exception("Failed to update earlier transitions event");
        }

    }


    public boolean isQcHoldUnHoldEvent(RequestPayload requestPayload){

        List<OrderItemActivityData> qcHoldEvent = orderItemActivityDataRepository.findByWmsOrderCodeAndUpdatedStatus(requestPayload.getWmsOrderCode(), Constants.OrderActivityEventConstants.QC_HOLD_EVENT);
        if(!qcHoldEvent.isEmpty()){
            return true;
        }
        return false;
    }

}
