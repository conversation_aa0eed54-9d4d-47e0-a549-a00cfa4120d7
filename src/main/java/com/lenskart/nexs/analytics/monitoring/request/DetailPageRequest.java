package com.lenskart.nexs.analytics.monitoring.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.analytics.model.request.MonitorPanelFilters;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetailPageRequest {
    @NonNull
    private String frTag;
    @NonNull
    private String category;
    @NonNull
    private String status;
    private int page=1;
    private int pageSize=25;
    private MonitorPanelFilters monitorPanelFilters;
    private CustomRangeFilter customRangeFilter;

    private String severity;
}
