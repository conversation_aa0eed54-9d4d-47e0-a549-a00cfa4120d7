package com.lenskart.nexs.analytics.monitoring.response;

import com.lenskart.nexs.analytics.monitoring.pojo.OrderItemGroup;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ListingPageResponse {
//   private List<Map<String, String>> orderItemTagGroupMap;
   private Map<String, OrderItemGroup> orderItemTagGroupMap;
   private Map<String, Integer> overallStatusCountMap;
   private Integer totalOrders;
}
