package com.lenskart.nexs.analytics.monitoring.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@JsonIgnoreProperties
public class MonitorPanelTagCountResponse {

    @JsonProperty("v3FrTag")
    private String v3FrTag;

    @JsonProperty("pickingPriority")
    private int pickingPriority;

    @JsonProperty("totalCount")
    private long totalCount;
}
