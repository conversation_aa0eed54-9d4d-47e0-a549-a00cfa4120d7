package com.lenskart.nexs.analytics.monitoring.repository.monitorpanel;

import com.lenskart.nexs.analytics.monitoring.entities.monitorpanel.MonitorPanelData1;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import javax.persistence.Tuple;
import java.util.List;

public interface MonitorPanelData1Repository extends CrudRepository<MonitorPanelData1, Long>, JpaSpecificationExecutor<MonitorPanelData1>, PagingAndSortingRepository<MonitorPanelData1,Long> {

    @Query(value = "SELECT  fr_tag\n" +
            "         as 'tag',mp_status as status,\n" +
            "       count(*) as 'totalCount' FROM monitor_panel.monitor_panel_data_one where is_fulfillable =?1  group by  mp_status,fr_tag ;", nativeQuery = true)
    List<Tuple> findOrderItemCountByFRTag(int isFullfillable);

    void deleteAllInBatch();

    @Modifying
    @Query(
            value = "truncate table order_data_snapshot1",
            nativeQuery = true
    )
    void truncateTable();

//    @Modifying
//    @Query(value = "UPDATE MonitorPanelData1 SET group_effective_status =:groupEffectiveStatus, effective_group_update_time = :effectiveGroupUpdateTime WHERE group_id=:groupId")
//    void updateGroupEffectiveStatus(@Param("groupId") String groupId, @Param("groupEffectiveStatus") String groupEffectiveStatus, @Param("effectiveGroupUpdateTime") Date effectiveGroupUpdateTime);

    List<MonitorPanelData1> findByGroupId(String groupId);

    @Query(value ="\n" +
            "\tSELECT facility_code as facilityCode,\n" +
            "    COUNT(CASE WHEN is_fulfillable = 0  THEN 'FULFILLABLE' ELSE NULL END) AS 'FULFILLABLE',\n" +
            "    COUNT(CASE WHEN is_fulfillable = 1 THEN 'UN_FULFILLABLE' ELSE NULL END) AS 'UNFULFILLABLE',\n" +
            "    COUNT(CASE WHEN is_allocated = 1 AND is_fulfillable = 0 THEN 'ALLOCATED' ELSE NULL END) AS 'ALLOCATED'\n" +
            "    FROM nexs_dp.order_data_snapshot1\n" +
            "    WHERE product_id = :pid\n" +
            "    AND v3_fr_tag IN :frTags\n" +
            "    AND order_item_created_at BETWEEN :startDate AND :endDate GROUP BY facility_code;", nativeQuery = true)
    List<Tuple> getFacilityBasedProductDetails(@Param("pid")Long pid, @Param("frTags")List<String> frTags, @Param("startDate")String startDate , @Param("endDate") String endDate);


    @Query(value = "select facility_code as facilityCode, count(product_id) as total\n" +
            "from order_data_snapshot1\n" +
            "where product_id = :pid\n" +
            "AND v3_fr_tag IN :frTags\n" +
            "AND order_item_created_at BETWEEN :startDate AND :endDate GROUP BY facility_code;", nativeQuery = true)
    List<Tuple> getProductCountForAllFacilities(@Param("pid")Long pid, @Param("frTags")List<String> frTags, @Param("startDate")String startDate , @Param("endDate") String endDate);
}
