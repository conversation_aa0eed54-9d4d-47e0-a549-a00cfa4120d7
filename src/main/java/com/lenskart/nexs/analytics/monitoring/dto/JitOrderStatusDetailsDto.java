package com.lenskart.nexs.analytics.monitoring.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;




@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class JitOrderStatusDetailsDto {

    @JsonProperty("fitting_id")
    private Integer fittingId;
    @JsonProperty("rx_status")
    private String rxString;
    @JsonProperty("order_status")
    private String orderStatus;
    @JsonProperty("sub_status")
    private String subStatus;
    @JsonProperty("blank_pid")
    private String blankPid;
    /*    @Column(name = "uw_item_id")
       private Integer uwItemId;*/
    @JsonProperty("lens")
    private String lens;
    @JsonProperty("order_code")
    private Integer orderCode;
    @JsonProperty("country_code")
    private String countryCode;
    @JsonProperty("facility")
    private String facility;
    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("order_sub_type")
    private String orderSubType;
    @JsonProperty("lens_id")
    private Integer lensId;
    @JsonProperty("lens_package")
    private String lensPackage;
    @JsonProperty("barcode")
    private String barcode;
    @JsonProperty("actual_pid")
    private Integer actualPid;
    @JsonProperty("increment_id")
    private Integer incrementId;
    @JsonProperty("source")
    private String source;
    @JsonProperty("source_reference_id")
    private String sourceReferenceId;
    @JsonProperty("qc_fail_count")
    private Integer qcFailCount;
    @JsonProperty("lk_status")
    private String lkStatus;
}
