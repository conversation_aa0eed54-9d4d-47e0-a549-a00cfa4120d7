package com.lenskart.nexs.analytics.monitoring.entities.mongodb;

import com.lenskart.nexs.analytics.model.request.MonitorPanelFilters;
import com.lenskart.nexs.analytics.monitoring.model.MonitorPanelSavedView;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

@Data
@Document
public class MonitorPanelSavedViewsOfEmp {

    @Id
    private String empCode;

    List<MonitorPanelSavedView> monitorPanelSavedViewList;

    private Date updatedAt;


}
