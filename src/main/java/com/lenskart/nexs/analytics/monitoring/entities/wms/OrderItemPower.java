package com.lenskart.nexs.analytics.monitoring.entities.wms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.wms.entities.base.baseEntity.BaseEntity;
import com.lenskart.nexs.wms.entities.constants.SqlTableConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Table(name = SqlTableConstants.ORDER_ITEM_POWER)
public class OrderItemPower extends BaseEntity {

    @Column(name ="order_id")
    private Integer orderId;

    @Column(name ="product_id")
    private Integer productId;

    @Column(name ="shell_id")
    private Integer shellId;

    @Column(name ="right_lens")
    private String rightLens;

    @Column(name ="axis")
    private String axis;

    @Column(name ="sph")
    private String sph;

    @Column(name ="lens_height")
    private String lensHeight;

    @Column(name ="lens_width")
    private String lensWidth;

    @Column(name ="cyl")
    private String cyl;

    @Column(name ="ap")
    private String ap;

    @Column(name ="package")
    private String packageData;

    @Column(name ="lens_package_type")
    private String lensPackageType;

    @Column(name ="patient_DOB")
    private Date patientDOB;

    @Column(name ="patient_name")
    private String patientName;

    @Column(name ="patient_dr_name")
    private String patientDrName;

    @Column(name ="gender")
    private String gender;

    @Column(name ="patient_comments")
    private String patientComments;

    @Column(name ="pd")
    private String pd;

    @Column(name ="related_product")
    private String relatedProduct;

    @Column(name ="bottom_distance")
    private String bottomDistance;

    @Column(name ="edge_distance")
    private String edgeDistance;

    @Column(name ="effective_dia")
    private String effectiveDia;

    @Column(name ="near_pd")
    private String nearPD;

    @Column(name ="top_distance")
    private String topDistance;

    @Column(name="updated_at",insertable = false,updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name ="package_price")
    private Double packagePrice;

    @Column(name ="customer_axis")
    private String customerAxis;

    @Column(name ="customer_sph")
    private String customerSPH;

    @Column(name ="customer_cyl")
    private String customerCYL;

    @Column(name ="web_package")
    private String webPackage;

    @Column(name ="coating_oid")
    private String coatingOid;

    @Column(name ="coating_name")
    private String coatingName;

    @Column(name ="lens_id")
    private Integer lensId;

    @Column(name ="lens_index")
    private Double lensIndex;

    @Column(name ="lensname")
    private String lensName;

    @Column(name ="lenstype")
    private String lensType;

    @Column(name ="type")
    private String type;

    @Column(name ="with_ap")
    private String withAp;

    @Column(name ="vendor_package_name")
    private String vendorPackageName;

    @Column(name ="brand")
    private String brand;

    @Column(name ="is_active")
    private String isActive;

    @Column(name ="coating")
    private String coating;
}
