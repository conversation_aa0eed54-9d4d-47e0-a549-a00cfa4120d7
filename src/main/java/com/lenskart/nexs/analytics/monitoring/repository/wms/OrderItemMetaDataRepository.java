package com.lenskart.nexs.analytics.monitoring.repository.wms;


import com.lenskart.nexs.analytics.monitoring.entities.wms.OrderItemMetaData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface OrderItemMetaDataRepository extends JpaRepository<OrderItemMetaData,Integer> {
//    List<OrderItemMetaData> findByOrder_Item_Id(Integer orderItemId);
}
