package com.lenskart.nexs.analytics.monitoring.processor;

import com.lenskart.nexs.analytics.monitoring.strategy.MonitorPanelBaseStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
public class MonitorPanelExecutorConfiguration {

    @Bean
    public MonitorPanelExecutor monitorPanelExecutor(List<MonitorPanelBaseStrategy> strategies) {
        return new MonitorPanelExecutor(
                strategies.stream()
                        .collect(Collectors.toMap(MonitorPanelBaseStrategy::supportedStates, Function.identity()))
        );
    }
}
