package com.lenskart.nexs.analytics.monitoring.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ManualAsrsWaveResponse implements Serializable {
    @JsonProperty("groupEffectiveStatus")
    String groupEffectiveStatus;
    @JsonProperty("processingType")
    String processingType;
    @JsonProperty("pickingPriority")
    String pickingPriority;
    @JsonProperty("jitType")
    String jitType;
    @JsonProperty("jitGroupEffectiveStatus")
    String jitGroupEffectiveStatus;
    @JsonProperty("isAsrsOrder")
    Boolean isAsrsOrder;
    @JsonProperty("navChannel")
    String navChannel;
    @JsonProperty("count")
    Integer count;
}
