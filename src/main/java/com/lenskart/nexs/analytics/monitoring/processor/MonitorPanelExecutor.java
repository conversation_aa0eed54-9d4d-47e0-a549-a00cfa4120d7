package com.lenskart.nexs.analytics.monitoring.processor;

import com.lenskart.nexs.analytics.monitoring.enums.QueryType;
import com.lenskart.nexs.analytics.monitoring.request.RequestPayload;
import com.lenskart.nexs.analytics.monitoring.strategy.MonitorPanelBaseStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@AllArgsConstructor
public class MonitorPanelExecutor {
    private final Map<QueryType, MonitorPanelBaseStrategy> executors;

    public void doExecute(RequestPayload requestPayload, QueryType state) throws Exception {
        executors.get(state).doExecute(requestPayload);
    }
}
