package com.lenskart.nexs.analytics.consumer;

import com.newrelic.api.agent.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import com.lenskart.nexs.analytics.constants.AnalyticsConstants;
import com.lenskart.nexs.analytics.service.AccountBarcodeReportService;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.ims.entity.BarcodeItem;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class NexsAnalyticsConsumer {

    @Autowired
    AccountBarcodeReportService accountBarcodeReportService;

    @Trace(dispatcher = true)
    @KafkaListener(topics = "${nexs.analytics.kafka.topic}", groupId = "${spring.kafka.group-id}",containerFactory = "kafkaListenerContainerAnalyticsBarcodeTransactionalFactory")
    public void listen(@Payload Message<Object> message, Acknowledgment ack) throws Exception {
        log.info("NexsAnalyticsConsumer: consumed message: {}", message);
        String barcode = null;
        try {
            log.info((message.getHeaders().get(AnalyticsConstants.ACCOUNT_BARCODE_REPORT_STATE).toString())+"");
            String itemStatus = (message.getHeaders().get(AnalyticsConstants.ACCOUNT_BARCODE_REPORT_STATE).toString());
            BarcodeItem barcodeItem = ObjectHelper.readValue(message.getPayload().toString() , BarcodeItem.class);
            barcode = barcodeItem.getBarcode();
            accountBarcodeReportService.sendAnalyticReport( barcodeItem, itemStatus);
        } catch (Exception e) {
            log.error("NexsAnalyticsConsumer: exception for barcode :" + barcode + ",  exception : " + e.getMessage(), e);
        } finally {
        	log.info("NexsAnalyticsConsumer: processed message: {}", message);
        	ack.acknowledge();
        }
    }

}
