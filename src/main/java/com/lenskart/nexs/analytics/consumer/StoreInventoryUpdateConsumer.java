package com.lenskart.nexs.analytics.consumer;

import com.lenskart.nexs.analytics.service.StoreInventoryService;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.ims.entity.BarcodeItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class StoreInventoryUpdateConsumer {

    @Autowired
    StoreInventoryService storeInventoryService;

    @Value("${nexs.store.inventory.update.consumer.enabled}")
    private boolean isStoreInventoryUpdateEnable;

    @KafkaListener(topics = "${nexs.analytics.kafka.topic}", groupId = "nexs_analytics_update_group")
    public void storeInventoryUpdateConsumer(@Payload Message<Object> message, Acknowledgment ack){
        try{
            if(isStoreInventoryUpdateEnable) {
                log.info("[storeInventoryUpdateConsumer] consumed message: {} at {}", message, new Date());
                BarcodeItem barcodeItem = ObjectHelper.readValue(message.getPayload().toString(), BarcodeItem.class);
                storeInventoryService.updateStoreInventoryEntity(barcodeItem);
            }
        } catch (Exception e){
            log.error("[storeInventoryUpdateConsumer] failed with exception: {} for message: {}", e.getMessage(), message, e);
        } finally {
            log.info("storeInventoryUpdateConsumer completed : {}", message);
            ack.acknowledge();
        }
    }
}
