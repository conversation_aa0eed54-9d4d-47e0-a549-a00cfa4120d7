package com.lenskart.nexs.analytics.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.analytics.dao.CacheDAO;
import com.lenskart.nexs.analytics.entity.unireports.LcrdSyncStatus;
import com.lenskart.nexs.analytics.model.LCRDWmsModel;
import com.lenskart.nexs.analytics.repository.unireports.LcrdSyncStatusRepository;
import com.lenskart.nexs.analytics.service.LenskartCompleteReportDailyService;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class NexsDailyReportSyncConsumer {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private LenskartCompleteReportDailyService lenskartCompleteReportDailyService;

    @Autowired
    private LcrdSyncStatusRepository lcrdSyncStatusRepository;

    @Value("${daily.sales.order.report.consumer.enabled:false}")
    private Boolean retryDailyReportOrderConsumerEnabled;

    @Value("${daily.sales.order.daily.report.consumer.redis.enabled:false}")
    private Boolean retryDailyReportOrderConsumerRedisEnabled;

    @Autowired
    private CacheDAO cacheDAO;

    @Trace(dispatcher = true)
    @KafkaListener(topics = "${nexs.analytics.daily.report.kafka.topic}", groupId = "${nexs.analytics.daily.report" +
            ".kafka.topic.group}")
    public void listen(String message, Acknowledgment ack) throws Exception {
    	log.info("NexsDailyReportSyncConsumer message : {}", message);
        LCRDWmsModel lcrdWmsData = new LCRDWmsModel();
        try {
            if(retryDailyReportOrderConsumerEnabled) {
                lcrdWmsData = objectMapper.readValue(message, LCRDWmsModel.class);
                log.info("Fetching data for order_item_id : " + lcrdWmsData.getUwItemId());
                String redisKey = "NEXS_DAILY_REPORT_ANALYTCS_CACHE_KEY-"+lcrdWmsData.getUwItemId();
                if(cacheDAO.hasKey(redisKey)) {
                    log.info("Multiple DISPATCHED request in same time interval for order item_id : {}",
                            lcrdWmsData.getUwItemId());
                    return;
                }
                lenskartCompleteReportDailyService.buildPayloadAndInsert(lcrdWmsData);
                if(retryDailyReportOrderConsumerRedisEnabled)
                cacheDAO.putVal(redisKey, lcrdWmsData.getUwItemId(), 5L, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error("NexsDailyReportSyncConsumer error: {}", e.getMessage(), e);
            LcrdSyncStatus failureHistoryRecord = lcrdSyncStatusRepository.findByStatusAndHistoryId("failure",
                    lcrdWmsData.getHistoryId());
            if (failureHistoryRecord == null) {
                failureHistoryRecord = new LcrdSyncStatus();
                failureHistoryRecord.setStatus("failure");
                failureHistoryRecord.setFailureCount(0);
            }
            failureHistoryRecord.setFailureCount(failureHistoryRecord.getFailureCount() + 1);
            failureHistoryRecord.setReason(e.getMessage());
            failureHistoryRecord.setHistoryId(lcrdWmsData.getHistoryId());
            failureHistoryRecord.setUpdatedAt(new Date());
            lcrdSyncStatusRepository.save(failureHistoryRecord);
        } finally {
	        log.info("NexsDailyReportSyncConsumer completed : {}", message);
	        ack.acknowledge();
        }
    }

}

