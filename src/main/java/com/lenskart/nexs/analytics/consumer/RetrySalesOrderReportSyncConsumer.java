package com.lenskart.nexs.analytics.consumer;

import com.lenskart.nexs.analytics.service.LenskartCompleteReportDailyService;
import com.lenskart.nexs.analytics.util.ObjectHelper;
import com.lenskart.nexs.wms.response.OrderStatusUpdateResponse;
import com.newrelic.api.agent.Trace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RetrySalesOrderReportSyncConsumer {

    @Autowired
    private LenskartCompleteReportDailyService lenskartCompleteReportDailyService;

    @Value("${retry.sales.order.report.consumer.enabled:true}")
    private Boolean retrySalesOrderConsumerEnabled;

    @Trace(dispatcher = true)
    @KafkaListener(topics = "${nexs.sales.order.report.kafka.topic}", groupId = "${nexs.sales.order.report.sync.topic.group}")
    public void listen(@Payload String message, Acknowledgment ack) throws Exception {
        log.info("RetrySalesOrderReportSyncConsumer: consumed message: {}", message);
        try {
            if(retrySalesOrderConsumerEnabled) {
                OrderStatusUpdateResponse orderStatusUpdateResponse = ObjectHelper.getObjectMapper().readValue(message, OrderStatusUpdateResponse.class);
                lenskartCompleteReportDailyService.buildNexsOrderSalesReportAndInsert(orderStatusUpdateResponse);
            }
        } catch (Exception e) {
            log.error("RetrySalesOrderReportSyncConsumer: exception : {} , message: {}", e.getMessage(), message,e);
        } finally {
            log.info("RetrySalesOrderReportSyncConsumer: processed message: {}", message);
            ack.acknowledge();
        }

    }
}
