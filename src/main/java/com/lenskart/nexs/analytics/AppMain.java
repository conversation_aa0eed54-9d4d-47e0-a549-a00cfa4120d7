package com.lenskart.nexs.analytics;

import com.lenskart.nexs.analytics.repository.nexs.AnalyticsPurchaseInvoiceRepository;
import com.lenskart.nexs.analytics.repository.nexs.AnalyticsPurchaseOrderItemRepository;
import com.lenskart.nexs.analytics.repository.unireports.AccountBarcodeReportRepository;
import com.lenskart.nexs.analytics.repository.unireports.LenskartCompleteReportDailyRepository;
import com.lenskart.nexs.analytics.repository.unireports.ShippingPackageTimelineRepository;
import com.lenskart.nexs.analytics.repository.wms.*;
import com.lenskart.nexs.common.entity.entityService.EntitySeriesPrefixFormatEntityService;
import com.lenskart.nexs.common.entity.po.invoice.DebitNoteHeaderEntity;
import com.lenskart.nexs.common.entity.repositories.*;
import com.lenskart.nexs.common.entity.repositories.invoice.DebitNoteHeaderRepository;
import com.lenskart.nexs.common.entity.repositories.invoice.InvoiceSeqGenRepository;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {
        MongoAutoConfiguration.class,
        MongoDataAutoConfiguration.class
})
//@EntityScan(basePackages = { "com.lenskart.*"})
@ComponentScan(basePackages = { "com.lenskart.nexs" }, excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.lenskart.nexs.common.dbutil.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.lenskart.nexs.wms.*")})
//@EnableJpaRepositories(basePackages = {"com.lenskart.nexs.*"})
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT30S")
@EnableJpaAuditing
//@EnableAutoConfiguration
@Slf4j
public class AppMain {

    public static void main(String[] args) {
        log.info("Analytics Application Up.");
        SpringApplication.run(AppMain.class, args);
    }

}
