package com.lenskart.nexs.analytics.controller;

import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.request.InvoiceDateUpdateRequest;
import com.lenskart.nexs.analytics.service.LenskartCompleteReportDailyService;
import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import com.lenskart.nexs.responseBuilder.ResponseBuilder;
import com.lenskart.nexs.wms.response.OrderStatusUpdateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/nexs/analytics/")
@Slf4j
public class NexsAnalyticsController {

        @Autowired
        private ResponseBuilder responseBuilder;

        @Autowired
        private LenskartCompleteReportDailyService lenskartCompleteReportDailyService;

        @RequestMapping(value = Constants.DAILY_REPORT_SYNC, method = RequestMethod.POST)
        public ResponseEntity<BaseResponseModel> syncDailReports(@RequestBody List<Integer> nexsOrderIds,
                                                                 @RequestParam(value = "minHistoryId",
                                                                         defaultValue = "0") int minHistoryId,
                                                                 @RequestParam(value = "maxHistoryId") int maxHistoryId) throws Exception {
            boolean isSuccess = lenskartCompleteReportDailyService.syncItems(nexsOrderIds, minHistoryId, maxHistoryId);
            return responseBuilder.successResponse(isSuccess, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
        }
    @RequestMapping(value = Constants.POPULATE_INVOICE_DATE, method = RequestMethod.PUT)
    public ResponseEntity<BaseResponseModel> poupulateInvoiceDate(@RequestBody InvoiceDateUpdateRequest invoiceDateUpdateRequest) throws Exception {
        log.info("Inside populateInvoiceDate Controller Layer  for request : {} ", invoiceDateUpdateRequest);
        try {
            lenskartCompleteReportDailyService.populateInvoiceDate(invoiceDateUpdateRequest);
            return responseBuilder.successResponse("", "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
        } catch (Exception e) {
            log.error("Exception occured while processing poupulateInvoiceDate for request : {} ", invoiceDateUpdateRequest);
            return responseBuilder.failureResponse("", "FAILURE", ResponseCodes.RESPONSE_FAILURE);
        }
    }

    @RequestMapping(value = Constants.PUSH_TO_NEXS_SALES_ORDER_REPORT, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> pushToSalesOrderAnalyticsReport(@RequestBody OrderStatusUpdateResponse orderStatusUpdateResponse) throws Exception {
        log.info("Inside pushToSalesOrderAnalyticsReport  Controller Layer  for request : {} ", orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
        try {
            lenskartCompleteReportDailyService.buildNexsOrderSalesReportAndInsert(orderStatusUpdateResponse);
            return responseBuilder.successResponse("", "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
        } catch (Exception e) {
            log.error("Exception occured while processing pushToSalesOrderAnalyticsReport for request : {} ", orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            return responseBuilder.failureResponse("", "FAILURE", ResponseCodes.RESPONSE_FAILURE);
        }
    }

    @RequestMapping(value = Constants.PUSH_TO_NEXS_SALES_ORDER_TOPIC, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> pushToSalesOrderAnalyticsTopic(@RequestBody List<String> shippingIds) throws Exception {
        log.info("Inside pushToSalesOrderAnalyticsTopic  Controller  for request : {} ", shippingIds.size());
        try {
            lenskartCompleteReportDailyService.syncForSalesOrderReport(shippingIds);
            return responseBuilder.successResponse("", "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
        } catch (Exception e) {
            log.error("Exception occured while processing pushToSalesOrderAnalyticsTopic for request : {} ", shippingIds.size());
            return responseBuilder.failureResponse("", "FAILURE", ResponseCodes.RESPONSE_FAILURE);
        }
    }

    /* for re pushing missed order item in daily report due to fdsdb change */
    @RequestMapping(value = Constants.DAILY_REPORT_REPUSH, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> repushMissingOrderToDailReports(@RequestBody List<String> shippingIds,
                                                             @RequestParam(value = "historyId") int historyId) throws Exception {
        boolean isSuccess = lenskartCompleteReportDailyService.syncMissingItemsToDailyReport(shippingIds, historyId);
        return responseBuilder.successResponse(isSuccess, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    /* for re pushing missed order item in daily report due to fdsdb change */
    @RequestMapping(value = Constants.DAILY_REPORT_SCHEDULER, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> repushMissingOrderToDailReports() throws Exception {
        lenskartCompleteReportDailyService.lenskartCompleteReportDailyScheduler();
        return responseBuilder.successResponse(true, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }

    @RequestMapping(value = Constants.DAILY_REPORT_SYNC_FOR_ORDER_ID, method = RequestMethod.POST)
    public ResponseEntity<BaseResponseModel> syncDailyReports(@RequestBody List<Integer> orderItemIds) throws Exception {
        boolean isSuccess = lenskartCompleteReportDailyService.syncDailyReportByOrderItems(orderItemIds);
        return responseBuilder.successResponse(isSuccess, "SUCCESS", ResponseCodes.RESPONSE_SUCCESS);
    }
}
