package com.lenskart.nexs.analytics.repository.wms;


import com.lenskart.nexs.analytics.entity.wms.InvoiceItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface InvoiceItemRepository extends JpaRepository<InvoiceItem,Integer> {

//    @Query(value = "SELECT * FROM invoice_item where invoice_number = ?1 and quantity=1 limit 1", nativeQuery = true)
//    InvoiceItem fetchInvoiceDetails(String invoiceNumber);

    @Query(value = "SELECT * FROM invoice_item where order_item_id = ?1 limit 1", nativeQuery = true)
    InvoiceItem fetchInvoiceDetails(Integer orderItemId);

    @Query(value = "SELECT * FROM invoice_item where fitting_id = ?1 and product_id = ?2 limit 1", nativeQuery = true)
    InvoiceItem findByFittingIdAndProductId(Integer fittingId, String pid);

    @Query(value = "SELECT * FROM invoice_item WHERE invoice_number = :invoiceNumber AND product_id = :productId", nativeQuery = true)
    InvoiceItem findByInvoiceNumberAndProductId(@Param("invoiceNumber") String invoiceNumber, @Param("productId") int productId);
}
