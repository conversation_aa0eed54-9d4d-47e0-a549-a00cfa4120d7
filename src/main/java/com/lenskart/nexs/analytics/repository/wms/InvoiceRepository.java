package com.lenskart.nexs.analytics.repository.wms;


import com.lenskart.nexs.analytics.entity.wms.Invoice;
import com.lenskart.nexs.analytics.entity.wms.InvoiceItem;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice,String> {


    @Query(value = "SELECT t FROM Invoice t where   t.shippingPackageId  = ?1 ")
    Invoice findByShipmentPackageId(String shipmentPackageId);

    @Query(value = "SELECT t FROM Invoice t where   t.id  = ?1 ")
    Invoice findByInvoiceId(String invoiceId);

    @Query(value = "select * from invoice t where t.id  in (?1)   ",nativeQuery = true)
    List<Invoice> findAllByInVoiceNumber(List<String> invoiceIds);

    @Query(value = "SELECT i.* FROM invoice i " +
            "INNER JOIN invoice_item it ON i.id = it.invoice_number " +
            "INNER JOIN order_items oi ON oi.order_item_id = it.order_item_id " +
            "WHERE it.id = :orderItemId", nativeQuery = true)
    Optional<Invoice> findByOrderItemId(@Param("orderItemId") String orderItemId);

}
