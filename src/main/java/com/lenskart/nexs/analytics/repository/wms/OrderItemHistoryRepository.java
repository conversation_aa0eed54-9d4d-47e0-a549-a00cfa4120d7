package com.lenskart.nexs.analytics.repository.wms;

import com.lenskart.nexs.analytics.entity.wms.OrderItemHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.persistence.Tuple;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OrderItemHistoryRepository extends JpaRepository<OrderItemHistory, Long> {
    List<OrderItemHistory> findAllByUpdatedAtBetween(
            Date start,
            Date end);

    @Query(value =  "SELECT  oih.history_id, oih.barcode, oih.order_item_id, oih.payment_method, oih.wms_order_code, oih.product_id, oih.hold, oih.fulfilled_type,\n" +
            "oih.status as order_item_status, oih.updated_at as order_item_updated_at, oih.fitting_type, oh.increment_id, oh.created_at as order_created_at, oh.payment_status, oih.shipping_package_id, sh.status as shipment_status\n" +
            ",sh.created_at as shipment_created_at, sh.updated_at as shipment_updated_at, pd.picking_summary_id, ps.created_at as picking_summary_created_at\n" +
            "FROM    wms.order_items_history oih\n" +
            "INNER JOIN\n" +
            "        wms.orders_history oh\n" +
            "ON      oh.history_id = \n" +
            "        (\n" +
            "        SELECT  history_id\n" +
            "        FROM    wms.orders_history ohh\n" +
            "        WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                ohh.action_time DESC\n" +
            "        LIMIT 1\n" +
            "        )\n" +
            "INNER JOIN\n" +
            "        wms.shipment_history sh\n" +
            "ON      sh.history_id = \n" +
            "        (\n" +
            "        SELECT  history_id\n" +
            "        FROM    wms.shipment_history shh\n" +
            "        WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                shh.action_time DESC\n" +
            "        LIMIT 1\n" +
            "        ) \n" +
            "INNER JOIN wms.order_meta_data omd " +
            "ON omd.nexs_order_id = " +
            "(SELECT nexs_order_id FROM wms.order_meta_data omdh" +
            " WHERE oih.nexs_order_id = " +
            "omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) " +
            "AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED'"+
            "LEFT JOIN \n" +
            "        wms.picking_detail pd\n" +
            "ON pd.id = \n" +
            "(\n" +
            "  SELECT  id\n" +
            "        FROM    wms.picking_detail pdd\n" +
            "        WHERE  pdd.wms_order_item_id=oih.id and pdd.updated_at<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                pdd.updated_at DESC\n" +
            "        LIMIT 1\n" +
            ")        \n" +
            "LEFT JOIN \n" +
            "        wms.picking_summary ps\n" +
            "ON ps.id = \n" +
            "(\n" +
            "  SELECT  id\n" +
            "        FROM    wms.picking_summary pss\n" +
            "        WHERE  pss.id=pd.picking_summary_id\n" +
            ")  "+
            "\twhere oih.history_id>= (:historyId)" +
            "and oih.status IN ('CREATED','PICKED','DISPATCHED','PENDING_CUSTOMIZATION','READY_TO_SHIP','CUSTOMIZATION_COMPLETE','IN_PICKING','PACKED','DELIVERED')" +
            "and oih.facility_code IN (:analyticsFacilities)" +
            "LIMIT 500",nativeQuery = true)
    List<Map<String, Object>> findAllFromHistoryId(@Param("historyId") long l,@Param("analyticsFacilities") List<String> analyticsFacilities);

    @Query(value = "SELECT  oih.history_id,oih.barcode, oih.order_item_id, oih.payment_method, oih.wms_order_code, oih.product_id, oih.hold, oih.fulfilled_type,\n" +
            "oih.status as order_item_status, oih.updated_at as order_item_updated_at, oih.fitting_type, oh.increment_id, oh.created_at as order_created_at, oh.payment_status, oih.shipping_package_id, sh.status as shipment_status\n" +
            ",sh.created_at as shipment_created_at, sh.updated_at as shipment_updated_at, pd.picking_summary_id, ps.created_at as picking_summary_created_at\n" +
            "FROM    wms.order_items_history oih\n" +
            "INNER JOIN\n" +
            "        wms.orders_history oh\n" +
            "ON      oh.history_id = \n" +
            "        (\n" +
            "        SELECT  history_id\n" +
            "        FROM    wms.orders_history ohh\n" +
            "        WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                ohh.action_time DESC\n" +
            "        LIMIT 1\n" +
            "        )\n" +
            "INNER JOIN\n" +
            "        wms.shipment_history sh\n" +
            "ON      sh.history_id = \n" +
            "        (\n" +
            "        SELECT  history_id\n" +
            "        FROM    wms.shipment_history shh\n" +
            "        WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                shh.action_time DESC\n" +
            "        LIMIT 1\n" +
            "        ) \n" +
            "LEFT JOIN \n" +
            "        wms.picking_detail pd\n" +
            "ON pd.id = \n" +
            "(\n" +
            "  SELECT  id\n" +
            "        FROM    wms.picking_detail pdd\n" +
            "        WHERE  pdd.wms_order_item_id=oih.id and pdd.updated_at<=oih.action_time\n" +
            "        ORDER BY\n" +
            "                pdd.updated_at DESC\n" +
            "        LIMIT 1\n" +
            ")        \n" +
            "LEFT JOIN \n" +
            "        wms.picking_summary ps\n" +
            "ON ps.id = \n" +
            "(\n" +
            "  SELECT  id\n" +
            "        FROM    wms.picking_summary pss\n" +
            "        WHERE  pss.id=pd.picking_summary_id\n" +
            ")  "+
            "\twhere oih.action_time >= NOW() - INTERVAL 15 MINUTE" +
            "and oih.status IN ('CREATED','PICKED','DISPATCHED','PENDING_CUSTOMIZATION','READY_TO_SHIP','CUSTOMIZATION_COMPLETE','IN_PICKING','PACKED','DELIVERED')" +
            "and oih.facility_code IN (:analyticsFacilities)" +
            "LIMIT 500", nativeQuery = true)
    List<Map<String,Object>> findAllInPastFewMinutes(@Param("analyticsFacilities") List<String> analyticsFacilities);

    @Query(value = "SELECT  oih.history_id, oih.processing_type, oih.fitting_id,oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih.created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as `paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa.state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh.ship_to_store_required,ship.cis_invoice_no as `invoiceNumber`, oih.shipping_package_id, os.id as sensieItemId FROM wms.order_items_history oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh.action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship.shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id LEFT JOIN `order_sensei`.`order_items` os on os.uw_item_id = oih.`order_item_id` where oih.action_time >= NOW() - INTERVAL 15 MINUTE and oih.status ='DISPATCHED' and oih.`barcode` is not null LIMIT 10000"
            ,nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataInPastFewMinutes();

    @Query(value = "SELECT  oih.history_id, oih.processing_type, oih.fitting_id,oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih.created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as `paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa.state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh.ship_to_store_required,ship.cis_invoice_no as `invoiceNumber`, oih.shipping_package_id FROM wms.order_items_history_old oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh.action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship.shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id where oih.action_time >= NOW() - INTERVAL 15 MINUTE and oih.status ='DISPATCHED' LIMIT 10000"
            ,nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataInPastFewMinutesOld();


    @Query(value = "SELECT  oih.history_id,oih.processing_type, oih.fitting_id, oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih" +
            ".created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as " +
            "`paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as " +
            "shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa" +
            ".state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh" +
            ".ship_to_store_required, ship.cis_invoice_no as `invoiceNumber`, oih.shipping_package_id, os.id as sensieItemId FROM wms" +
            ".order_items_history oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id FROM " +
            "wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh" +
            ".action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  " +
            "order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih" +
            ".order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = " +
            "oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id" +
            " FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = " +
            "'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms" +
            ".shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh" +
            ".shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time ORDER BY shh" +
            ".action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship.shipping_package_id = oih" +
            ".shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id" +
            " LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id LEFT JOIN `order_sensei`.`order_items` os on os.uw_item_id = oih.`order_item_id` where oih.history_id >= " +
            "(:historyId) and oih.status in :statusList and oih.`barcode` is not null LIMIT :queryLimit", nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataFromHistoryId(@Param("historyId") long l,
                                                           @Param("queryLimit") int queryLimit,@Param("statusList") List<String> statusList);

    @Query(value = "SELECT  oih.history_id,oih.processing_type, oih.fitting_id, oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih" +
            ".created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as " +
            "`paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as " +
            "shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa" +
            ".state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh" +
            ".ship_to_store_required, ship.cis_invoice_no as `invoiceNumber`, oih.shipping_package_id, os.id as sensieItemId FROM wms" +
            ".order_items_history_old oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id " +
            "FROM wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER " +
            "BY ohh.action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  " +
            "order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih" +
            ".order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = " +
            "oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id" +
            " FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = " +
            "'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms" +
            ".shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh" +
            ".shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time ORDER BY shh" +
            ".action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship.shipping_package_id = oih" +
            ".shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id" +
            " LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id LEFT JOIN `order_sensei`.`order_items` os on os.uw_item_id = oih.`order_item_id where oih.history_id >= " +
            "(:historyId) and oih.status in :statusList LIMIT :queryLimit", nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataFromHistoryIdOld(@Param("historyId") long l,
                                                              @Param("queryLimit") int queryLimit,@Param("statusList") List<String> statusList);

    @Query(value = "SELECT  oih.history_id, oih.processing_type, oih.fitting_id,oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih" +
            ".created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as " +
            "`paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as " +
            "shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa" +
            ".state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh" +
            ".ship_to_store_required, ship.cis_invoice_no as `invoiceNumber` FROM wms.order_items_history oih INNER JOIN wms" +
            ".orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih" +
            ".nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh.action_time DESC LIMIT 1) INNER " +
            "JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms" +
            ".order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa" +
            ".address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN " +
            "wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE" +
            " oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd" +
            ".pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  " +
            "history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id and shh" +
            ".action_time<=oih.action_time ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship" +
            ".shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih" +
            ".shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd" +
            ".shipment_id = cs.shipment_id where oih.history_id >= (:startHistoryId) and oih.history_id <= (:endHistoryId) and omd.nexs_order_id IN :nexsOrderIds  and oih.status " +
            "='DISPATCHED'"
            , nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataByNexsOrderId(@Param("startHistoryId") long startHistoryId,
                                                           @Param("endHistoryId") long endHistoryId, @Param("nexsOrderIds") List<Integer> nexsOrderIds);

    @Query(value = "SELECT  oih.history_id,oih.updated_at as `order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih.created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as `paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa.state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh.ship_to_store_required,inv.id as `invoiceNumber` FROM wms.order_items_history oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh.action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.invoice inv ON inv.shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id where oih.history_id > (:historyId) and oih.status IN ('DISPATCHED') and oih.action_time <= '2022-11-26 00:00:00' and oih.facility_code IN ('NXS1') LIMIT 10000",nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataFromHistoryIdforNxs1(@Param("historyId") long l);

    @Query(value = "SELECT  oih.history_id, oih.processing_type, oih.fitting_id, oih.facility_code, oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih" +
            ".created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as " +
            "`paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as " +
            "shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa" +
            ".state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh" +
            ".ship_to_store_required,ship.cis_invoice_no as `invoiceNumber`, oi.item_id, oh.source, oih.shipping_package_id, os.id as sensieItemId FROM wms.order_items_history oih INNER JOIN wms" +
            ".orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih" +
            ".nexs_order_id=ohh.id ORDER BY ohh.action_time DESC LIMIT 1) INNER " +
            "JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms" +
            ".order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa" +
            ".address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN " +
            "wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE" +
            " oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd" +
            ".pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  " +
            "history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id " +
            "ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship" +
            ".shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih" +
            ".shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd" +
            ".shipment_id = cs.shipment_id INNER JOIN wms.order_items oi on oi.order_item_id = oih.order_item_id LEFT JOIN `order_sensei`.`order_items` os on os.uw_item_id = oih.`order_item_id`" +
            "where oih.history_id >= (:historyId) and oih.shipping_package_id = :shippingPackageId and " +
            " oih.status in :statusList  group by oi.id ", nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataByShippingPackageId(@Param("shippingPackageId") String shippingPackageId,
                                                                 @Param("historyId") long historyId, @Param("statusList") List<String> statusList);

    @Query(value = "SELECT  oih.history_id, oih.processing_type, oih.fitting_id, oih.facility_code, oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih" +
            ".created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as " +
            "`paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as " +
            "shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa" +
            ".state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh" +
            ".ship_to_store_required,ship.cis_invoice_no as `invoiceNumber`, oi.item_id, oh.source, oih.shipping_package_id FROM wms.order_items_history_old oih INNER JOIN wms" +
            ".orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih" +
            ".nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh.action_time DESC LIMIT 1) INNER " +
            "JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms" +
            ".order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa" +
            ".address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN " +
            "wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE" +
            " oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd" +
            ".pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  " +
            "history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id and shh" +
            ".action_time<=oih.action_time ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship" +
            ".shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih" +
            ".shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd" +
            ".shipment_id = cs.shipment_id INNER JOIN wms.order_items oi on oi.order_item_id = oih.order_item_id " +
            "where oih.history_id >= (:historyId) and oih.shipping_package_id = :shippingPackageId and " +
            " oih.status in :statusList  group by oi.id ", nativeQuery = true)
    List<Map<String, Object>> findAllLcrdDataByShippingPackageIdOld(@Param("shippingPackageId") String shippingPackageId,
                                                                 @Param("historyId") long historyId, @Param("statusList") List<String> statusList);

    @Query(value = "SELECT MIN(`history_id`) AS min_history_id FROM order_items_history WHERE `shipping_package_id` = :shippingPackageId", nativeQuery = true)
    Long findMinimumHistoryIdForShippingPackage(@Param("shippingPackageId") String shippingPackageId);

    @Query(value = "SELECT MIN(`history_id`) AS min_history_id FROM order_items_history_old WHERE `shipping_package_id` = :shippingPackageId", nativeQuery = true)
    Long findMinimumHistoryIdForShippingPackageOld(@Param("shippingPackageId") String shippingPackageId);

    @Query(value = "SELECT updated_at FROM order_items_history WHERE order_item_id = :orderItemId AND status in :statusList ORDER BY updated_at DESC LIMIT 1", nativeQuery = true)
    Date findLatestUpdatedAtByOrderItemIdAndStatus(@Param("orderItemId") Integer orderItemId, @Param("statusList") List<String> statusList);

    @Query(value = "SELECT  oih.history_id,oih.processing_type, oih.fitting_id, oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih.created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as `paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa.state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh.ship_to_store_required, ship.cis_invoice_no as `invoiceNumber`, oih.shipping_package_id FROM wms.order_items_history oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id and ohh.action_time<=oih.action_time ORDER BY ohh.action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id and shh.action_time<=oih.action_time ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship.shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id where oih.history_id >= (:historyId) and oih.shipping_package_id = :shippingPackageId and oih.status IN ('DISPATCHED')",nativeQuery = true)
    List<Map<String, Object>> findLcrdDataFromHistoryIdAndShippingPackageId(@Param("shippingPackageId") String shippingPackageId,
                                                                            @Param("historyId") long historyId);

    @Query(value="SELECT o.order_item_id as orderItemId, o.shipping_package_id as shippingPackageId " +
            "FROM order_items_history o " +
            "WHERE o.status = 'DISPATCHED' " +
            "AND o.updated_at >= :minutesAgoDate",nativeQuery = true)
    List<Tuple> findDispatchedOrderItems(@Param("minutesAgoDate") Date minutesAgoDate);


    @Query(value="select product_delivery_type from order_items_history where history_id=:historyId limit 1", nativeQuery = true)
    String getProductDeliveryTypeByHistoryId(@Param("historyId") Long historyId);

    @Query(value = "SELECT  oih.history_id, oih.processing_type, oih.fitting_id,oih.updated_at as " +
            "`order_item_updated_at`,oh.increment_id,oh.customer_id,oh.currency, oih.product_id,oih.barcode,oih.created_at as order_item_created_at, oih.payment_method as `paymentGateway`,oih.payment_method as `paymentSource`, oih.wms_order_code, oih.`channel`,oih.nav_channel, oih.order_item_id, sh.`status` as shipment_status, sh.manifest_id, oa.`name`, oa.address_line1, oa.address_line2,oa.city,oa.pincode,oa.state,oa.country,oa.Email,csd.awb_number,csd.courier_code,omd.value as `dualCompanyFlag` , oihh.ship_to_store_required,ship.cis_invoice_no as `invoiceNumber`, oih.shipping_package_id, os.id as sensieItemId FROM wms.order_items_history oih INNER JOIN wms.orders_history oh ON oh.history_id = (SELECT  history_id FROM wms.orders_history ohh WHERE  oih.nexs_order_id=ohh.id ORDER BY ohh.action_time DESC LIMIT 1) INNER JOIN wms.order_address oa ON oa.order_item_header_id = (SELECT  order_item_header_id FROM wms.order_address oahh WHERE  oahh.order_item_header_id=oih.order_item_header_id LIMIT 1) and oa.address_type=1 INNER JOIN wms.order_item_header oihh ON oihh.id = oih.order_item_header_id INNER JOIN wms.order_meta_data omd ON omd.nexs_order_id = (SELECT nexs_order_id FROM wms.order_meta_data omdh WHERE oih.nexs_order_id = omdh.nexs_order_id and pair_key = 'IS_DUAL_COMPANY_ENABLED' LIMIT 1) AND omd.pair_key = 'IS_DUAL_COMPANY_ENABLED' INNER JOIN wms.shipment_history sh ON sh.history_id = (SELECT  history_id FROM wms.shipment_history shh WHERE  shh.shipping_package_id=oih.shipping_package_id ORDER BY shh.action_time DESC LIMIT 1) INNER JOIN wms.shipment ship ON ship.shipping_package_id = oih.shipping_package_id LEFT JOIN wms.courier_shipment cs on oih.shipping_package_id=cs.shipping_package_id LEFT JOIN wms.courier_shipment_details csd ON csd.shipment_id = cs.shipment_id LEFT JOIN `order_sensei`.`order_items` os on os.uw_item_id = oih.`order_item_id` where oih.`order_item_id` in :orderItemIds and oih.status in ('DISPATCHED','RETURNED') AND oih.`barcode` is not null"
            ,nativeQuery = true)
    List<Map<String, Object>> findAllLcrdsByOrderItems(@Param("orderItemIds") List<Integer> orderItemIds);

}
