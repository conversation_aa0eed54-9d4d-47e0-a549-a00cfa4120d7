package com.lenskart.nexs.analytics.repository.manifest;

import com.lenskart.nexs.analytics.entity.manifest.ChannelCourierMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ChannelCourierMappingRepository extends JpaRepository<ChannelCourierMapping, Integer> {

    @Query(value = "SELECT shipping_provider from channel_couriers_mapping where shipping_provider_code = ? and channel = ? order by id desc limit 1", nativeQuery = true)
    String getCourierName(String shippingProviderCode, String channel);
}
