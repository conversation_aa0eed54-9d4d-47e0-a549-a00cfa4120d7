package com.lenskart.nexs.analytics.repository.inventory;

import com.lenskart.core.model.ItemWisePriceDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ItemWisePriceRepository extends JpaRepository<ItemWisePriceDetails, Long> {
    ItemWisePriceDetails findByItemId(Integer itemId);
}
