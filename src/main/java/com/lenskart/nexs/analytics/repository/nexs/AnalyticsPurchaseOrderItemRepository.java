package com.lenskart.nexs.analytics.repository.nexs;

import com.lenskart.nexs.common.entity.po.PurchaseOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface AnalyticsPurchaseOrderItemRepository extends JpaRepository<PurchaseOrderItem, Long> {

    @Query(value = "SELECT * from purchase_order_item where po_num=? and product_id=?", nativeQuery = true)
    PurchaseOrderItem getUnitPriceWithTaxes(String poNum, String pid);
}
