package com.lenskart.nexs.analytics.repository.nexs;

import com.lenskart.nexs.analytics.entity.nexs.GRNItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface GrnItemsRepository extends JpaRepository<GRNItem, String> {

    GRNItem findByBarcode(String barcode);

    GRNItem findByBarcodeAndFacility(String barcode, String facility);
}
