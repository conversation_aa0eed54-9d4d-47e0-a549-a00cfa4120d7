package com.lenskart.nexs.analytics.repository.unireports;

import com.lenskart.nexs.analytics.entity.unireports.ShippingPackageTimeline;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShippingPackageTimelineRepository extends JpaRepository<ShippingPackageTimeline, Long> {

    @Query(value = "select * from nexs_shipping_package_timeline where id< ?1 order by id desc limit 1000 ", nativeQuery = true)
    List<ShippingPackageTimeline> findAllByIdAndOrderByDesc(long lastId);

    ShippingPackageTimeline findByOrderItemIdAndShippingPackageStatusAndOrderItemUpdatedAt(String orderItemId, String shippingPackageStatus, String orderItemUpdatedAt);
}
