package com.lenskart.nexs.analytics.repository.wms;

import com.lenskart.nexs.wms.entities.base.EntitiesRepository;
import com.lenskart.nexs.wms.entities.order.Orders;
import org.springframework.stereotype.Repository;

@Repository
public interface OrderRepository extends EntitiesRepository<Orders, Long> {

    Orders findById(long id);
    Orders findByCountryCode(String id);

    Orders findByIncrementId(int incrementId);


}
