package com.lenskart.nexs.analytics.repository.unireports;

import com.lenskart.nexs.analytics.entity.unireports.LenskartCompleteReportDaily;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LenskartCompleteReportDailyRepository extends JpaRepository<LenskartCompleteReportDaily, Long> {


    LenskartCompleteReportDaily findByUwItemIdAndUnicomShipmentStatus(String uwItemId, String status);

    @Query(value = "select * from nexs_lenskart_complete_report_daily t where `Invoice No.` in (?1) and `Invoice Date` is null   ",nativeQuery = true)
    List<LenskartCompleteReportDaily> findAllByInVoiceNumber(List<String> invoiceIds);

    @Query(value = "select * from nexs_lenskart_complete_report_daily where id< ?1 order by id desc limit 1000 ", nativeQuery = true)
    List<LenskartCompleteReportDaily> findAllByIdAndOrderByDesc(long lastId);

    @Query(value = "SELECT * FROM nexs_lenskart_complete_report_daily t where  `Order Item Updated At` BETWEEN ?1 and ?2   ",nativeQuery = true)
    List<LenskartCompleteReportDaily> findByUpdatedAtStartAndEndDate(Date startDate, Date endDate, Pageable pageable);
}
