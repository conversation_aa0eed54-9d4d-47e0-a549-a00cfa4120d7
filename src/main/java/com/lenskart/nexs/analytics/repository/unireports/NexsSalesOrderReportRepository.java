package com.lenskart.nexs.analytics.repository.unireports;

import com.lenskart.nexs.analytics.entity.unireports.NexsSalesOrderReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface NexsSalesOrderReportRepository extends JpaRepository<NexsSalesOrderReport, Long> {
    @Query(value = "SELECT * FROM nexs_sales_order_report WHERE `Sale Order Item Code` = :uwItemId", nativeQuery = true)
    NexsSalesOrderReport findByUwItemId(@Param("uwItemId") String uwItemId);

    @Query(value = "SELECT n.`Sale Order Item Code` " +
            "FROM nexs_sales_order_report n " +
            "WHERE n.`Order Item Updated At` >= :minutesAgoDate", nativeQuery = true)
    List<String> findRecentSaleOrderItemCodes(@Param("minutesAgoDate") Date minutesAgoDate);
}
