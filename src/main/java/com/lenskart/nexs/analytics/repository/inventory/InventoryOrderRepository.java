package com.lenskart.nexs.analytics.repository.inventory;

import com.lenskart.core.model.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InventoryOrderRepository extends JpaRepository<Order, Integer> {
     List<Order> findByIncrementIdAndProductId(int incrementId, int productId);
}
