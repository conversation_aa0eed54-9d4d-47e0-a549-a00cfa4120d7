package com.lenskart.nexs.analytics.repository.unireports;

import com.lenskart.nexs.analytics.entity.unireports.SptSyncStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SptSyncStatusRepository extends JpaRepository<SptSyncStatus, Long> {

    @Query(value = "select * from nexs_spt_sync_status where status=?", nativeQuery = true)
    SptSyncStatus findByStatus(String status);

    @Query(value = "select * from nexs_spt_sync_status where status=? and history_id=?", nativeQuery = true)
    SptSyncStatus findByStatusAndHistoryId(String status, Long historyId);

    @Query(value = "select * from nexs_spt_sync_status where failure_count > 1 order by history_id desc LIMIT 1", nativeQuery = true)
    SptSyncStatus findHighestHistoryIdFailureRecordWithFailureCountGreaterThan1();

}
