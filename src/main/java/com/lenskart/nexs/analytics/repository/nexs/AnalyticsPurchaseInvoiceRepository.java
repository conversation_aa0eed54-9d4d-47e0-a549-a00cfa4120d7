package com.lenskart.nexs.analytics.repository.nexs;

import com.lenskart.nexs.common.entity.po.invoice.PurchaseInvoiceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface AnalyticsPurchaseInvoiceRepository extends JpaRepository<PurchaseInvoiceEntity, Integer> {

    @Query(value = "SELECT vendor_invoice_number from purchase_invoice where invoice_ref_number=?", nativeQuery = true)
    String fetchInvoiceNumber(String invoiceRefNumber);
}
