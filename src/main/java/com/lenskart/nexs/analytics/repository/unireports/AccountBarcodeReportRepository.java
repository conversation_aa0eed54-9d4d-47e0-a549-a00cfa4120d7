package com.lenskart.nexs.analytics.repository.unireports;

import com.lenskart.nexs.analytics.entity.unireports.AccountBarcodeReport;
import com.lenskart.nexs.analytics.entity.unireports.LenskartCompleteReportDaily;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AccountBarcodeReportRepository extends JpaRepository<AccountBarcodeReport, Long> {

    @Query(value = "select * from nexs_account_barcode_report where id< ?1 order by id desc limit 1000 ", nativeQuery = true)
    List<AccountBarcodeReport> findAllByIdAndOrderByDesc(long lastId);

    @Query(value = "select Shelf_Code from nexs_account_barcode_report where Item_Code= ?1 and Item_Updated_On<= ?2 limit 1", nativeQuery = true)
    String findByItemUpdatedOnLessThanOrEqualToGivenUpdatedAt(String barcode, String updatedAt);
    List<AccountBarcodeReport> findByBarcode(String barcode);

}
