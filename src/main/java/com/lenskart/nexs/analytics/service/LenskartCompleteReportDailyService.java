package com.lenskart.nexs.analytics.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.HubMaster;
import com.lenskart.core.model.ItemWisePriceDetails;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.OrderItemGSTDetail;
import com.lenskart.core.model.OrdersHeader;
import com.lenskart.fds.dto.DocumentDetailsDto;
import com.lenskart.fds.dto.DocumentItemDetailsDto;
import com.lenskart.nexs.analytics.connector.CatalogOpsConnector;
import com.lenskart.nexs.analytics.connector.FMSConnector;
import com.lenskart.nexs.analytics.connector.FinanceConnector;
import com.lenskart.nexs.analytics.connector.GrnConnector;
import com.lenskart.nexs.analytics.connector.ManifestConnector;
import com.lenskart.nexs.analytics.connector.OmsConnector;
import com.lenskart.nexs.analytics.connector.OrderOpsConnector;
import com.lenskart.nexs.analytics.connector.PSClientConnector;
import com.lenskart.nexs.analytics.constants.LCRDConstants;
import com.lenskart.nexs.analytics.dao.CacheDAO;
import com.lenskart.nexs.analytics.dao.FMSResponse;
import com.lenskart.nexs.analytics.dao.GrnPriceRequest;
import com.lenskart.nexs.analytics.dao.GrnPriceResponse;
import com.lenskart.nexs.analytics.entity.manifest.Manifest;
import com.lenskart.nexs.analytics.entity.nexs.GRNItem;
import com.lenskart.nexs.analytics.entity.unireports.AccountBarcodeReport;
import com.lenskart.nexs.analytics.entity.unireports.LcrdSyncStatus;
import com.lenskart.nexs.analytics.entity.unireports.LenskartCompleteReportDaily;
import com.lenskart.nexs.analytics.entity.unireports.NexsSalesOrderReport;
import com.lenskart.nexs.analytics.entity.wms.Invoice;
import com.lenskart.nexs.analytics.entity.wms.InvoiceItem;
import com.lenskart.nexs.analytics.mapper.LCRDMapper;
import com.lenskart.nexs.analytics.model.DispatchedOrderItemDTO;
import com.lenskart.nexs.analytics.model.LCRDWmsModel;
import com.lenskart.nexs.analytics.model.SalesOrderWmsModel;
import com.lenskart.nexs.analytics.monitoring.Constants;
import com.lenskart.nexs.analytics.monitoring.request.InvoiceDateUpdateRequest;
import com.lenskart.nexs.analytics.repository.inventory.HubMasterRepository;
import com.lenskart.nexs.analytics.repository.inventory.InventoryOrderRepository;
import com.lenskart.nexs.analytics.repository.inventory.ItemWisePriceRepository;
import com.lenskart.nexs.analytics.repository.inventory.OrderHeaderRepository;
import com.lenskart.nexs.analytics.repository.inventory.OrderItemGSTDetailRepository;
import com.lenskart.nexs.analytics.repository.inventory.ProductRepository;
import com.lenskart.nexs.analytics.repository.manifest.ChannelCourierMappingRepository;
import com.lenskart.nexs.analytics.repository.manifest.ManifestRepository;
import com.lenskart.nexs.analytics.repository.nexs.AnalyticsPurchaseInvoiceRepository;
import com.lenskart.nexs.analytics.repository.nexs.AnalyticsPurchaseOrderItemRepository;
import com.lenskart.nexs.analytics.repository.nexs.AnalyticsPurchaseOrderRepository;
import com.lenskart.nexs.analytics.repository.nexs.GrnItemsRepository;
import com.lenskart.nexs.analytics.repository.unireports.AccountBarcodeReportRepository;
import com.lenskart.nexs.analytics.repository.unireports.LcrdSyncStatusRepository;
import com.lenskart.nexs.analytics.repository.unireports.LenskartCompleteReportDailyRepository;
import com.lenskart.nexs.analytics.repository.unireports.NexsSalesOrderReportRepository;
import com.lenskart.nexs.analytics.repository.wms.InvoiceItemRepository;
import com.lenskart.nexs.analytics.repository.wms.InvoiceRepository;
import com.lenskart.nexs.analytics.repository.wms.OrderItemHistoryRepository;
import com.lenskart.nexs.analytics.repository.wms.OrderRepository;
import com.lenskart.nexs.analytics.repository.wms.ShipmentDetailRepository;
import com.lenskart.nexs.analytics.util.LCRDUtil;
import com.lenskart.nexs.common.entity.po.PurchaseOrder;
import com.lenskart.nexs.common.entity.po.PurchaseOrderItem;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.wms.entities.order.Orders;
import com.lenskart.nexs.wms.entities.order.ShipmentDetails;
import com.lenskart.nexs.wms.enums.ProcessingType;
import com.lenskart.nexs.wms.response.OmsOrderItemDetailsResponse;
import com.lenskart.nexs.wms.response.OrderItemStatusResponse;
import com.lenskart.nexs.wms.response.OrderStatusUpdateResponse;
import com.lenskart.nexs.wms.response.order.ShipmentResponse;
import com.lenskart.nexs.wms.response.orderops.FranchiseMaster;
import com.lenskart.nexs.wms.response.orderops.FranchiseOrder;
import com.lenskart.nexs.wms.response.qc.Product;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemPricesDto;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service
@Slf4j
public class LenskartCompleteReportDailyService {

    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final String GSTIN_REGEX = "\\b\\d{2}[A-Z]{5}\\d{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[A-Z0-9]{1}\\b";
    private static final Pattern GSTIN_PATTERN = Pattern.compile(GSTIN_REGEX);
    @Autowired
    CatalogOpsConnector catalogOpsConnector;
    @Autowired
    FinanceConnector financeConnector;
    @Autowired
    OrderOpsConnector orderOpsConnector;
    @Autowired
    ManifestConnector manifestConnector;
    @Autowired
    OrderItemHistoryRepository orderItemHistoryRepository;
    @Autowired
    LcrdSyncStatusRepository lcrdSyncStatusRepository;
    @Autowired
    ChannelCourierMappingRepository channelCourierMappingRepository;
    @Autowired
    LenskartCompleteReportDailyRepository lenskartCompleteReportDailyRepository;
    @Autowired
    InvoiceRepository invoiceRepository;
    @Autowired
    InvoiceItemRepository invoiceItemRepository;
    ObjectMapper mapper = new ObjectMapper();
    @Autowired
    GrnItemsRepository grnItemRepository;
    @Autowired
    AnalyticsPurchaseInvoiceRepository analyticsPurchaseInvoiceRepository;
    @Autowired
    AnalyticsPurchaseOrderItemRepository analyticsPurchaseOrderItemRepository;
    @Autowired
    ManifestRepository manifestRepository;
    @Autowired
    AnalyticsPurchaseOrderRepository analyticsPurchaseOrderRepository;
    @Autowired
    ProductRepository productRepository;
    @Autowired
    ItemWisePriceRepository itemWisePriceRepository;
    @Autowired
    HubMasterRepository hubMasterRepository;
    @Autowired
    AccountBarcodeReportRepository accountBarcodeReportRepository;
    @Autowired
    NexsSalesOrderReportRepository nexsSalesOrderReportRepository;
    @Autowired
    OrderItemGSTDetailRepository orderItemGSTDetailRepository;
    @Autowired
    InventoryOrderRepository inventoryOrderRepository;
    @Autowired
    OrderHeaderRepository orderHeaderRepository;
    @Autowired
    FdsService fdsService;
    @Value("${nexs.analytics.facilities}")
    List<String> analyticsFacilities;
    @Value("${nexs.analytics.daily.query.report.limit}")
    int queryLimit;
    @Value("${nexs.grn.fetch.base.price.new}")
    boolean isGrnBasePriceFetchEnable;
    @Autowired
    private ShipmentDetailRepository shipmentDetailsRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private PSClientConnector psClientConnector;
    @Autowired
    private OmsConnector omsConnector;
    @Autowired
    private FMSConnector fmsConnector;
    @Autowired
    private GrnConnector grnConnector;
    @Value("${nexs.analytics.daily.report.kafka.topic}")
    private String syncTopic;
    @Value("${nexs.analytics.daily.order.sync.limit}")
    private int nexsOderLimit;
    @Value("${nexs.analytics.daily.order.invoice.date.pagesize}")
    private int invoiceDateUpdatePageSize;
    @Value("${nexs.analytics.invoice.update.date.range.allowed}")
    private int invoiceUpdateDateRangeAllowed;
    @Value("${nexs.sales.order.report.kafka.topic}")
    private String nexsAnalyticsSyncTopic;
    @Value("${nexs.analytics.sales.website.facilities}")
    private List<String> websiteFacilities;
    @Value("${nexs.analytics.sales.resync.minutesAgo}")
    private String minutesAgo;
    @Value("${nexs.analytics.sales.complete.status.list:DISPATCHED}")
    private List<String> completeStatusList;

    @Value("${nexs.analytics.realtime.sales.complete.status.list:DISPATCHED}")
    private List<String> saleOrderOnlyCompleteStatusList;

    @Value("${wms.order.item.history.old.enabled:false}")
    private Boolean wmsOrderItemHistoryOldEnabled;

    @Value("${nexs.sale.report.api.enable.duplicate.check:false}")
    boolean enableDuplicateEntryCheck;

    @Autowired
    private CacheDAO cacheDAO;

    @Autowired
    @Qualifier("kafkaProducerTemplate")
    private KafkaTemplate kafkaProducerTemplate;

    private static final String REALTIME_REPORT_ANALYTCS_CACHE_KEY = "NEXS_SALE_ORDER_REPORT";
    private static final String REALTIME_REPORT_ANALYTCS_CACHE_VALUE = "REQUEST_RECEIVED";

    @Transactional(value = "unireportsTransactionManager", rollbackFor = Exception.class)
    public void lenskartCompleteReportDailyScheduler() throws SQLException {
        LcrdSyncStatus successHistoryRecord = getSuccessHistoryRecord();
        List<Map<String, Object>> lcrdWmsDataList = new ArrayList<>();
        try {
            if (successHistoryRecord.getHistoryId() != 0) {
                lcrdWmsDataList =
                        orderItemHistoryRepository.findAllLcrdDataFromHistoryId(successHistoryRecord.getHistoryId() + 1, queryLimit,completeStatusList);

                if(wmsOrderItemHistoryOldEnabled && lcrdWmsDataList.isEmpty()) {
                    lcrdWmsDataList =
                            orderItemHistoryRepository.findAllLcrdDataFromHistoryIdOld(successHistoryRecord.getHistoryId()+1,queryLimit,completeStatusList);
                }
            } else {
                lcrdWmsDataList = orderItemHistoryRepository.findAllLcrdDataInPastFewMinutes();
                if(wmsOrderItemHistoryOldEnabled && lcrdWmsDataList.isEmpty()) {
                    lcrdWmsDataList = orderItemHistoryRepository.findAllLcrdDataInPastFewMinutesOld();
                }
            }
            //long historyId = 0;
            log.info("lcrdWmsDataList size : {}", lcrdWmsDataList.size());
            if (lcrdWmsDataList != null && lcrdWmsDataList.size() > 0)
                syncToAnalytics(successHistoryRecord, lcrdWmsDataList, successHistoryRecord.getHistoryId());
            else {
                log.info("No data in order item history for lcrd");
            }
        } catch (Exception e) {
            log.error("Exception in LenskartCompleteReportDaily scheduler", e);
        }
    }

    @Transactional(value = "unireportsTransactionManager", rollbackFor = Exception.class)
    private void syncToAnalytics(LcrdSyncStatus successHistoryRecord, List<Map<String, Object>> lcrdWmsDataList,
                                 long historyId) {
        LCRDWmsModel lcrdWmsData = new LCRDWmsModel();
        try {
            for (Map<String, Object> lcrdWmsDataMap : lcrdWmsDataList) {
                try {
                    Integer orderItemId = (Integer) lcrdWmsDataMap.getOrDefault("order_item_id", 1);
                    LenskartCompleteReportDaily lenskartCompleteReportDaily =
                            getLenskartCompleteReportDailyIfExists(String.valueOf(orderItemId), "DISPATCHED");
                    if (Objects.isNull(lenskartCompleteReportDaily)) {
                        log.info("syncItems orderItemId {} push to kafka", orderItemId);
                        lcrdWmsData = mapper.convertValue(lcrdWmsDataMap, LCRDWmsModel.class);
                        String payload = mapper.writeValueAsString(lcrdWmsData);
                        pushToAnalyticsKafka(syncTopic, payload);
                    } else {
                        log.info("syncItems orderItemId {} already exists in report", orderItemId);
                        lcrdWmsData = mapper.convertValue(lcrdWmsDataMap, LCRDWmsModel.class);   //for updating the history id for scheduler
                    }
                } catch (Exception e) {
                    log.info("Error for order lcrdWmsDataMap id - {}",lcrdWmsDataMap);
                    log.error("Exception while preparing lcrdWmsData for sync : ",e);
                }
            }
            log.info("Final lcrdWmsData:{}", lcrdWmsData);
            historyId = lcrdWmsData.getHistoryId();
        } catch (Exception e) {
            log.error("lenskartCompleteReportDailyScheduler error:", e);
            historyId = lcrdWmsData.getHistoryId() - 1;

            LcrdSyncStatus failureHistoryRecord = lcrdSyncStatusRepository.findByStatusAndHistoryId("failure"
                    , lcrdWmsData.getHistoryId());
            if (failureHistoryRecord == null) {
                failureHistoryRecord = new LcrdSyncStatus();
                failureHistoryRecord.setStatus("failure");
                failureHistoryRecord.setFailureCount(0);
            }
            failureHistoryRecord.setFailureCount(failureHistoryRecord.getFailureCount() + 1);
            failureHistoryRecord.setReason(e.getMessage());
            failureHistoryRecord.setHistoryId(lcrdWmsData.getHistoryId());
            failureHistoryRecord.setUpdatedAt(new Date());
            lcrdSyncStatusRepository.save(failureHistoryRecord);
        } finally {
            successHistoryRecord.setHistoryId(historyId);
            successHistoryRecord.setUpdatedAt(new Date());
            lcrdSyncStatusRepository.save(successHistoryRecord);
            log.info("successHistoryRecord: {}", successHistoryRecord);
        }
    }

    private void pushToAnalyticsKafka(String topic, String payload) throws JsonProcessingException {

        log.info("pushing to kafka payload :{}", payload);
        kafkaProducerTemplate.send(topic, payload);
    }

    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    @Transactional(value = "unireportsTransactionManager", rollbackFor = Exception.class)
    public void buildPayloadAndInsert(LCRDWmsModel lcrdWmsData) throws Exception {
        try {
            log.info("Fetching data for order_item_id : " + lcrdWmsData.getUwItemId());
            LenskartCompleteReportDaily lenskartCompleteReportDaily = LCRDMapper.INSTANCE.mapLCRDModel(lcrdWmsData);
            populateFieldsFromLcrdWmsData(lenskartCompleteReportDaily, lcrdWmsData);

            //populating constant fields
            populateConstantFields(lenskartCompleteReportDaily);

            //fetching product details
            Product product = catalogOpsConnector.getProductDetailsFromId(Integer.parseInt(lcrdWmsData.getPid()));
            LCRDUtil.populateProductDDetails(lenskartCompleteReportDaily, product);

            //Fetching manifest closing date
            fetchManifestDetails(lenskartCompleteReportDaily, lcrdWmsData);

            //Fetching invoice details
//            InvoiceItem invoiceItem = fetchInvoiceDetails(lcrdWmsData);
//            if (invoiceItem != null) {
//                LCRDUtil.populateGSTDetails(lenskartCompleteReportDaily, invoiceItem);
//            } else {
//                DocumentDetailsDto invoiceDetails = fetchInvoiceDetailsFromFdsdb(lcrdWmsData.getShippingPackageId());
//                populateInvoiceAndGSTDetailsFromFdsdb(lenskartCompleteReportDaily, invoiceDetails, lcrdWmsData);
//            }
// made fds check first and fall back to wms invoice item.

            DocumentDetailsDto invoiceDetails = fetchInvoiceDetailsFromFdsdb(lcrdWmsData.getShippingPackageId());
            if(invoiceDetails != null) {
                populateInvoiceAndGSTDetailsFromFdsdb(lenskartCompleteReportDaily, invoiceDetails, lcrdWmsData);
            } else {
                InvoiceItem invoiceItem = fetchInvoiceDetails(lcrdWmsData);
                LCRDUtil.populateGSTDetails(lenskartCompleteReportDaily, invoiceItem);
            }

            //populating address
            LCRDUtil.populateAddress(lenskartCompleteReportDaily, lcrdWmsData);

            //Fetching grn details
            if (lcrdWmsData.getBarcode() != null) {
                ShipmentDetails shipmentDetails =
                        shipmentDetailsRepository.findByShippingPackageId(lcrdWmsData.getShippingPackageId());
                fetchGRNDetails(lcrdWmsData.getBarcode(),shipmentDetails.getFacilityCode(), lenskartCompleteReportDaily);
            }

            //Fetching courier name details
            populateCourierName(lenskartCompleteReportDaily, lcrdWmsData);

            //updating hsn code logic
            OrderItemGSTDetail orderItemGSTDetail =
                    orderItemGSTDetailRepository.findByUwItemId(lcrdWmsData.getUwItemId());
            lenskartCompleteReportDaily.setHsn(orderItemGSTDetail != null ? orderItemGSTDetail.getHsn() : null);

            /*setting up completed date for missing manifest closing date cases */
            if (lenskartCompleteReportDaily.getManifestClosingDate() == null) {
                lenskartCompleteReportDaily.setManifestClosingDate(getmanifestDateFromOrderItemHistory(lcrdWmsData.getUwItemId(), lenskartCompleteReportDaily.getOrderCountry()));
            }

            log.info("Saving data into lenskartCompleteReportDaily table for order_item_id : " + lcrdWmsData.getUwItemId());
            try {
                LenskartCompleteReportDaily lenskartCompleteReportDailyFromDB =
                        getLenskartCompleteReportDailyIfExists(lenskartCompleteReportDaily.getUwItemId(),
                                lenskartCompleteReportDaily.getUnicomShipmentStatus());
                log.info("Existing lcrd record: {}", lenskartCompleteReportDailyFromDB);
                if (lenskartCompleteReportDailyFromDB == null) {
                    lenskartCompleteReportDailyRepository.save(lenskartCompleteReportDaily);
                    log.info("lcrd report has been saved sucessfully : {}", lenskartCompleteReportDaily);
                } else {
                    lenskartCompleteReportDaily.setId(lenskartCompleteReportDailyFromDB.getId());
                    lenskartCompleteReportDailyRepository.save(lenskartCompleteReportDaily);
                    log.info("lcrd report has been saved sucessfully : {}", lenskartCompleteReportDaily);
                }
            } catch (ConstraintViolationException e) {
                log.error("[lenskartCompleteReportDailyScheduler] ConstraintViolationException while adding new " +
                        "report: {} - {}", lenskartCompleteReportDaily, e.getMessage());
                throw e;
            }
        } catch (Exception e) {
            log.error("buildPayloadAndInsert error message:{}", e.getMessage(), e);
            throw e;
        }
    }

    private void populateInvoiceAndGSTDetailsFromFdsdb(LenskartCompleteReportDaily lenskartCompleteReportDaily,
                                                       DocumentDetailsDto invoiceDetails, LCRDWmsModel lcrdWmsData) {
        if (invoiceDetails != null) {

            //filter over the order_item_id
            log.info("populating GST details for lcrd from fdsdb: {}", lenskartCompleteReportDaily);

            List<DocumentItemDetailsDto> documentItemDetailsDtos = invoiceDetails.getDocumentItems();

//            DocumentItemDetailsDto documentItemDetails = documentItemDetailsDtos.stream()
//                    .filter(i -> lenskartCompleteReportDaily.getUwItemId().equalsIgnoreCase(String.valueOf(i.getDocumentSourceReferenceItemId())))
//                    .findFirst().orElseThrow(() -> new RuntimeException("item detail not found in fds db"));

            DocumentItemDetailsDto documentItemDetails = documentItemDetailsDtos.stream()
                    .filter(i -> lenskartCompleteReportDaily.getUwItemId().equalsIgnoreCase(String.valueOf(i.getDocumentSourceReferenceItemId())))
                    .findFirst().orElse(null);

            if(documentItemDetails == null) {
                documentItemDetails = documentItemDetailsDtos.stream()
                        .filter(i -> Long.valueOf(lcrdWmsData.getSensieItemId()).equals(i.getDocumentSourceReferenceItemId()))
                        .findFirst().orElseThrow(() -> new RuntimeException("item detail not found in fds db against sensie id"));
            }

            documentItemDetails.getDocumentItemTaxDetailsDto().stream().forEach(taxItem -> {
                switch (taxItem.getTaxType()) {
                    case CGST:
                        lenskartCompleteReportDaily.setCgst(String.valueOf(taxItem.getPercentage()));
                        lenskartCompleteReportDaily.setCgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    case SGST:
                        lenskartCompleteReportDaily.setSgst(String.valueOf(taxItem.getPercentage()));
                        lenskartCompleteReportDaily.setSgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    case IGST:
                        lenskartCompleteReportDaily.setIgst(String.valueOf(taxItem.getPercentage()));
                        lenskartCompleteReportDaily.setIgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    case UGST:
                        lenskartCompleteReportDaily.setUtgst(String.valueOf(taxItem.getPercentage()));
                        lenskartCompleteReportDaily.setUtgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    default:
                        break;
                }
            });

            lenskartCompleteReportDaily.setDiscount("0.0");
            lenskartCompleteReportDaily.setItemTotalAfterDiscount(String.valueOf(documentItemDetails.getUnitPriceWithTax()));
            lenskartCompleteReportDaily.setSalePriceWithoutTax(String.valueOf(documentItemDetails.getUnitPrice()));
            lenskartCompleteReportDaily.setInvoiceNo(invoiceDetails.getDocumentNo());

            //for OTC the data is invoice created at is complete date
            if(StringUtils.isEmpty(lenskartCompleteReportDaily.getManifestClosingDate())){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                lenskartCompleteReportDaily.setManifestClosingDate(simpleDateFormat.format(invoiceDetails.getCreatedAt()));
            }

            log.info("populated GST details for lcrd from fdsdb: {}", lenskartCompleteReportDaily);
        }
    }


    private InvoiceItem fetchInvoiceDetails(LCRDWmsModel lcrdWmsData) {
        if (lcrdWmsData.getProcessingType() != null && lcrdWmsData.getProcessingType().equals(ProcessingType.FR0.toString())) {
            //return invoiceItemRepository.fetchInvoiceDetails(lcrdWmsData.getUwItemId());
            return invoiceItemRepository.findByInvoiceNumberAndProductId(lcrdWmsData.getInvoiceNumber(),
                    Integer.parseInt(lcrdWmsData.getPid()));
        } else {
            Integer fittingId = lcrdWmsData.getFittingId();
            if(lcrdWmsData.getProcessingType().equals(ProcessingType.FR1.toString())) {
                fittingId = lcrdWmsData.getUwItemId();
            }
            return invoiceItemRepository.findByFittingIdAndProductId(fittingId, lcrdWmsData.getPid());
        }
    }

    private LenskartCompleteReportDaily getLenskartCompleteReportDailyIfExists(String uwItemId, String status) {
        return lenskartCompleteReportDailyRepository.findByUwItemIdAndUnicomShipmentStatus(String.valueOf(uwItemId),
                status);
    }

    private void populateCourierName(LenskartCompleteReportDaily lenskartCompleteReportDaily,
                                     LCRDWmsModel lcrdWmsData) {
        log.info("populating courier name for lcrd: {}", lenskartCompleteReportDaily);

        if (lcrdWmsData.getCourierCode() != null) {
            String channel = lcrdWmsData.getShipToStoreRequired().equals("1") ? "STS" : "STC";
            String courierName = channelCourierMappingRepository.getCourierName(lcrdWmsData.getCourierCode(), channel);
            lenskartCompleteReportDaily.setCourierName(courierName);
        }

        log.info("populated courier name for lcrd: {}", lenskartCompleteReportDaily);

    }

    private void populateConstantFields(LenskartCompleteReportDaily lenskartCompleteReportDaily) {
        log.info("populating constant fields for lcrd: {}", lenskartCompleteReportDaily);

        lenskartCompleteReportDaily.setIncrement("");
        lenskartCompleteReportDaily.setRewardPoint("");
        lenskartCompleteReportDaily.setUnicomSyncStatus("");
        if (lenskartCompleteReportDaily.getUnitPriceVl() == null)
            lenskartCompleteReportDaily.setUnitPriceVl("0.0");
        if (lenskartCompleteReportDaily.getBaseCostAverage() == null)
            lenskartCompleteReportDaily.setBaseCostAverage("0.0");
        lenskartCompleteReportDaily.setShippingCharges("0");
        lenskartCompleteReportDaily.setCess("0.0");
        lenskartCompleteReportDaily.setCessRate("0.0");
        lenskartCompleteReportDaily.setPrepaidAmount("0");
        lenskartCompleteReportDaily.setStoreCredit("0");
        lenskartCompleteReportDaily.setShipmentStatus("COMPLETE");
        lenskartCompleteReportDaily.setUnicomShipmentStatus("DISPATCHED");
        lenskartCompleteReportDaily.setOrderStatus("COMPLETE");
        lenskartCompleteReportDaily.setWebsite(LCRDConstants.WEBSITE);
        lenskartCompleteReportDaily.setQty("1");
        lenskartCompleteReportDaily.setTaxPercent("0");
        lenskartCompleteReportDaily.setTaxType(LCRDConstants.TAXTYPE);
        lenskartCompleteReportDaily.setTaxAmt("0");
        lenskartCompleteReportDaily.setFulfilmentChannel("Lenskart Solutions Pvt Ltd");
        lenskartCompleteReportDaily.setOrderId("");
        lenskartCompleteReportDaily.setOldOrderId("");
        lenskartCompleteReportDaily.setFastRefundReason("");
        lenskartCompleteReportDaily.setTaxCollected("0");
        lenskartCompleteReportDaily.setDkMar("0");
        lenskartCompleteReportDaily.setReturnReship("0");
        lenskartCompleteReportDaily.setStoreName("");
        lenskartCompleteReportDaily.setCourierAmount("0");
        lenskartCompleteReportDaily.setEmiCharge("0");
        lenskartCompleteReportDaily.setRefundPO("");
        lenskartCompleteReportDaily.setPrepaidDiscount("0");

        log.info("populated constant fields for lcrd: {}", lenskartCompleteReportDaily);

    }

    public LcrdSyncStatus getSuccessHistoryRecord() throws SQLException {
        log.info("[getSuccessHistoryRecord] Fetch success record from lenskart_complete_report_daily_history table");
        LcrdSyncStatus successHistoryRecord = lcrdSyncStatusRepository.findByStatus("success");
        log.info("[getSuccessHistoryRecord] successHistoryRecord : {}", successHistoryRecord);
        if (successHistoryRecord == null) {
            successHistoryRecord = new LcrdSyncStatus();
            successHistoryRecord.setStatus("success");
            successHistoryRecord.setHistoryId(0L);
            return successHistoryRecord;
        } else {
            log.info("[getSuccessHistoryRecord] Fetch highest history ID record with failure count greater than 1 " +
                    "from lenskart_complete_report_daily_history table");
            LcrdSyncStatus failureHistoryRecord =
                    lcrdSyncStatusRepository.findHighestHistoryIdFailureRecordWithFailureCountGreaterThan1();
            log.info("[getSuccessHistoryRecord] failureHistoryRecord : " + failureHistoryRecord);
            if (failureHistoryRecord == null) {
                return successHistoryRecord;
            } else if (failureHistoryRecord.getHistoryId() < successHistoryRecord.getHistoryId()) {
                return successHistoryRecord;
            } else {
                successHistoryRecord.setHistoryId(failureHistoryRecord.getHistoryId());
                return successHistoryRecord;
            }
        }
    }


    private void fetchManifestDetails(LenskartCompleteReportDaily lenskartCompleteReportDaily,
                                      LCRDWmsModel lcrdWmsModel) throws Exception {
        log.info("populating manifest closing date for lcrd: {}", lenskartCompleteReportDaily);

        if (lcrdWmsModel.getManifestNumber() != null) {
            Manifest manifest = manifestRepository.findByManifestId(lcrdWmsModel.getManifestNumber());
            log.info("Manifest: {}", manifest);
            if (manifest != null && manifest.getStatus().equals("CLOSED")) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                log.info("[fetchManifestDetails] fetching manifest details for itemID : " + lcrdWmsModel.getUwItemId());
                lenskartCompleteReportDaily.setManifestClosingDate(simpleDateFormat.format(manifest.getUpdatedAt().getTime()));
            }
        }

        log.info("populated manifest closing date for lcrd: {}", lenskartCompleteReportDaily);
    }

    private void fetchGRNDetails(String barcode, String facilityCode, LenskartCompleteReportDaily lenskartCompleteReportDaily) throws Exception {
        log.info("populating grn for lcrd: {} {} {}",barcode, facilityCode, lenskartCompleteReportDaily);

        GRNItem grnItem = grnItemRepository.findByBarcodeAndFacility(barcode,facilityCode);
        log.info(grnItem + " grn item ");
        if (Objects.nonNull(grnItem) &&  grnItem.getCreatedAt() != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            lenskartCompleteReportDaily.setDkGRNDate(dateFormat.format(grnItem.getCreatedAt()));
        }

        if (isGrnBasePriceFetchEnable) {
            GrnPriceResponse grnPriceResponse = fetchGrnBasePrice(barcode, facilityCode);
            if (!ObjectUtils.isEmpty(grnPriceResponse)) {
                lenskartCompleteReportDaily.setUnitPriceVl(String.valueOf(grnPriceResponse.getPrice()));
                lenskartCompleteReportDaily.setBaseCostAverage(String.valueOf(grnPriceResponse.getPrice()));
                log.info("[fetchGrnBasePrice] updated grn unit price for barcode : {}", barcode);
            }
        } else if ( Objects.nonNull(grnItem) && grnItem.getPoId() != null && grnItem.getPid() != null) {
            fetchPODetails(lenskartCompleteReportDaily, grnItem.getPoId(), grnItem.getPid());
        }

        log.info("populated grn for lcrd: {}", lenskartCompleteReportDaily);
    }

    private void fetchInvoiceNo(LenskartCompleteReportDaily lenskartCompleteReportDaily, String invoiceRefNum) throws Exception {
        log.info("populating invoice no for lcrd: {}", lenskartCompleteReportDaily);
        String invoiceNumber = analyticsPurchaseInvoiceRepository.fetchInvoiceNumber(invoiceRefNum);
        lenskartCompleteReportDaily.setInvoiceNo(invoiceNumber);
        log.info("populated invoice no for lcrd: {}", lenskartCompleteReportDaily);
    }


    private void fetchPODetails(LenskartCompleteReportDaily lenskartCompleteReportDaily, String poNum, String pid) throws Exception {
        log.info("[fetchPODetails] Fetching PO details from po_item table ");
        PurchaseOrderItem purchaseOrderItem = analyticsPurchaseOrderItemRepository.getUnitPriceWithTaxes(poNum, pid);
        Optional<PurchaseOrder> purchaseOrder = analyticsPurchaseOrderRepository.findById(poNum);

        if (purchaseOrder.isPresent() && purchaseOrderItem != null) {
            BigDecimal unitPriceWithTaxes = getUnitCostPrice(purchaseOrder.get(), purchaseOrderItem);
            lenskartCompleteReportDaily.setUnitPriceVl(String.valueOf(unitPriceWithTaxes));
            lenskartCompleteReportDaily.setBaseCostAverage(String.valueOf(unitPriceWithTaxes));
            log.info("[fetchPODetails] Fetched PO details from po_item table ");
        }
    }

    private GrnPriceResponse fetchGrnBasePrice(String barcode, String facilityCode) throws Exception {
        log.info("[fetchGrnBasePrice] fetching grn unit price for barcode : {} | facilityCode:{}", barcode,
                facilityCode);
        GrnPriceRequest request = new GrnPriceRequest();
        request.setBarcode_list(Arrays.asList(barcode));
        request.setFacility_code(facilityCode);
        try {
            GrnPriceResponse grnPriceResponse = grnConnector.getCostPrice(request);
            return grnPriceResponse;
        } catch (Exception ex) {
            throw ex;
        }
    }

    private BigDecimal getUnitCostPrice(PurchaseOrder purchaseOrder, PurchaseOrderItem purchaseOrderItem) {
        Double conversionRate = Double.valueOf(1);
        if (purchaseOrder.getCurrencyCovRate() != null && purchaseOrder.getCurrencyCovRate() > 0 && !purchaseOrder.getCurrency().equalsIgnoreCase(LCRDConstants.INR_CURRENCY)) {
            conversionRate = purchaseOrder.getCurrencyCovRate();
        }

        return BigDecimal.valueOf(
                (purchaseOrderItem.getUnitPriceWithTaxes() + getAdditionalUnitCost(purchaseOrderItem.getTotalAdditionalCost(), purchaseOrderItem.getQuantity()))
                        * conversionRate);
    }

    private Double getAdditionalUnitCost(Double totalAdditionalCost, int quantity) {
        if (totalAdditionalCost != null && quantity > 0) {
            return totalAdditionalCost / quantity;
        } else
            return 0.0;
    }

    private void populateFieldsFromLcrdWmsData(LenskartCompleteReportDaily lenskartCompleteReportDaily,
                                               LCRDWmsModel lcrdWmsData) {
        log.info("populating fields from wms data for lcrd: {}", lenskartCompleteReportDaily);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (lcrdWmsData.getOrderItemCreatedAt() != null) {
            lenskartCompleteReportDaily.setOrderItemCreatedAt(simpleDateFormat.format(lcrdWmsData.getOrderItemCreatedAt()));
        }

        if (lcrdWmsData.getDualCompanyFlag() != null)
            lenskartCompleteReportDaily.setDualCompanyFlag(lcrdWmsData.getDualCompanyFlag().equals("true") ? "1" : "0");

        lenskartCompleteReportDaily.setOrderItemChannel(lcrdWmsData.getChannel());

        if (lcrdWmsData.getPaymentGateway() != null)
            lenskartCompleteReportDaily.setPaymentMethod(lcrdWmsData.getPaymentGateway().equals("cashondelivery") ?
                    "COD" : "PREPAID");

        if (lcrdWmsData.getOrderItemUpdatedAt() != null) {
            lenskartCompleteReportDaily.setLatestTransaction(simpleDateFormat.format(lcrdWmsData.getOrderItemUpdatedAt()));
            lenskartCompleteReportDaily.setOrderItemUpdatedAt(simpleDateFormat.format(lcrdWmsData.getOrderItemUpdatedAt()));
        }

        lenskartCompleteReportDaily.setInvoiceNo(lcrdWmsData.getInvoiceNumber());

        log.info("populated fields from wms data for lcrd: {}", lenskartCompleteReportDaily);

    }

    public boolean syncItems(List<Integer> nexsOrderIds, int startHistoryId, int endHistoryId) {
        log.info("syncItems nexsOrderIds: {}", nexsOrderIds);
        if (nexsOrderIds.size() > nexsOderLimit) {
            log.error("order limit validation failied");
        }
        List<Map<String, Object>> lcrdWmsDataList =
                orderItemHistoryRepository.findAllLcrdDataByNexsOrderId(startHistoryId, endHistoryId, nexsOrderIds);
        try {
            log.info("syncItems lcrdWmsDataList: {}", lcrdWmsDataList);
            if (CollectionUtils.isEmpty(lcrdWmsDataList)) {
                return false;
            }
            for (Map<String, Object> lcrdWmsDataMap : lcrdWmsDataList) {
                LCRDWmsModel lcrdWmsData = new LCRDWmsModel();
                Integer orderItemId = (Integer) lcrdWmsDataMap.get("order_item_id");
                LenskartCompleteReportDaily lenskartCompleteReportDaily =
                        getLenskartCompleteReportDailyIfExists(String.valueOf(orderItemId), "DISPATCHED");
                if (Objects.isNull(lenskartCompleteReportDaily)) {
                    log.info("syncItems orderItemId {} push to kafka", orderItemId);
                    lcrdWmsData = mapper.convertValue(lcrdWmsDataMap, LCRDWmsModel.class);
                    String payload = mapper.writeValueAsString(lcrdWmsData);
                    pushToAnalyticsKafka(syncTopic, payload);
                } else {
                    log.info("syncItems orderItemId {} already exist", orderItemId);
                }
            }
        } catch (Exception ex) {
            log.error("syncItems error:{}", ex.getMessage(), ex);
            return false;
        }
        return true;
    }

    @Transactional(value = "unireportsTransactionManager", rollbackFor = Exception.class)
    public void populateInvoiceDate(InvoiceDateUpdateRequest invoiceDateUpdateRequest) throws ParseException {
        log.info("Inside populateInvoiceDate for request : {} ", invoiceDateUpdateRequest);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        formatter.setLenient(false);
        simpleDateFormat.setLenient(false);
        Pageable pageable = null;
        int pageNo = Constants.PAGE_NO;
        int pageSize = invoiceDateUpdatePageSize;

        pageable = PageRequest.of(pageNo, pageSize);
        List<Invoice> invoices = null;
        List<LenskartCompleteReportDaily> lenskartCompleteReportDailies = null;
        if (Objects.nonNull(invoiceDateUpdateRequest.getInvoiceId())) {
            populateInvoiceDateForSpecificInvoice(invoiceDateUpdateRequest, simpleDateFormat);
            return;
        }
        if (Objects.nonNull(invoiceDateUpdateRequest.getShipmentPackageId())) {
            populateInvoiceDateForSpecificShippingPackageId(invoiceDateUpdateRequest, simpleDateFormat);
            return;
        }
        Date fromDate = null;
        if (Objects.nonNull(invoiceDateUpdateRequest.getStartDate())) {
            fromDate = formatter.parse(invoiceDateUpdateRequest.getStartDate());
        }
        Date toDate = null;
        if (Objects.nonNull(invoiceDateUpdateRequest.getEndDate())) {
            toDate = formatter.parse(invoiceDateUpdateRequest.getEndDate());
        }
        validateStartAndEndDate(invoiceDateUpdateRequest, fromDate, toDate);
        Calendar c = Calendar.getInstance();
        c.setTime(toDate);
        c.add(Calendar.DATE, 1);
        toDate = c.getTime();
        while (!((lenskartCompleteReportDailies = fetchListOfDailyReportsWithDateRange(fromDate, toDate, pageable)).isEmpty())) {
            log.info("Fetched Invoice Data   for request : {} ", invoiceDateUpdateRequest);
            Map<String, Date> dateMap = new HashMap<>();
            List<String> invoiceIds = new ArrayList<>();
            for (LenskartCompleteReportDaily lenskartCompleteReportDaily : lenskartCompleteReportDailies) {
                invoiceIds.add(lenskartCompleteReportDaily.getInvoiceNo());
            }
            invoices = invoiceRepository.findAllByInVoiceNumber(invoiceIds);
            for (Invoice invoice : invoices) {
                invoiceIds.add(invoice.getId());
                dateMap.put(invoice.getId(), invoice.getCreatedAt());
            }
            for (LenskartCompleteReportDaily lenskartCompleteReportDaily : lenskartCompleteReportDailies) {
                try {
                    if (Objects.isNull(lenskartCompleteReportDaily.getInvoiceDate())) {
                        lenskartCompleteReportDaily.setInvoiceDate(simpleDateFormat.format(simpleDateFormat.parse(String.valueOf(dateMap.get(lenskartCompleteReportDaily.getInvoiceNo())))));
                    }
                } catch (Exception e) {
                    log.error("Exception occured while populating invoice date for daily report : {} ",
                            lenskartCompleteReportDaily);
                }
            }
            lenskartCompleteReportDailyRepository.saveAll(lenskartCompleteReportDailies);

            log.info("Successfully Updated Invoice Date for  request : {} ", invoiceDateUpdateRequest);
            pageNo++;
            pageable = PageRequest.of(pageNo, pageSize);
        }
        log.info("Exited populateInvoiceDate for request : {} ", invoiceDateUpdateRequest);
    }

    public boolean syncMissingItemsToDailyReport(List<String> shippingIds, int historyId) {
        log.info("syncItems nexsOrderIds: {}", shippingIds);
        if (shippingIds.size() > nexsOderLimit) {
            log.error("order limit validation failied");
        }
        for (String shippingPackageId : shippingIds) {
            log.info("fetching lcrdWmsDataList for shipment id - {}", shippingPackageId);
            List<Map<String, Object>> lcrdWmsDataList =
                    orderItemHistoryRepository.findLcrdDataFromHistoryIdAndShippingPackageId(shippingPackageId,
                            historyId);
            try {
                log.info("syncItems lcrdWmsDataList: {}", lcrdWmsDataList);
                if (CollectionUtils.isEmpty(lcrdWmsDataList)) {
                    return false;
                }
                for (Map<String, Object> lcrdWmsDataMap : lcrdWmsDataList) {
                    LCRDWmsModel lcrdWmsData;
                    Integer orderItemId = (Integer) lcrdWmsDataMap.get("order_item_id");
                    LenskartCompleteReportDaily lenskartCompleteReportDaily =
                            getLenskartCompleteReportDailyIfExists(String.valueOf(orderItemId), "DISPATCHED");
                    if (Objects.isNull(lenskartCompleteReportDaily)) {
                        log.info("syncItems orderItemId {} push to kafka", orderItemId);
                        lcrdWmsData = mapper.convertValue(lcrdWmsDataMap, LCRDWmsModel.class);
                        String payload = mapper.writeValueAsString(lcrdWmsData);
                        pushToAnalyticsKafka(syncTopic, payload);
                    } else {
                        log.info("syncMissingItemsToDailyReport orderItemId {} already exist", orderItemId);
                    }
                }
            } catch (Exception ex) {
                log.error("syncMissingItemsToDailyReport error:{}", ex.getMessage(), ex);
                return false;
            }
        }
        return true;
    }

    private List<LenskartCompleteReportDaily> fetchListOfDailyReportsWithDateRange(Date startDate, Date endDate,
                                                                                   Pageable pageable) {
        log.info("Inside  fetchListOfInvoiceWithDateRange for startDate : {} and endDate : {}  ", startDate, endDate);
        List<LenskartCompleteReportDaily> lenskartCompleteReportDailies =
                lenskartCompleteReportDailyRepository.findByUpdatedAtStartAndEndDate(startDate, endDate, pageable);
        log.info("Exiting  fetchListOfInvoiceWithDateRange for startDate : {} and endDate : {}   ", startDate, endDate);
        return lenskartCompleteReportDailies;
    }

    private void updateInvoiceDateForInvoices(Invoice invoice, SimpleDateFormat simpleDateFormat) throws ParseException {
        log.info("Inside  updateInvoiceDateForInvoices for invoice : {}  ", invoice);
        List<String> invoiceNos = new ArrayList<>();
        invoiceNos.add(invoice.getId());
        List<LenskartCompleteReportDaily> lenskartCompleteReportDailies =
                lenskartCompleteReportDailyRepository.findAllByInVoiceNumber(invoiceNos);
        for (LenskartCompleteReportDaily lenskartCompleteReportDaily : lenskartCompleteReportDailies) {
            lenskartCompleteReportDaily.setInvoiceDate(simpleDateFormat.format(simpleDateFormat.parse(String.valueOf(invoice.getCreatedAt()))));
        }
        lenskartCompleteReportDailyRepository.saveAll(lenskartCompleteReportDailies);
        log.info("Exiting  updateInvoiceDateForInvoices for invoice : {}   ", invoice);
    }


    private void validateStartAndEndDate(InvoiceDateUpdateRequest invoiceDateUpdateRequest, Date fromDate,
                                         Date toDate) {
        if (Objects.nonNull(fromDate) && Objects.nonNull(toDate)) {
            if (fromDate.after(toDate)) {
                log.error("From Date is Greater than to Date for request : {} ", invoiceDateUpdateRequest);
                throw new RuntimeException("From Date is Greater than to Date for request " + invoiceDateUpdateRequest);
            }
            long diff = toDate.getTime() - fromDate.getTime();
            long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            if (invoiceUpdateDateRangeAllowed < days) {
                log.error("This Days Range is not allowed for populateInvoiceDate for  request : {} ",
                        invoiceDateUpdateRequest);
                throw new RuntimeException("This Days Range is not allowed for populateInvoiceDate for  request " + invoiceDateUpdateRequest);
            }
        }

    }

    private void populateInvoiceDateForSpecificInvoice(InvoiceDateUpdateRequest invoiceDateUpdateRequest,
                                                       SimpleDateFormat simpleDateFormat) throws ParseException {
        Invoice invoice = invoiceRepository.findByInvoiceId(invoiceDateUpdateRequest.getInvoiceId());
        updateInvoiceDateForInvoices(invoice, simpleDateFormat);
        log.info("Successfully Updated Invoice Date for  request : {} for  InvoiceId", invoiceDateUpdateRequest);
    }

    private void populateInvoiceDateForSpecificShippingPackageId(InvoiceDateUpdateRequest invoiceDateUpdateRequest,
                                                                 SimpleDateFormat simpleDateFormat) throws ParseException {
        Invoice invoice = invoiceRepository.findByShipmentPackageId(invoiceDateUpdateRequest.getShipmentPackageId());
        updateInvoiceDateForInvoices(invoice, simpleDateFormat);
        log.info("Successfully Updated Invoice Date for  request : {} for  shippingPackageId ",
                invoiceDateUpdateRequest);
    }


    @Retryable(value = Exception.class, maxAttempts = 2, backoff = @Backoff(delay = 1000))
    @Transactional(value = "unireportsTransactionManager", rollbackFor = Exception.class)
    public void buildNexsOrderSalesReportAndInsert(OrderStatusUpdateResponse orderStatusUpdateResponse) throws Exception {
        try {
            if (!"DISPATCHED".equalsIgnoreCase(orderStatusUpdateResponse.getShipmentResponse().getStatus())) {
                log.info("order status received not in dispatched state");
                return;
            }
            log.info("building sales order from the event received for shipment - {}",
                    orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            String redisKey = REALTIME_REPORT_ANALYTCS_CACHE_KEY+orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId();
            if(cacheDAO.hasKey(redisKey)) {
                log.info("Multiple DISPATCHED request in same time interval for shipping package id : {}",orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
                return;
            }
            long historyId =
                    orderItemHistoryRepository.findMinimumHistoryIdForShippingPackage(orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());

            log.info("Hitory ID fetched as per shipment - {} for {}", historyId,
                    orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            if (historyId == 0) {
                throw new RuntimeException("Shipment not present in order item history for " + orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            }

            List<Map<String, Object>> lcrdWmsDataList =
                    orderItemHistoryRepository.findAllLcrdDataByShippingPackageId(orderStatusUpdateResponse
                            .getShipmentResponse().getShippingPackageId(), historyId, saleOrderOnlyCompleteStatusList);
            log.info("lcrdWmsDataList fetched for shippment id from order item history with map size - {} for {}",
                    lcrdWmsDataList.size(), orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());

            // Retry from old if lcrdWmsDataList is empty
            if(wmsOrderItemHistoryOldEnabled && lcrdWmsDataList.isEmpty()) {
                historyId = orderItemHistoryRepository.findMinimumHistoryIdForShippingPackageOld(orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
                if (historyId == 0) {
                    throw new RuntimeException("Shipment not present in order item history for " + orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
                }
                lcrdWmsDataList =
                        orderItemHistoryRepository.findAllLcrdDataByShippingPackageIdOld(orderStatusUpdateResponse
                                .getShipmentResponse().getShippingPackageId(), historyId, saleOrderOnlyCompleteStatusList);
                log.info("lcrdWmsDataList fetched for shippment id from order item history older with map size - {} for {}",
                        lcrdWmsDataList.size(), orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            }

            // Retry if lcrdWmsDataList is empty
            if (lcrdWmsDataList.isEmpty()) {
                throw new RuntimeException("Failed to fetch data from slave DB. Retrying...");
            }
            List<NexsSalesOrderReport> nexsSalesOrderReportList = new ArrayList<>();
            DocumentDetailsDto documentDetailsDto = fetchFdsInvoiceDetail(orderStatusUpdateResponse
                    .getShipmentResponse().getShippingPackageId());
            for (Map<String, Object> lcrdWmsDataMap : lcrdWmsDataList) {
                NexsSalesOrderReport nexsSalesOrderReport = populateSaleOrderItem(orderStatusUpdateResponse,
                        historyId, lcrdWmsDataMap,documentDetailsDto);
                if (Objects.nonNull(nexsSalesOrderReport)) {
                    nexsSalesOrderReportList.add(nexsSalesOrderReport);
                }
            }
            nexsSalesOrderReportRepository.saveAll(nexsSalesOrderReportList);
            cacheDAO.putVal(redisKey, REALTIME_REPORT_ANALYTCS_CACHE_VALUE, 60L, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("buildNexsOrderSalesReportAndInsert error message: " + e.getMessage() + " request: " + orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId(), e);
            e.printStackTrace();
            throw e;
        }
    }

    private DocumentDetailsDto fetchFdsInvoiceDetail(String shippingPackageId) {
        try {
            DocumentDetailsDto documentDetailsDto = fetchInvoiceDetailsFromFdsdb(shippingPackageId);
        } catch (Exception e) {
            log.error("getting fds document detials {}",e.getMessage(),e);
        }
        return null;
    }

    private NexsSalesOrderReport populateSaleOrderItem(OrderStatusUpdateResponse orderStatusUpdateResponse, Long historyId,
                                                       Map<String, Object> lcrdWmsDataMap,
                                                       DocumentDetailsDto documentDetailsDto) throws Exception {
        String hubCountry = null;
        log.info("lcrdWmsDataMap : {}", lcrdWmsDataMap);
        try {
            SalesOrderWmsModel salesOrderWmsModel = mapper.convertValue(lcrdWmsDataMap, SalesOrderWmsModel.class);
            NexsSalesOrderReport nexsSalesOrderReport = buildNexsOrderReportFromLcrWmsData(salesOrderWmsModel);
            String productDeliveryType = orderItemHistoryRepository.getProductDeliveryTypeByHistoryId(historyId);

            if ("OTC".equals(productDeliveryType)) {
                log.info("[buildNexsOrderSalesReportAndInsert] order {} is OTC getting facility details from " +
                        "FranchiseMaster", nexsSalesOrderReport.getIncrementId());
                FranchiseMaster franchiseMaster = getDetailsFromFranchiseMaster(nexsSalesOrderReport.getIncrementId(),
                        nexsSalesOrderReport.getNavChannel());
                populateSalesOrderReportFromFranchiseMaster(nexsSalesOrderReport, franchiseMaster);
            } else {
                HubMaster hubMaster = hubMasterRepository.findByFacilityCode(salesOrderWmsModel.getFacilityCode());
                log.info("[buildNexsOrderSalesReportAndInsert] hubmaster - {}",hubMaster);
                if(!Objects.isNull(hubMaster)) {
                    hubCountry = hubMaster.getCountry();
                    populateSalesOrderReportDataFromHubMaster(nexsSalesOrderReport, hubMaster);
                } else {
                    log.info("[buildNexsOrderSalesReportAndInsert] order {} is B2B getting facility details from " +
                            "FranchiseMaster", nexsSalesOrderReport.getIncrementId());
                    FranchiseMaster franchiseMaster = getDetailsFromFranchiseMaster(nexsSalesOrderReport.getIncrementId(),
                            nexsSalesOrderReport.getNavChannel());
                    populateSalesOrderReportFromFranchiseMaster(nexsSalesOrderReport, franchiseMaster);
                }
            }

            Manifest manifest = null;
            if (!StringUtils.isEmpty(salesOrderWmsModel.getManifestNumber())) {
                log.info("populating manifest detail for sales order Item Code  - {} | {}",
                        nexsSalesOrderReport.getUwItemId(), manifest);
                manifest = manifestRepository.findByManifestId(salesOrderWmsModel.getManifestNumber());
            }
            if (Objects.nonNull(manifest)) {
                nexsSalesOrderReport.setCourierName(manifest.getShippingProvider());
                if (Objects.isNull(nexsSalesOrderReport.getManifestClosingDate())) {
                    nexsSalesOrderReport.setManifestClosingDate(convertToLocalDate(manifest.getUpdatedAt(),
                            hubCountry));
                }
            }

            OrdersHeader ordersHeader =
                    orderHeaderRepository.findByIncrementId(Integer.parseInt(salesOrderWmsModel.getIncrementId()));
            if (ordersHeader != null) {
                nexsSalesOrderReport.setPaymentSource(ordersHeader.getPaymentMode());
                ItemWisePriceDetails priceDetails =
                        itemWisePriceRepository.findByItemId(Integer.parseInt(salesOrderWmsModel.getItemId()));
                populateItemPriceDtails(nexsSalesOrderReport, priceDetails, ordersHeader != null ?
                        ordersHeader.getPaymentCaptureFlag() : 0);
            } else {
                //order sensei flow order
                populateItemWisePriceDetailFromOms(nexsSalesOrderReport);
            }

//            InvoiceItem invoiceItem = fetchInvoiceDetails(salesOrderWmsModel);
//            if (invoiceItem != null) {
//                populateInvoiceItemDetailInSalesOrder(nexsSalesOrderReport, invoiceItem, hubCountry);
//            } else {
//                if(Objects.isNull(documentDetailsDto)) {
//                    log.info("documentDetailsDto null calling again fetchInvoiceDetailsFromFdsdb : {}",
//                            orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
//                    documentDetailsDto =
//                            fetchInvoiceDetailsFromFdsdb(salesOrderWmsModel.getShippingPackageId());
//                }
//                populateInvoiceItemDetailFromFdsdbInSalesOrderReport(nexsSalesOrderReport, documentDetailsDto,salesOrderWmsModel);
//            }

            if(Objects.isNull(documentDetailsDto)) {
                log.info("documentDetailsDto null calling again fetchInvoiceDetailsFromFdsdb : {}",
                        orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
                documentDetailsDto =
                        fetchInvoiceDetailsFromFdsdb(salesOrderWmsModel.getShippingPackageId());
            }
            if(!Objects.isNull(documentDetailsDto)) {
                populateInvoiceItemDetailFromFdsdbInSalesOrderReport(nexsSalesOrderReport, documentDetailsDto, salesOrderWmsModel);
            } else {
                InvoiceItem invoiceItem = fetchInvoiceDetails(salesOrderWmsModel);
                populateInvoiceItemDetailInSalesOrder(nexsSalesOrderReport, invoiceItem, hubCountry);
            }

            OrderItemGSTDetail orderItemGSTDetail =
                    orderItemGSTDetailRepository.findByUwItemId(salesOrderWmsModel.getUwItemId());
            populateOrderGSTDetailsForSalesReport(nexsSalesOrderReport, orderItemGSTDetail);


            com.lenskart.core.model.Product product =
                    productRepository.findByProductId(Integer.parseInt(salesOrderWmsModel.getPid()));
            populateProductDetailsInOrderReport(nexsSalesOrderReport, product);

            List<Order> order =
                    inventoryOrderRepository.findByIncrementIdAndProductId(Integer.parseInt(salesOrderWmsModel.getIncrementId()), Integer.parseInt(salesOrderWmsModel.getPid()));
            if (order != null && order.size() > 0) {
                nexsSalesOrderReport.setTaxCollected(String.valueOf(order.get(0).getTaxCollected()));
            }

            if (Objects.nonNull(salesOrderWmsModel.getBarcode())) {
                populateSalesOrderDetailFromBarcodeReport(nexsSalesOrderReport, salesOrderWmsModel.getBarcode());
            }

            ShipmentDetails shipmentDetails =
                    shipmentDetailsRepository.findByShippingPackageId(orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            if (shipmentDetails != null) {
                nexsSalesOrderReport.setFacilityCode(shipmentDetails.getFacilityCode());
            }

            log.info("coming 7 for {}", orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());

            Orders orders =
                    orderRepository.findByIncrementId(Integer.parseInt(salesOrderWmsModel.getIncrementId()));
            if (orders != null) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                nexsSalesOrderReport.setOrderItemCreatedAt(simpleDateFormat.format(orders.getOrderCreatedAt()));
            }

            updateB2BStoreName(nexsSalesOrderReport);
            //updateBillToPartyGSTIN(nexsSalesOrderReport, salesOrderWmsModel);
            updateFulFIllmentChannel(nexsSalesOrderReport);

            log.info("coming 8 for {}", orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());

            populateSalesOrderDetailCompletedDate(nexsSalesOrderReport, hubCountry);

            if (StringUtils.isEmpty(nexsSalesOrderReport.getCreatedAt())) {
                nexsSalesOrderReport.setCreatedAt(SIMPLE_DATE_FORMAT.format(new Date()));
            }
            nexsSalesOrderReport.setUpdatedAt(SIMPLE_DATE_FORMAT.format(new Date()));
            log.info("sales order report has been updated sucessfully : {} for {}", nexsSalesOrderReport,
                    orderStatusUpdateResponse.getShipmentResponse().getShippingPackageId());
            return nexsSalesOrderReport;
        } catch (Exception ex) {
            log.error("populateSaleOrderItem error {} : {}", lcrdWmsDataMap, ex.getMessage(), ex);
        }
        return null;
    }

    private void populateItemWisePriceDetailFromOms(NexsSalesOrderReport nexsSalesOrderReport) {
        OmsOrderItemDetailsResponse omsOrderItemDetailsResponse =
                omsConnector.getOrderItemDetailsByUwItemId(nexsSalesOrderReport.getUwItemId());
        OrderItemDto orderItemDto = omsOrderItemDetailsResponse.getContent().get(0); //fetching one item only
        OrderItemPricesDto orderItemPricesDto = orderItemDto.getOrderItemPrice();
        nexsSalesOrderReport.setPaymentSource(nexsSalesOrderReport.getPaymentMethod());
        log.info("populating itemwise detail for sales order Item Code  from oms - {}",
                nexsSalesOrderReport.getUwItemId());
        nexsSalesOrderReport.setShippingCharges(String.valueOf(orderItemPricesDto.getShippingCharges()));
        nexsSalesOrderReport.setStoreCredit(String.valueOf(orderItemPricesDto.getScDiscount()));
        nexsSalesOrderReport.setTaxCollected(String.valueOf(orderItemPricesDto.getTaxCollected()));
        if ("prepaid".equalsIgnoreCase(nexsSalesOrderReport.getPaymentMethod())) {
            nexsSalesOrderReport.setPrepaidAmount(String.valueOf(orderItemPricesDto.getScDiscount()));
        }

    }

    private NexsSalesOrderReport buildNexsOrderReportFromLcrWmsData(SalesOrderWmsModel salesOrderWmsModel) {
        NexsSalesOrderReport nexsSalesOrderReport = null;
        nexsSalesOrderReport =
                getNexsSalesOrderReportIfExists(String.valueOf(salesOrderWmsModel.getUwItemId()));
        if(Objects.isNull(nexsSalesOrderReport)) {
            nexsSalesOrderReport = new NexsSalesOrderReport();
        }
        nexsSalesOrderReport.setIncrementId(salesOrderWmsModel.getIncrementId());
        nexsSalesOrderReport.setCustomerId(salesOrderWmsModel.getCustomerId());
        nexsSalesOrderReport.setUwItemId(String.valueOf(salesOrderWmsModel.getUwItemId()));
        nexsSalesOrderReport.setPid(salesOrderWmsModel.getPid());
        nexsSalesOrderReport.setBarcode(salesOrderWmsModel.getBarcode());
        nexsSalesOrderReport.setQty("1");
        nexsSalesOrderReport.setPaymentMethod(salesOrderWmsModel.getPaymentSource());
        nexsSalesOrderReport.setPaymentGateway(salesOrderWmsModel.getPaymentGateway());
        nexsSalesOrderReport.setWmsOrderCode(salesOrderWmsModel.getWmsOrderCode());
        nexsSalesOrderReport.setChannel(salesOrderWmsModel.getChannel());
        nexsSalesOrderReport.setNavChannel(salesOrderWmsModel.getNavChannel());
        nexsSalesOrderReport.setCurrencyCode(salesOrderWmsModel.getCurrencyCode());
        nexsSalesOrderReport.setCourierCode(salesOrderWmsModel.getCourierCode());
        nexsSalesOrderReport.setOrderItemChannel("NA"); // NA in nexs as we don't ship marketplace orders
        //nexsSalesOrderReport.setFulfilmentChannel(Constants.WEBSITE_NAME);

        if (salesOrderWmsModel.getOrderItemUpdatedAt() != null) {
            nexsSalesOrderReport.setLatestTransaction(SIMPLE_DATE_FORMAT.format(salesOrderWmsModel.getOrderItemUpdatedAt()));
            nexsSalesOrderReport.setOrderItemUpdatedAt(SIMPLE_DATE_FORMAT.format(salesOrderWmsModel.getOrderItemUpdatedAt()));
        }

        nexsSalesOrderReport.setOrderCity(salesOrderWmsModel.getOrderCity());
        nexsSalesOrderReport.setOrderPincode(salesOrderWmsModel.getOrderPincode());
        nexsSalesOrderReport.setOrderState(salesOrderWmsModel.getOrderState());
        nexsSalesOrderReport.setOrderCountry(salesOrderWmsModel.getOrderCountry());
        nexsSalesOrderReport.setFranchiseCode(salesOrderWmsModel.getSource());
        nexsSalesOrderReport.setTrackingNumber(salesOrderWmsModel.getTrackingNumber());
        nexsSalesOrderReport.setFacilityCode(salesOrderWmsModel.getFacilityCode());

        for (String facilities : websiteFacilities) {
            if (facilities.equalsIgnoreCase(salesOrderWmsModel.getFacilityCode())) {
                nexsSalesOrderReport.setWebsite(LCRDConstants.WEBSITE);
            }
        }
        log.info("sales order creating for : {} for {} id-{}", nexsSalesOrderReport.getUwItemId(),
                salesOrderWmsModel.getShippingPackageId(),nexsSalesOrderReport.getId());
        return nexsSalesOrderReport;
    }

    private NexsSalesOrderReport getNexsSalesOrderReportIfExists(String uwItemId) {
        return nexsSalesOrderReportRepository.findByUwItemId(uwItemId);
    }

    private void populateSalesOrderReportDataFromHubMaster(NexsSalesOrderReport nexsSalesOrderReport,
                                                           HubMaster hubMaster) {
        if (hubMaster != null) {
            if (!hubMaster.getCountry().toLowerCase().equalsIgnoreCase(nexsSalesOrderReport.getOrderCountry().toLowerCase())) {
                nexsSalesOrderReport.setIsExport("yes");
                nexsSalesOrderReport.setBillFromPartyGSTIN(hubMaster.getLogisticsGstin());
            } else {
                nexsSalesOrderReport.setIsExport("No");
                nexsSalesOrderReport.setBillFromPartyGSTIN(hubMaster.getGstin());
            }
        }
    }

    private void populateProductDetailsInOrderReport(NexsSalesOrderReport nexsSalesOrderReport,
                                                     com.lenskart.core.model.Product product) {
        log.info("populated product details for sales order Item Code: {}", nexsSalesOrderReport.getUwItemId());
        if (product != null) {
            nexsSalesOrderReport.setBrand(product.getBrand());
            nexsSalesOrderReport.setProductName(product.getValue());
            nexsSalesOrderReport.setHsnClassification(product.getHsnClassification());
            nexsSalesOrderReport.setProductDetails("{hsnCode: " + product.getHsnCode() + ", prodDesc: " + product.getValue() + "}");
            if (nexsSalesOrderReport.getHsn() == null) {
                nexsSalesOrderReport.setHsn(product.getHsnCode());
            }
        }
    }

    private void populateInvoiceItemDetailInSalesOrder(NexsSalesOrderReport nexsSalesOrderReport,
                                                       InvoiceItem invoiceItem, String hubCountry) {
        if (invoiceItem != null) {
            log.info("populating invoice detail for sales order Item Code  - {}", nexsSalesOrderReport.getUwItemId());
            nexsSalesOrderReport.setInvoiceNo(invoiceItem.getInvoiceNumber().getId());
            Double totalTax = (invoiceItem.getCgstRate() + invoiceItem.getIgstRate() + invoiceItem.getSgstRate());
//            Double shippingCharge = (double) 0;
//            if (nexsSalesOrderReport.getShippingCharges() != null) {
//                shippingCharge = Double.valueOf(nexsSalesOrderReport.getShippingCharges());
//            }
            Double grandTotal = invoiceItem.getTotalPrice() / invoiceItem.getQuantity();
            nexsSalesOrderReport.setSalePriceWithoutTax(String.valueOf(grandTotal - totalTax));
            nexsSalesOrderReport.setSalePriceWithTax(String.valueOf(grandTotal));
            if (invoiceItem.getCreatedAt() != null) {
                nexsSalesOrderReport.setInvoiceCreationDate(convertToLocalDate(invoiceItem.getCreatedAt(), hubCountry));
            }
        }
    }

    private void populateOrderGSTDetailsForSalesReport(NexsSalesOrderReport nexsSalesOrderReport,
                                                       OrderItemGSTDetail orderItemGSTDetail) {
        if (orderItemGSTDetail != null) {
            log.info("populating gst hsn detail for sales order Item Code  - {}", nexsSalesOrderReport.getUwItemId());
            nexsSalesOrderReport.setCgst(String.valueOf(orderItemGSTDetail.getCgstAmount()));
            nexsSalesOrderReport.setIgst(String.valueOf(orderItemGSTDetail.getIgstAmount()));
            nexsSalesOrderReport.setSgst(String.valueOf(orderItemGSTDetail.getSgstAmount()));
            nexsSalesOrderReport.setUtgst(String.valueOf(orderItemGSTDetail.getUgstAmount()));
            nexsSalesOrderReport.setCgstRate(String.valueOf(orderItemGSTDetail.getCgstPc()));
            nexsSalesOrderReport.setIgstRate(String.valueOf(orderItemGSTDetail.getIgstPc()));
            nexsSalesOrderReport.setSgstRate(String.valueOf(orderItemGSTDetail.getSgstPc()));
            nexsSalesOrderReport.setUtgstRate(String.valueOf(orderItemGSTDetail.getUgstPc()));
            nexsSalesOrderReport.setHsn(orderItemGSTDetail.getHsn());
        }
    }

    private void populateItemPriceDtails(NexsSalesOrderReport nexsSalesOrderReport,
                                         ItemWisePriceDetails itemWisePriceDetails, int prepaidFlag) {
        if (itemWisePriceDetails != null) {
            log.info("populating itemwise detail for sales order Item Code  - {}", nexsSalesOrderReport.getUwItemId());
            nexsSalesOrderReport.setShippingCharges(String.valueOf(itemWisePriceDetails.getShippingCharges()));
            nexsSalesOrderReport.setStoreCredit(String.valueOf(itemWisePriceDetails.getStoreCreditDiscount()));
            if (prepaidFlag == 1) {
                nexsSalesOrderReport.setPrepaidAmount(String.valueOf(itemWisePriceDetails.getStoreCreditDiscount()));
            }
        }
    }


    private void populateSalesOrderDetailCompletedDate(NexsSalesOrderReport nexsSalesOrderReport,
                                                       String hubCountry) {
        if(Objects.isNull(nexsSalesOrderReport.getManifestClosingDate())) {
            log.info("populateSalesOrderDetailCompletedDate for {}",nexsSalesOrderReport.getUwItemId());
            nexsSalesOrderReport.setManifestClosingDate(getmanifestDateFromOrderItemHistory(Integer.valueOf(nexsSalesOrderReport.getUwItemId()), hubCountry));
        }
    }

    @Logging
    private String getmanifestDateFromOrderItemHistory(Integer orderItemId, String hubCountry) {
        Date manifestDate = orderItemHistoryRepository.findLatestUpdatedAtByOrderItemIdAndStatus(orderItemId, completeStatusList);
        return manifestDate != null ? convertToLocalDate(manifestDate, hubCountry) : null;
    }

    private void populateSalesOrderDetailFromBarcodeReport(NexsSalesOrderReport nexsSalesOrderReport, String barcode) throws Exception {

        log.info("populating barcode report detail for sales order Item Code  - {}",
                nexsSalesOrderReport.getUwItemId());
        if (isGrnBasePriceFetchEnable) {
            GrnPriceResponse grnPriceResponse = fetchGrnBasePrice(barcode, nexsSalesOrderReport.getFacilityCode());
            if (!ObjectUtils.isEmpty(grnPriceResponse)) {
                nexsSalesOrderReport.setSourcingCostWithOutTax(String.valueOf(grnPriceResponse.getPrice()));
            }
        } else {
            List<AccountBarcodeReport> accountBarcodeReport = accountBarcodeReportRepository.findByBarcode(barcode);
            if (!CollectionUtils.isEmpty(accountBarcodeReport)) {
                nexsSalesOrderReport.setSourcingCostWithOutTax(String.valueOf(accountBarcodeReport.get(0).getUnitPriceWithoutTax()));
            }
        }
    }

    private InvoiceItem fetchInvoiceDetails(SalesOrderWmsModel salesOrderWmsModel) {
        if (salesOrderWmsModel.getProcessingType() != null && salesOrderWmsModel.getProcessingType().equals(ProcessingType.FR0.toString())) {
            //return invoiceItemRepository.fetchInvoiceDetails(salesOrderWmsModel.getUwItemId());
            return invoiceItemRepository.findByInvoiceNumberAndProductId(salesOrderWmsModel.getInvoiceNumber(),
                    Integer.parseInt(salesOrderWmsModel.getPid()));
        } else {
            return invoiceItemRepository.findByFittingIdAndProductId(salesOrderWmsModel.getFittingId(),
                    salesOrderWmsModel.getPid());
        }
    }

    public void syncForSalesOrderReport(List<String> shippingIds) {
        log.info("Pushing to shipping ids nexs sync order report topic: {}",shippingIds);
        try {
            for (String shippingPackageId : shippingIds) {
                OrderStatusUpdateResponse orderStatusUpdateResponse = new OrderStatusUpdateResponse();
                ShipmentResponse shipmentResponse = new ShipmentResponse();
                shipmentResponse.setShippingPackageId(shippingPackageId);
                shipmentResponse.setStatus("DISPATCHED");
                orderStatusUpdateResponse.setShipmentResponse(shipmentResponse);

                OrderItemStatusResponse item = new OrderItemStatusResponse();
                item.setShippingPackageId(shippingPackageId);
                item.setStatus("DISPATCHED");
                List<OrderItemStatusResponse> list = new ArrayList<>();
                list.add(item);
                orderStatusUpdateResponse.setOrderItemStatusResponses(list);

                String payload = mapper.writeValueAsString(orderStatusUpdateResponse);

                pushToAnalyticsKafka(nexsAnalyticsSyncTopic, payload);
            }
            log.info("Successfully produced {} items to sync order report topic", shippingIds.size());
        } catch (Exception e) {
            log.error("Exception occured while producing item for nexs sales order sync topic");
            throw new RuntimeException(e);
        }
    }

    private DocumentDetailsDto fetchInvoiceDetailsFromFdsdb(String shippingPackageId) throws Exception {
        return fdsService.getDocumentDetails(shippingPackageId);
    }

    private void populateInvoiceItemDetailFromFdsdbInSalesOrderReport(NexsSalesOrderReport nexsSalesOrderReport,
                                                                      DocumentDetailsDto invoiceDetails, SalesOrderWmsModel salesOrderWmsModel) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (invoiceDetails != null) {
            nexsSalesOrderReport.setInvoiceNo(invoiceDetails.getDocumentNo());

            DocumentItemDetailsDto documentItemDetails = invoiceDetails.getDocumentItems().stream()
                    .filter(i -> nexsSalesOrderReport.getUwItemId().equalsIgnoreCase(String.valueOf(i.getDocumentSourceReferenceItemId())))
                    .findFirst().orElse(null);


            if(documentItemDetails == null) {
                documentItemDetails = invoiceDetails.getDocumentItems().stream()
                        .filter(i -> Long.valueOf(salesOrderWmsModel.getSensieItemId()).equals(i.getDocumentSourceReferenceItemId()))
                        .findFirst().orElseThrow(() -> new RuntimeException("item detail not found in fds db against sensie id"));
            }

            nexsSalesOrderReport.setSalePriceWithoutTax(String.valueOf(documentItemDetails.getUnitPrice()));
            nexsSalesOrderReport.setSalePriceWithTax(String.valueOf(documentItemDetails.getUnitPriceWithTax()));
            if (invoiceDetails.getCreatedAt() != null) {
                nexsSalesOrderReport.setInvoiceCreationDate(simpleDateFormat.format(invoiceDetails.getCreatedAt()));
            }

            //filter over the order_item_id

            documentItemDetails.getDocumentItemTaxDetailsDto().stream().forEach(taxItem -> {
                switch (taxItem.getTaxType()) {
                    case CGST:
                        nexsSalesOrderReport.setCgst(String.valueOf(taxItem.getPercentage()));
                        nexsSalesOrderReport.setCgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    case SGST:
                        nexsSalesOrderReport.setSgst(String.valueOf(taxItem.getPercentage()));
                        nexsSalesOrderReport.setSgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    case IGST:
                        nexsSalesOrderReport.setIgst(String.valueOf(taxItem.getPercentage()));
                        nexsSalesOrderReport.setIgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    case UGST:
                        nexsSalesOrderReport.setUtgst(String.valueOf(taxItem.getPercentage()));
                        nexsSalesOrderReport.setUtgstRate(String.valueOf(taxItem.getValue()));
                        break;
                    default:
                        break;
                }
            });

            //nexsSalesOrderReport.setDiscount("0.0");
            //nexsSalesOrderReport.setItemTotalAfterDiscount(String.valueOf(documentItemDetails.getUnitPriceWithTax()));
            nexsSalesOrderReport.setInvoiceNo(invoiceDetails.getDocumentNo());
            nexsSalesOrderReport.setSalePriceWithoutTax(String.valueOf(documentItemDetails.getUnitPrice()));
            if(StringUtils.isEmpty(nexsSalesOrderReport.getManifestClosingDate())){
                nexsSalesOrderReport.setManifestClosingDate(simpleDateFormat.format(invoiceDetails.getCreatedAt()));
            }
            log.info("populated GST details for nexs sales order from fdsdb: {}", nexsSalesOrderReport);
        }
    }

    public String convertToLocalDate(Date gmtDate, String hubCountry) {
        log.info("Calculating local date for hub countyr : {} | gmtDate:{} ", hubCountry, gmtDate);
        ZoneId targetZoneId;
        if (hubCountry == null) {
            hubCountry = "IN";  //default to IN
        }
        switch (hubCountry) {
            case "IN":
                targetZoneId = ZoneId.of("Asia/Kolkata");
                break;
            case "SG":
                targetZoneId = ZoneId.of("Asia/Singapore");
                break;
            case "AE":
                targetZoneId = ZoneId.of("Asia/Dubai");
                break;
            default:
                targetZoneId = ZoneId.of("Asia/Kolkata");
        }
        ZonedDateTime gmtZonedDateTime = ZonedDateTime.ofInstant(gmtDate.toInstant(), ZoneId.of("GMT"));
        ZonedDateTime istZonedDateTime = gmtZonedDateTime.withZoneSameInstant(targetZoneId);
        Date istDate = Date.from(istZonedDateTime.toInstant());
        SIMPLE_DATE_FORMAT.setTimeZone(TimeZone.getTimeZone(targetZoneId));
        return SIMPLE_DATE_FORMAT.format(istDate);
    }

    public void nexsSalesReportDataSyncScheduler() {
        try {
            log.info("[nexsSalesReportDataSyncScheduler] Syncing missing order item");
            // Calculate the time to fetch order item
            Date now = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);
            cal.add(Calendar.MINUTE, -Integer.parseInt(minutesAgo));
            Date minutesAgoDate = cal.getTime();

            List<DispatchedOrderItemDTO> dispatchedOrderItems = getDispatchedOrderItems(minutesAgoDate);

            log.info("dispatchedOrderItems fetched - {}", dispatchedOrderItems.size());
            List<String> syncedUwtOrderIds =
                    nexsSalesOrderReportRepository.findRecentSaleOrderItemCodes(minutesAgoDate);
            log.info("syncedUwtOrderIds fetched from report- {}", syncedUwtOrderIds.size());

            Set<Integer> syncedUwtOrderIdsSet = syncedUwtOrderIds.stream()
                    .map(Integer::valueOf)
                    .collect(Collectors.toSet());

            List<String> unsyncedShippingPackageIds = dispatchedOrderItems.stream()
                    .filter(orderItem -> syncedUwtOrderIdsSet.isEmpty() || !syncedUwtOrderIdsSet.contains(orderItem.getOrderItemId()))
                    .map(DispatchedOrderItemDTO::getShippingPackageId)
                    .collect(Collectors.toList());

            syncForSalesOrderReport(unsyncedShippingPackageIds);

            log.info("[nexsSalesReportDataSyncScheduler] synced order items successfully");

        } catch (Exception e) {
            log.error("[nexsSalesReportDataSyncScheduler] Exception occured while syncing missing orders", e);
        }
    }


    private List<DispatchedOrderItemDTO> getDispatchedOrderItems(Date minutesAgoDate) {
        List<Tuple> results = orderItemHistoryRepository.findDispatchedOrderItems(minutesAgoDate);
        List<DispatchedOrderItemDTO> items = new ArrayList<>();
        for (Tuple tuple : results) {
            Integer orderItemId = tuple.get("orderItemId", Integer.class);
            String shippingPackageId = tuple.get("shippingPackageId", String.class);
            items.add(new DispatchedOrderItemDTO(orderItemId, shippingPackageId));
        }
        return items;
    }

    private void updateB2BStoreName(NexsSalesOrderReport nexsSalesOrderReport) {
        String navchannel = nexsSalesOrderReport.getNavChannel().toLowerCase();
        if ("webb2b".equalsIgnoreCase(navchannel)) {
            nexsSalesOrderReport.setStoreCustomerName(Constants.B2B_STORE_NAME_WEBB2B);
            return;
        }
        if (navchannel.contains("b2b") || navchannel.contains("bulk")) {
            log.info("Updating B2b store name for inc id : {}", nexsSalesOrderReport.getIncrementId());
            String channel = navchannel.contains("b2b") ? "B2B" : "FRANCHISEBULK";
            ResponseEntity<String> responseEntity =
                    psClientConnector.getPsClientOrderDetails(Integer.parseInt(nexsSalesOrderReport.getIncrementId())
                            , channel);
            log.info("[updateB2BStoreName] GetStoreAddress responseEntity:{}", responseEntity);
            FranchiseMaster franchiseAddress = null;
            try {
                if ("B2B".equalsIgnoreCase(channel)) {
                    FranchiseOrder franchiseOrder = mapper.readValue(responseEntity.getBody(),
                            FranchiseOrder.class);
                    if (franchiseOrder != null && franchiseOrder.getFranchiseMaster() != null) {
                        franchiseAddress = franchiseOrder.getFranchiseMaster();
                    }
                } else {
                    franchiseAddress = mapper.readValue(responseEntity.getBody(), FranchiseMaster.class);
                }
            } catch (Exception e) {
                log.error("Exception in converting franchise master ", e);
            }

            if (franchiseAddress != null) {
                nexsSalesOrderReport.setStoreCustomerName(franchiseAddress.getFranchiseName());
                nexsSalesOrderReport.setBillToPartyGSTIN(franchiseAddress.getGstin());
            }
            log.info("updated B2B store name for inc id : {}", nexsSalesOrderReport.getIncrementId());
        }
    }


    private void updateBillToPartyGSTIN(NexsSalesOrderReport nexsSalesOrderReport,
                                        SalesOrderWmsModel salesOrderWmsModel) {
        log.info("updating bill to party GSTIN for inc id - {}", nexsSalesOrderReport.getIncrementId());
        Matcher matcher = GSTIN_PATTERN.matcher(salesOrderWmsModel.getAddressLine1());
        if (matcher.find()) {
            nexsSalesOrderReport.setBillToPartyGSTIN(matcher.group());
            log.info("successfully updated bill to party GSTIN for - {}", nexsSalesOrderReport.getIncrementId());
        }
    }

    private void updateFulFIllmentChannel(NexsSalesOrderReport nexsSalesOrderReport) {
        log.info("[updateFulFIllmentChannel] orderItem: {}", nexsSalesOrderReport.getUwItemId());
        try {
            FMSResponse facilityResponse = fmsConnector.fetchFacilityDetails(nexsSalesOrderReport.getFacilityCode());
            log.info("[updateFulFIllmentChannel] FacilityDetails: {} for barcode: {}", facilityResponse,
                    nexsSalesOrderReport.getUwItemId());
            if (facilityResponse != null) {
                nexsSalesOrderReport.setFulfilmentChannel(facilityResponse.getLegalName());
            }
        } catch (Exception e) {
            log.error("Exception occured in fetching facility details from fms connector - ", e);
            nexsSalesOrderReport.setFulfilmentChannel(Constants.WEBSITE_NAME);
        }
    }

    private FranchiseMaster getDetailsFromFranchiseMaster(String incrementId, String navChannel) throws JsonProcessingException {
        ResponseEntity<String> responseEntity =
                psClientConnector.getPsClientOrderDetails(Integer.parseInt(incrementId), navChannel);

        FranchiseOrder franchiseOrder = mapper.readValue(responseEntity.getBody(), FranchiseOrder.class);
        return franchiseOrder.getFranchiseMaster();
    }

    private void populateSalesOrderReportFromFranchiseMaster(NexsSalesOrderReport nexsSalesOrderReport, FranchiseMaster franchiseMaster) {

        if (franchiseMaster != null) {
            nexsSalesOrderReport.setBillFromPartyGSTIN(franchiseMaster.getGstin());
            if(franchiseMaster.getCountryId().equalsIgnoreCase(nexsSalesOrderReport.getOrderCountry())) {
                nexsSalesOrderReport.setIsExport("No");
            } else {
                nexsSalesOrderReport.setIsExport("Yes");
            }
        }
    }

    public boolean syncDailyReportByOrderItems(List<Integer> orderItemIds) {
        log.info("syncDailyReportByOrderItems orderItems: {}", orderItemIds);
        if (orderItemIds.size() > nexsOderLimit) {
            log.error("order limit validation failied");
        }
        List<Map<String, Object>> lcrdWmsDataList =
                orderItemHistoryRepository.findAllLcrdsByOrderItems(orderItemIds);
        try {
            log.info("syncItems lcrdWmsDataList: {}", lcrdWmsDataList);
            if (CollectionUtils.isEmpty(lcrdWmsDataList)) {
                return false;
            }
            for (Map<String, Object> lcrdWmsDataMap : lcrdWmsDataList) {
                LCRDWmsModel lcrdWmsData = new LCRDWmsModel();
                Integer orderItemId = (Integer) lcrdWmsDataMap.get("order_item_id");
                LenskartCompleteReportDaily lenskartCompleteReportDaily = null;
                if(enableDuplicateEntryCheck) {
                    lenskartCompleteReportDaily =
                            getLenskartCompleteReportDailyIfExists(String.valueOf(orderItemId), "DISPATCHED");
                }
                if (Objects.isNull(lenskartCompleteReportDaily)) {
                    log.info("syncItems orderItemId {} push to kafka", orderItemId);
                    lcrdWmsData = mapper.convertValue(lcrdWmsDataMap, LCRDWmsModel.class);
                    String payload = mapper.writeValueAsString(lcrdWmsData);
                    pushToAnalyticsKafka(syncTopic, payload);
                } else {
                    log.info("syncItems orderItemId {} already exist", orderItemId);
                }
            }
        } catch (Exception ex) {
            log.error("syncItems error:{}", ex.getMessage(), ex);
            return false;
        }
        return true;
    }
}

